from typing import Optional

from fastapi import FastAPI

# app = FastAPI()
from handler import app
from handler_color import app
import uvicorn

# @app.get("/")
# def read_root():
#     return {"Hello": "World"}
#
#
# @app.get("/items/{item_id}")
# def read_item(item_id: int, q: Optional[str] = None):
#     return {"item_id": item_id, "q": q}
# import uvicorn

# async def app(scope, receive, send):



if __name__ == "__main__":
    uvicorn.run("main:app", host='0.0.0.0',port=8500, workers=4, debug=True)