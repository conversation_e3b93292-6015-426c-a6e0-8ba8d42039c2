
from src.shwethe_color.search import main4, main5, main6, schedule

# from src.pos_api import main as pos_api
# from src.pos_print_api import main as pos_print_api

from fastapi import FastAPI, Depends, Header, HTTPException, Response
from fastapi.middleware.cors import CORSMiddleware
from handler import app


# app = FastAPI(title = "FastAPI Video Store",
#               description = "Description and technical detail of APIs, Live on Medium | Author : <PERSON><PERSON><PERSON>",
#               version = "0.0.1")

origins = [
    "*",
    "https://localhost.tiangolo.com",
    "http://localhost",
    "http://************:8000",
]

app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# app.include_router(
#     main5.router,
#     prefix="/shwethe_petrol/api/v1/product",
#     tags=["product"],
#     responses={404: {"message": "Not found"}},
# )


app.include_router(
    main6.router,
    prefix="/shwethe_color/api/v1/test",
    tags=["color_test"],
    responses={404: {"message": "Not found"}},
)


