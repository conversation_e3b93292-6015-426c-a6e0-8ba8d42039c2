
from src.shwethe_petrol.search import main4, main5, main6, main, schedule

# from src.pos_api import main as pos_api
# from src.pos_print_api import main as pos_print_api

from fastapi import FastAP<PERSON>, Depends, Header, HTTPException, Response
from fastapi.middleware.cors import CORSMiddleware


app = FastAPI(title = "FastAPI Video Store",
              description = "Description and technical detail of APIs, Live on Medium | Author : <PERSON><PERSON><PERSON>",
              version = "0.0.1")

origins = [
    "*",
    "https://localhost.tiangolo.com",
    "http://localhost",
    "http://************:8000",
]

app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# app.include_router(
#     search.router,
#     prefix="/petrol_api/api/v1/search",
#     tags=["search"],
#     responses={404: {"message": "Not found"}},
# )

# app.include_router(
#     main.router,
#     prefix="/petrol",
#     tags=["petrol"],
#     responses={404: {"message": "Not found"}},
# )

# app.include_router(
#     schedule2.router,
#     prefix="/schedule2",
#     tags=["schedule2"],
#     responses={404: {"message": "Not found"}},
# )


app.include_router(
    schedule.router,
    prefix="/schedule",
    tags=["schedule"],
    responses={404: {"message": "Not found"}},
)


app.include_router(
    main4.router,
    prefix="/send_data_outside",
    tags=["petrol_send_data_outside"],
    responses={404: {"message": "Not found"}},
)


app.include_router(
    main5.router,
    prefix="/shwethe_petrol/api/v1/product",
    tags=["petrol_product"],
    responses={404: {"message": "Not found"}},
)


app.include_router(
    main6.router,
    prefix="/shwethe_petrol/api/v1/test",
    tags=["petrol_test"],
    responses={404: {"message": "Not found"}},
)


