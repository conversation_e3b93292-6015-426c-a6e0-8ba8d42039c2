import numpy as np
from fastapi import FastAPI, APIRouter, Body, Response, BackgroundTasks, Header, HTTPException
from pydantic import BaseModel
from sqlalchemy.orm.attributes import flag_modified
from helper import generate_id, generate_datetime, generate_datetime_id, generate_datetime_selie, get_bookszxzx
from typing import List, Optional
import json
import requests
import pandas as pd



from sqlalchemy import create_engine, tuple_
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import NullPool
from fastapi.responses import JSONResponse
from datetime import date, datetime
import datetime
from src.Connect.postgresql_connect import DATABASE_URL, DATABASE_URL_test_nern
from src.Connect.https_connect import Arter_api, shwethe_mysql_api
from src.shwethe_color.models.model6 import color_devices_list, color_devices, color_devices_sub_components, color_components, color_sub_components
from src.shwethe_color.schema.schema6 import addColorId, ColorMix
from functools import reduce
from fastapi.responses import J<PERSON>NResponse
from fastapi.encoders import jsonable_encoder
from sqlalchemy import and_, or_, not_
from sqlalchemy import extract, cast
from sqlalchemy import Column, Integer, String, Date, DateTime, ForeignKey, TEXT, FLOAT
from sqlalchemy.ext.declarative import declarative_base

router = APIRouter()


# engine = create_engine(DATABASE_URL_test_nern, poolclass=NullPool)
engine = create_engine(DATABASE_URL_test_nern)
Session = sessionmaker(bind=engine)





@router.get("/getBrand")
async def getBrand(fen_dian_id: int):

    session = Session()
    try:
        df10 = pd.read_sql(sql = session.query(color_devices_list)
                           .filter(color_devices_list.fen_dian_id == fen_dian_id)
                           .statement, con = session.bind)
        getColor_devices_id = df10['color_devices_id'].tolist()
        
        df20 = pd.read_sql(sql = session.query(color_devices)
                        .filter(color_devices.auto_id.in_(getColor_devices_id))
                        .statement, con = session.bind)
        df20 = df20.to_dict("records")
    except:
        df20 = []    
    session.close()
    return df20


@router.post("/addColorId")
async def add_color(post: addColorId):
    session = Session()
    db_user = color_devices_sub_components(
        color_devices_id=post.color_devices_id,
        color_id=post.color_id,
        price=post.price,
    )
    session.add(db_user)
    session.commit()
    session.refresh(db_user)
    session.close()
    return db_user


@router.post("/ColorMix")
async def ColorMix(post: ColorMix):
    session = Session()
    a, b, c = await get_bookszxzx()
    db_user = color_components(
        color_devices_id=post.color_devices_id,
        product_id_a=post.product_id_a,
        product_id_b=post.product_id_b,
        fen_dian_id=post.fen_dian_id,
        datetime=c,
    )
    session.add(db_user)
    session.commit()
    session.refresh(db_user)
    session.close()
    
    get_id = {"auto_id": db_user.auto_id}
    return get_id


# @router.get("/getBrand_by_id")
# async def getBrand_by_id(auto_id_: int):
#     auto_id = auto_id_
#     sql = """SELECT  * from color_devices where auto_id = %s"""
#     df = pd.read_sql_query(sql, engine, params=[auto_id])
#     df = df.to_dict('records')
#     return df


@router.get("/getColorComponent")
async def getColorComponent(color_devices_id: int):
    session = Session()

    df = pd.read_sql(sql = session.query(color_devices_sub_components)  
        .filter(color_devices_sub_components.color_devices_id == color_devices_id)
        .order_by(color_devices_sub_components.color_id, color_devices_sub_components.color_id.desc())
        .statement, con = session.bind)
    df['unit_use'] = ""
    df['priceCal'] = ""
    
    df30 = df
    df30 = df30.rename(columns={"color_id": 'product_id'})
    
    url = Arter_api + 'product_list_id'
    to_dict = df30.to_dict('records')
    body_raw = {"data_api": to_dict}
    df50 = requests.get(url=url, json=body_raw).json()
    df50 = pd.DataFrame(df50)
    
    
    merge = df.merge(df50, left_on='color_id', right_on='product_id', how='left')
    merge = merge.to_dict('records')
    return merge


@router.post("/colorComponentsListInsert")
async def colorComponentsListInsert(formData: List[dict]):
    session = Session()

    vvv = []
    try:
        for i in formData:
            session.add_all([
                color_sub_components(
                    color_components_id=i['color_components_id'],
                    color_id=i['color_id'],
                    unit_use=i['unit_use']
                    )
                ]
            )        
            vvv.append(i)    
    except Exception:
        raise HTTPException(status_code=500, detail="New Error Found")
    
    session.commit()
    session.close()
                
    return vvv


@router.get("/getListInday")
async def getListInday(fen_dian_id: int, getDate: str):

    session = Session()
    try:
        df10 = pd.read_sql(sql = session.query(color_components)
                           .filter(cast(color_components.datetime, Date) == getDate)
                           .filter(color_components.fen_dian_id == fen_dian_id)
                           .order_by(color_components.datetime.desc())
                           .statement, con = session.bind)
        df20 = df10.to_dict("records")
    except:
        df20 = []    
    session.close()
    return df20


@router.get("/indayDetails")
async def indayDetails(color_components_id: int):

    session = Session()
    try:
        df10 = pd.read_sql(sql = session.query(color_sub_components)  
            .filter(color_sub_components.color_components_id == color_components_id)
            .order_by(color_sub_components.color_id, color_sub_components.color_id.desc())
            .statement, con = session.bind)
        
        df20 = df10
        df20 = df20.rename(columns={"color_id": 'product_id'})
        
        url = Arter_api + 'product_list_id'
        to_dict = df20.to_dict('records')
        body_raw = {"data_api": to_dict}
        df30 = requests.get(url=url, json=body_raw).json()
        df30 = pd.DataFrame(df30)
        
        merge = df10.merge(df30, left_on='color_id', right_on='product_id', how='left')
        merge = merge.to_dict('records')
    except:
        merge = []    
    session.close()
    return merge


@router.get("/getProductIdname")
async def getProductIdname(GVID: str):

    session = Session()
    try:
        payload = {'ID': GVID}
        url = Arter_api + 'product_idname'
        df10 = requests.get(url=url, params=payload).json()
    except:
        df10 = False    
    session.close()
    return df10