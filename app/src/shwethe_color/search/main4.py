from asyncio.log import logger
from http.client import HTTPException
import numpy as np
from fastapi import FastAPI, APIRouter, Body, Response, BackgroundTasks, Header, Depends
from pydantic import BaseModel
from sqlalchemy.orm.attributes import flag_modified
from helper import generate_id, generate_datetime, generate_datetime_id, generate_datetime_selie, get_bookszxzx
from typing import List, Optional
import json
import requests
import pandas as pd
import time


from sqlalchemy import create_engine, desc, tuple_
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import NullPool
from fastapi.responses import JSONResponse
from datetime import date, datetime
import datetime
from src.Connect.postgresql_connect import DATABASE_URL_test_nern, DATABASE_URL
from src.Connect.https_connect import Arter_api, shwethe_mysql_api
from src.shwethe_color.models.model5 import PetrolModel, TypeCar, TypeLei, PetrolModel2
from src.shwethe_color.schema.schema import Put, formSubmit, addStore, addCar, UserModel, add_all
from functools import reduce
from fastapi.responses import JSONResponse
from fastapi.encoders import jsonable_encoder
from sqlalchemy import Column, Integer, String, Date, DateTime, ForeignKey, TEXT, FLOAT



router = APIRouter()


engine = create_engine(DATABASE_URL, poolclass=NullPool)
Session = sessionmaker(bind=engine)






@router.post("/oil_insertTopetrol2")
async def oil_insertTopetrol2():
    session = Session()

    DATABASE_URL_2 = "*************************************************/insert_data"
    engine = create_engine(DATABASE_URL_2, poolclass=NullPool)
    df = pd.read_sql_table('oil_insert', engine)
    # df= df.head(2)
    df = df.to_dict('records')

    vvv = []
    for i in df:
        data = {
                "auto_id": i['id'], 
                "petrol_id": i['bill_id'],
                "datetime": i['datetime'],  
                "data_details": [
                    {
                        "qty": i['qty'],
                        "r_id": i['data_detail']['iid'],
                        "u_id": i['uid'],
                        "lei_a": i['lei_a'],
                        "lei_b": i['lei_b'],
                        "price": i['price'],
                        "bi_zhi": 0,
                        "sub_id": 0,
                        "distant": float(i['data_detail']['distant']),
                        "ke_bian": i['ke_bian'],
                        "product_id": i['product_id'],
                        "che_liang_id": i['che_liang'],
                        "jia_yi_fang_a": i['type_a'],
                        "jia_yi_fang_b": i['type_b'],
                        "bu_bian": i['line'],
                        "qtyp": i['qtyp'],
                        "qtyn": i['qtyn'],
                    }
                ]        
            }
            
        
        vvv.append(data)
        
    for i in vvv:
        # print(i)
        session.add_all([
            PetrolModel2(
                auto_id=i['auto_id'],
                petrol_id=i['petrol_id'],
                datetime=i['datetime'],
                data_details=i['data_details'],
                )
            ]
        )
            
    session.commit()
    session.close()
    return "vvv"



@router.post("/insert_arterDatabase")
async def insert_arterDatabase():
    session = Session()
    
    import datetime as DT
    today = DT.date.today()
    week_ago = today - DT.timedelta(days=15)
    print(week_ago)
    
    # today = datetime.date.today()
    # future_day = today.day -1
    # future_month = (today.month) % 12
    # future_year = today.year + ((today.month) // 12)
    # six_months_ago = datetime.date(future_year, future_month, future_day)
    # print(six_months_ago)
    # print(today, future_day, future_month, future_year)
    
    df20 = pd.read_sql(sql = session.query(PetrolModel) 
                           .filter(PetrolModel.datetime > week_ago)
                           .filter(PetrolModel.status_details['status'].astext.cast(String) == "success")
                           .statement, con = session.bind)
    df20 = df20.to_dict('records')
    json_nor = pd.json_normalize(df20, "data_details", ["auto_id", "petrol_id", "datetime"])
    json_nor = json_nor.to_dict('records')
    
    # INSERT TO ARTER DATABASE
    aaa = []
    for ioo in json_nor:
        d = {
            'a_id': ioo['auto_id'],
            'b_id': ioo['sub_id'],
            'datetime': str(ioo['datetime']),
            'jin_huo_dang': ioo['petrol_id'],
            'uid': ioo['user_id'],
            'product_id': ioo['product_id'],
            'product_qty': ioo['qty'],
            'product_price_x': ioo['price'],
            'product_price_y': ioo['price'],
            'jia_yi_fang_a': ioo['jia_yi_fang_a'],
            'jia_yi_fang_b': ioo['jia_yi_fang_b'],
            'lei_a': ioo['lei_a'],
            'lei_b': ioo['lei_b'],
            'line': 1005,
            'ke_bian': ioo['ke_bian'],
            'che_liang': ioo['che_liang_id'],
            'kind': 10003,
            'bi_zhi': ioo['bi_zhi']
            }
        aaa.append(d)
    df1 = pd.DataFrame(aaa) 
    mata = {
    'data' : df1.to_dict(orient='records')
    }
        
    dd = requests.post(shwethe_mysql_api + '/api/v2/table/mysql', data= json.dumps(mata))

    return dd.status_code





# @router.get("/books")
# async def get_books(page_size: int = 15, page: int = 1):
#     # if(page_size > 100 or page_size < 0):
#     #     page_size = 100

#     page -= 1
#     session = Session()
#     books = session.query(PetrolModel).limit(page_size).offset(page*page_size).all()
#     session.close()

#     result = jsonable_encoder({
#         "books": books
#     })

#     return JSONResponse(status_code=200, content={
#         "status_code": 200,
#         "result": result
#     })