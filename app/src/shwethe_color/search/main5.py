import numpy as np
from fastapi import FastAPI, APIRouter, Body, Response, BackgroundTasks, Header
from pydantic import BaseModel
from sqlalchemy.orm.attributes import flag_modified
from helper import generate_id, generate_datetime, generate_datetime_id, generate_datetime_selie, get_bookszxzx
from typing import List, Optional
import json
import requests
import pandas as pd



from sqlalchemy import create_engine, tuple_
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import NullPool
from fastapi.responses import JSONResponse
from datetime import date, datetime
import datetime
from src.Connect.postgresql_connect import DATABASE_URL, DATABASE_URL_test_nern
from src.Connect.https_connect import Arter_api, shwethe_mysql_api
# from src.shwethe_color.models.model6 import PetrolModel, TypeCar, TypeLei, PetrolModel2
# from src.shwethe_color.schema.schema6 import Put, Put_distant, formSubmit, addStore, addCar, confirm_status, PutCarMotor, Put<PERSON>asakBill
from functools import reduce
from fastapi.responses import J<PERSON><PERSON>esponse
from fastapi.encoders import jsonable_encoder
from sqlalchemy import and_, or_, not_
from sqlalchemy import extract, cast
from sqlalchemy import Column, Integer, String, Date, DateTime, ForeignKey, TEXT, FLOAT
from sqlalchemy.ext.declarative import declarative_base

router = APIRouter()


# engine = create_engine(DATABASE_URL_test_nern, poolclass=NullPool)
engine = create_engine(DATABASE_URL)
Session = sessionmaker(bind=engine)


