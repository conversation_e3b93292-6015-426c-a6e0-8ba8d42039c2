from datetime import datetime, date, time, timezone
from typing import List, Optional, Set

from sqlalchemy.sql.sqltypes import Date, Integer
from fastapi.param_functions import Body
from pydantic import BaseModel, validator, Field


  
class addColorId(BaseModel):

    color_devices_id: int
    color_id: int
    price: float

    class Config:
        orm_mode = True


class ColorMix(BaseModel):

    color_devices_id: int
    product_id_a: int
    product_id_b: int
    fen_dian_id: int

    class Config:
        orm_mode = True