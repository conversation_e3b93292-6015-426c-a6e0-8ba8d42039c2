from datetime import datetime, date, time, timezone
from typing import List, Optional, Set

from sqlalchemy.sql.sqltypes import Date, Integer
from fastapi.param_functions import Body
from pydantic import BaseModel, validator, Field



class confirm_status(BaseModel):
    status: str = "success"

    class Config:
        orm_mode = True
        
        
class UserModel(BaseModel):
    auto_id: int
    petrol_id: str
    datatime: date

    class Config:
        orm_mode = True
        

class model200(BaseModel):
    message: str = ""
    

class addStore(BaseModel):
    jia_yi_id: int

    class Config:
        orm_mode = True
        
class addCar(BaseModel):
    jia_yi_id: int

    class Config:
        orm_mode = True
        
class formSubmit(BaseModel):
    # data_details: List[dict] = []
    # data_details: dict = {}

    fen_dian_id: int
    user_id: int
    warehouse_id: int
    u_id: int
    lei_a: int
    lei_b: int
    ke_bian: int
    che_liang_id: int
    store: int
    car: int
    petrol: int
    qty: float
    currency: int
    price: float
    distant: float
    bill: str
    

    # class Payload(BaseModel):
    #     sub_id: str
    #     r_id: str
    #     ke_bian: str
    #     bi_zhi: str
    # # info: Optional[Payload]
    # data_details: Optional[List[Payload]]
    
    class Config:
        orm_mode = True


class Put(BaseModel):
    # ke_bian: str
    # bi_zhi: str
    
    che_liang_id: int
    # lei_a: int
    # lei_b: int
    # ke_bian: int
    # store: int
    car: int
    petrol: int
    qty: float
    price: float
    # currency: int
    # distant: float

    class Config:
        orm_mode = True
        
        
class PutCarMotor(BaseModel):
    # ke_bian: str
    # bi_zhi: str
    
    che_liang_id: int
    lei_a: int
    lei_b: int
    ke_bian: int
    store: int
    car: int
    petrol: int
    qty: float
    price: float
    currency: int
    distant: float

    class Config:
        orm_mode = True     
        
        
class PutMasakBill(BaseModel):
    # ke_bian: str
    # bi_zhi: str
    
    fen_dian_id: int
    user_id: int
    warehouse_id: int
    # u_id: int
    che_liang_id: int
    lei_a: int
    lei_b: int
    # ke_bian: int
    store: int
    car: int
    petrol: int
    qty: float
    # price: float
    # currency: int
    # distant: float

    class Config:
        orm_mode = True              
        
        
class Put_distant(BaseModel):
    
    distant: float

    class Config:
        orm_mode = True       
        

class add_all(BaseModel):
    
    aaa: int
    
    class Config:
        orm_mode = True