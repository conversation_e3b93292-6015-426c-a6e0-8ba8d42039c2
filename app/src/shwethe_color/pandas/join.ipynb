{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import sqlalchemy\n", "from sqlalchemy import create_engine, tuple_\n", "from sqlalchemy.pool import NullPool\n", "import requests\n", "import numpy as np\n", "import json\n", "\n", "\n", "\n", "\n", "DATABASE_URL = \"*************************************************/insert_data\"\n", "engine = create_engine(DATABASE_URL, poolclass=NullPool)\n", "df = pd.read_sql_table('oil_insert', engine)\n", "df= df.head(2)\n", "df = df.to_dict('records')\n", "\n", "vvv = []\n", "for i in df:\n", "    data=[\n", "        {\n", "            \"auto_id\": i['id'], \n", "            \"petrol_id\": i['bill_id'],\n", "            \"datetime\": i['datetime'],  \n", "            \"data_details\": [\n", "                {\n", "                    \"qty\": i['qty'],\n", "                    \"r_id\": i['data_detail']['iid'],\n", "                    \"u_id\": i['uid'],\n", "                    \"lei_a\": i['lei_a'],\n", "                    \"lei_b\": i['lei_b'],\n", "                    \"price\": i['price'],\n", "                    \"bi_zhi\": 0,\n", "                    \"sub_id\": 0,\n", "                    \"distant\": int(i['data_detail']['distant']),\n", "                    \"ke_bian\": i['ke_bian'],\n", "                    \"product_id\": i['product_id'],\n", "                    \"che_liang_id\": i['che_liang'],\n", "                    \"jia_yi_fang_a\": i['type_a'],\n", "                    \"jia_yi_fang_b\": i['type_b'],\n", "                    \"bu_bian\": i['line'],\n", "                    \"qtyp\": i['qtyp'],\n", "                    \"qtyn\": i['qtyn'],\n", "                }\n", "            ]        \n", "        }\n", "    ]\n", "    vvv.extend(data)\n", "display(vvv, df)\n", "\n", "\n", "# base = \"*************************************************/test_nern\"\n", "# engine = create_engine(base, poolclass=NullPool)\n", "# df2 = pd.read_sql_table('petrol', engine)\n", "# df2= df2.head(3)\n", "# display(df2)\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"interpreter": {"hash": "916dbcbb3f70747c44a77c7bcd40155683ae19c65e1c03b4aa3499c5328201f1"}, "kernelspec": {"display_name": "Python 3.8.10 64-bit", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}