{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import sqlalchemy\n", "from sqlalchemy import create_engine, tuple_\n", "from sqlalchemy.pool import NullPool\n", "import numpy as np\n", "import json\n", "from sqlalchemy.ext.declarative import declarative_base\n", "from sqlalchemy import Column, Integer, String, Date, DateTime, ForeignKey, TEXT\n", "from sqlalchemy.sql.expression import table, text\n", "from sqlalchemy.dialects.postgresql.json import JSONB\n", "from sqlalchemy.orm import sessionmaker\n", "from sqlalchemy import and_, or_, not_\n", "from sqlalchemy import literal\n", "import datetime\n", "import itertools\n", "\n", "import sqlalchemy as sa\n", "\n", "# import pyodbc \n", "# server = '192.168.1.202' # to specify an alternate port\n", "# database = 'aa名稱' \n", "# username = 'sa' \n", "# password = '*********' \n", "# cnxn = pyodbc.connect('DRIVER={ODBC Driver 17 for SQL Server};SERVER='+server+';DATABASE='+database+';UID='+username+';PWD='+ password)\n", "# cursor = cnxn.cursor()\n", "# cursor\n", "\n", "# DATABASE_URL = \"*************************************************/test_nern\"\n", "# engine = create_engine(DATABASE_URL, poolclass=NullPool)\n", "# engine = sa.create_engine('mssql+pyodbc://sa:*********@192.168.1.202/aa名稱?driver=SQL+Server+Native+Client+11.0', echo=True)\n", "# engine = create_engine(\"mssql+pyodbc://sa:*********@192.168.1.202/aa名稱?driver=ODBC+Driver+17+for+SQL+Server\")\n", "# engine = create_engine(\"mssql+pyodbc://sa:*********@192.168.1.202/aa名稱?trusted_connection=yes&driver=ODBC+Driver+13+for+SQL+Server\")\n", "engine = create_engine('mssql+pymssql://sa:*********@192.168.1.202/aa名稱/?charset=utf8')\n", "Session = sessionmaker(bind=engine)\n", "\n", "Base = declarative_base()\n", "\n", "df = pd.read_sql_table('Table2', engine)\n", "df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pymssql\n", "\n", "conn = pymssql.connect(\"192.168.1.202\", \"sa\", \"*********\", \"aa名稱\")\n", "cursor = conn.cursor()\n", "cursor.execute('SELECT * FROM Table2')\n", "print( \"all persons\" )\n", "print( cursor.fetchall() )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import sqlalchemy\n", "from sqlalchemy import create_engine\n", "\n", "engine = create_engine(\"mssql+pymssql://sa:*********@192.168.1.202/aa名稱\")\n", "df = pd.read_sql_table('Table2', engine)\n", "df\n", "# df = pd.read_sql_query('SELECT * FROM Table2', engine)\n", "# df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pyodbc, sqlalchemy\n", "import pymssql\n", "import pandas as pd\n", "import sqlalchemy\n", "from sqlalchemy import create_engine\n", "\n", "def connect():\n", "    # pyodbc.connect('DRIVER={FreeTDS};Server=192.168.1.202;Database=aa名稱;UID=sa;PWD=*********;TDS_Version=8.0;Port=1433;')\n", "    # pymssql.connect(\"192.168.1.202\", \"sa\", \"*********\", \"aa名稱\")\n", "    pyodbc.connect('DRIVER={FreeTDS};SERVER=192.168.1.202;PORT=1433;DATABASE=aa名稱;UID=sa;PWD=*********;TDS_Version=8.0;')\n", "\n", "engine = sqlalchemy.create_engine('mssql://', creator=connect)\n", "\n", "df = pd.read_sql_table('Table2', engine)\n", "df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pyodbc\n", "\n", "\n", "conn = pyodbc.connect('DRIVER=FreeTDS;SERVER=192.168.1.202;PORT=1433;DATABASE=aa名稱;UID=sa;PWD=*********;TDS_Version=9;')\n", "cursor = conn.cursor()\n", "for row in cursor.execute('SELECT * FROM Table2'):\n", "    print (row)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import urllib\n", "import pandas as pd\n", "from sqlalchemy import create_engine\n", "\n", "\n", "engine = create_engine('mssql+pyodbc:///?odbc_connect=' +\n", "    urllib.parse.quote_plus('DRIVER=FreeTDS;SERVER=192.168.1.202;PORT=1433;DATABASE=aa名稱;UID=sa;PWD=*********;TDS_Version=8.0;')\n", ")\n", "# for row in engine.execute('SELECT * FROM Table2'):\n", "#     print (row)\n", "df = pd.read_sql_query('SELECT * FROM Table2', engine)\n", "df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import sqlalchemy\n", "\n", "engine = sqlalchemy.create_engine(\"DRIVER={FreeTDS};Server=192.168.1.202;Database=aa名稱;UID=sa;PWD=*********;TDS_Version=8.0;Port=1433;\").connect()\n", "\n", "for row in engine.execute('SELECT * FROM Table2'):\n", "    print (row)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"interpreter": {"hash": "916dbcbb3f70747c44a77c7bcd40155683ae19c65e1c03b4aa3499c5328201f1"}, "kernelspec": {"display_name": "Python 3.8.10 64-bit", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}