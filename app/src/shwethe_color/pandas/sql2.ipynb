{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "from sqlalchemy import create_engine\n", "\n", "engine = create_engine(\"mssql+pymssql://sa:123041316@192.168.1.202/aa名稱\")\n", "df = pd.read_sql_query('SELECT * FROM Table2', engine)\n", "df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import sqlalchemy\n", "from sqlalchemy import create_engine, tuple_\n", "from sqlalchemy.pool import NullPool\n", "import numpy as np\n", "import json\n", "from sqlalchemy.ext.declarative import declarative_base\n", "from sqlalchemy import Column, Integer, String, Date, DateTime, ForeignKey, TEXT\n", "from sqlalchemy.sql.expression import table, text\n", "from sqlalchemy.dialects.postgresql.json import JSONB\n", "from sqlalchemy.orm import sessionmaker\n", "from sqlalchemy import literal\n", "import matplotlib.pyplot as plt\n", "\n", "\n", "\n", "DATABASE_URL = \"mssql+pymssql://sa:123041316@192.168.1.202/aa名稱\"\n", "engine = create_engine(DATABASE_URL, poolclass=NullPool)\n", "Session = sessionmaker(bind=engine)\n", "\n", "Base = declarative_base()\n", "\n", "class BBPetrol(Base):\n", "    __tablename__ = 'Table2'\n", "    auto_id = Column(Integer, primary_key=True)\n", "    text = Column(TEXT)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import sqlalchemy\n", "from sqlalchemy import create_engine, tuple_\n", "from sqlalchemy.pool import NullPool\n", "import numpy as np\n", "import json\n", "from sqlalchemy.ext.declarative import declarative_base\n", "from sqlalchemy import Column, Integer, String, Date, DateTime, ForeignKey, TEXT, FLOAT\n", "from sqlalchemy.sql.expression import table, text\n", "from sqlalchemy.dialects.postgresql.json import JSONB\n", "from sqlalchemy.orm import sessionmaker\n", "from sqlalchemy import literal\n", "import matplotlib.pyplot as plt\n", "\n", "\n", "\n", "DATABASE_URL = \"*************************************************/n_data\"\n", "engine = create_engine(DATABASE_URL, poolclass=NullPool)\n", "df = pd.read_sql_table('petrol', engine)\n", "df = df.sort_values(['datetime'], ascending=[True])\n", "# df= df.head(2)\n", "df = df.to_dict('records')\n", "\n", "vvv = []\n", "for i in df:\n", "    # print(i)\n", "    data=[\n", "        {\n", "            \"petrol_id\": i['petrol_id'],\n", "            \"datetime\": i['datetime'],\n", "            \"sub_id\": i['data_details'][0]['sub_id'],\n", "            \"fen_dian_id\": i['data_details'][0]['fen_dian_id'],\n", "            \"warehouse_id\": i['data_details'][0]['warehouse_id'],\n", "            \"user_id\": i['data_details'][0]['user_id'],\n", "            \"jia_yi_fang_a\": i['data_details'][0]['jia_yi_fang_a'],\n", "            \"jia_yi_fang_b\": i['data_details'][0]['jia_yi_fang_b'],\n", "            \"che_liang_id\": i['data_details'][0]['che_liang_id'],\n", "            \"lei_a\": i['data_details'][0]['lei_a'],\n", "            \"lei_b\": i['data_details'][0]['lei_b'],\n", "            \"ke_bian\": i['data_details'][0]['ke_bian'],\n", "            \"product_id\": i['data_details'][0]['product_id'],\n", "            \"bi_zhi\": i['data_details'][0]['bi_zhi'],\n", "            \"price\": i['data_details'][0]['price'],\n", "            \"qty\": i['data_details'][0]['qty'],\n", "            \"distant\": i['data_details'][0]['distant'],\n", "            \"bill\": i['data_details'][0]['bill']\n", "        }\n", "    ]\n", "    vvv.extend(data)\n", "display(vvv)\n", "\n", "\n", "\n", "\n", "\n", "DATABASE_URL2 = \"mssql+pymssql://sa:123041316@192.168.1.202/aa名稱\"\n", "engine2 = create_engine(DATABASE_URL2, poolclass=NullPool)\n", "Session2 = sessionmaker(bind=engine2)\n", "\n", "Base = declarative_base()\n", "\n", "class BBPetrol(Base):\n", "    __tablename__ = 'Table1'\n", "    auto_id = Column(Integer, primary_key=True)\n", "    petrol_id = Column(TEXT)\n", "    datetime = Column(DateTime)\n", "    sub_id = Column(Integer)\n", "    fen_dian_id = Column(Integer)\n", "    warehouse_id = Column(Integer)\n", "    user_id = Column(Integer)\n", "    jia_yi_fang_a = Column(Integer)\n", "    jia_yi_fang_b = Column(Integer)\n", "    che_liang_id = Column(Integer)\n", "    lei_a = Column(Integer)\n", "    lei_b = Column(Integer)\n", "    ke_bian = Column(Integer)\n", "    product_id = Column(Integer)\n", "    bi_zhi = Column(Integer)\n", "    price = Column(FLOAT)\n", "    qty = Column(FLOAT)\n", "    distant = Column(FLOAT)\n", "    bill = Column(TEXT)\n", "    \n", "\n", "\n", "session = Session2()\n", "\n", "for i in vvv:\n", "        print(i)\n", "        session.add_all([\n", "            BBPetrol(\n", "                petrol_id=i['petrol_id'],\n", "                datetime=i['datetime'],\n", "                sub_id=i['sub_id'],\n", "                fen_dian_id=i['fen_dian_id'],\n", "                warehouse_id=i['warehouse_id'],\n", "                user_id=i['user_id'],\n", "                jia_yi_fang_a=i['jia_yi_fang_a'],\n", "                jia_yi_fang_b=i['jia_yi_fang_b'],\n", "                che_liang_id=i['che_liang_id'],\n", "                lei_a=i['lei_a'],\n", "                lei_b=i['lei_b'],\n", "                ke_bian=i['ke_bian'],\n", "                product_id=i['product_id'],\n", "                bi_zhi=i['bi_zhi'],\n", "                price=i['price'],\n", "                qty=i['qty'],\n", "                distant=i['distant'],\n", "                bill=i['bill']\n", "                )\n", "            ]\n", "        )\n", "            \n", "session.commit()\n", "session.close()\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"interpreter": {"hash": "916dbcbb3f70747c44a77c7bcd40155683ae19c65e1c03b4aa3499c5328201f1"}, "kernelspec": {"display_name": "Python 3.8.10 64-bit", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}