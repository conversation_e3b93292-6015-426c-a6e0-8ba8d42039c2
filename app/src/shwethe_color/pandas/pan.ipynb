{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import sqlalchemy\n", "from sqlalchemy import create_engine, tuple_\n", "import requests\n", "import numpy as np\n", "import json\n", "\n", "\n", "DATABASE_URL = \"*************************************************/n_data\"\n", "engine = create_engine(DATABASE_URL , poolclass=NullPool)\n", "\n", "\n", "\n", "df = pd.read_sql_table('petrol', engine)\n", "# df = df.head(3)\n", "df = df.to_dict('records')\n", "df = pd.json_normalize(df, \"data_details\", [\"petrol_id\", \"datetime\", \"auto_id\"])\n", "df[\"product_price_b\"] = 0\n", "df['bu_bian'] = 0\n", "df['jin_huo_bian'] = 0\n", "df = df.rename(columns=dict(auto_id=\"a_id\", sub_id=\"b_id\", qty=\"product_qty\", price=\"product_price_b\", petrol_id=\"jin_huo_dang\"))\n", "display(df)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import sqlalchemy\n", "from sqlalchemy import create_engine, tuple_\n", "from sqlalchemy.pool import NullPool\n", "import requests\n", "import numpy as np\n", "import json\n", "\n", "\n", "DATABASE_URL = \"*************************************************/n_data\"\n", "engine = create_engine(DATABASE_URL, poolclass=NullPool)\n", "\n", "# df = df.loc[df['che_liang_id'] == 36555]\n", "# df = df.groupby('che_liang_id').filter(lambda g: len(g) > 1)\n", "# df = df.drop_duplicates(subset=['qty'], keep=\"first\")\n", "\n", "df = pd.read_sql_table('petrol', engine)\n", "# df = df.head(3)\n", "df = df.to_dict('records')\n", "df = pd.json_normalize(df, \"data_details\", [\"petrol_id\", \"datetime\", \"auto_id\"])\n", "df = df.groupby('che_liang_id').filter(lambda g: len(g) > 1)\n", "display(df)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import sqlalchemy\n", "from sqlalchemy import create_engine, tuple_\n", "import requests\n", "import numpy as np\n", "import json\n", "\n", "\n", "DATABASE_URL = \"*************************************************/n_data\"\n", "engine = create_engine(DATABASE_URL, poolclass=NullPool)\n", "\n", "\n", "df = pd.read_sql_table('petrol', engine)\n", "df = df.to_dict('records')\n", "df = pd.json_normalize(df, \"data_details\", [\"petrol_id\", \"datetime\", \"auto_id\"])\n", "df = df.loc[df['che_liang_id'] == 104]\n", "# df = df.iloc[-4:]\n", "# df = df.iloc[-0:-2]\n", "display(df)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import sqlalchemy\n", "from sqlalchemy import create_engine, tuple_\n", "from sqlalchemy.pool import NullPool\n", "import numpy as np\n", "import json\n", "from sqlalchemy.ext.declarative import declarative_base\n", "from sqlalchemy import Column, Integer, String, Date, DateTime, ForeignKey, TEXT\n", "from sqlalchemy.sql.expression import table, text\n", "from sqlalchemy.dialects.postgresql.json import JSONB\n", "from sqlalchemy.orm import sessionmaker\n", "from sqlalchemy import literal\n", "import matplotlib.pyplot as plt\n", "\n", "\n", "DATABASE_URL = \"*************************************************/test_nern\"\n", "engine = create_engine(DATABASE_URL, poolclass=NullPool)\n", "Session = sessionmaker(bind=engine)\n", "\n", "Base = declarative_base()\n", "\n", "class PetrolModel(Base):\n", "    __tablename__ = 'petrol'\n", "    auto_id = Column(Integer, primary_key=True)\n", "    petrol_id = Column(TEXT(convert_unicode=True), server_default=text(\"'petrol'||lpad(NEXTVAL('petrol_sequence'::regclass)::text,8, '0')\"))\n", "    datetime = Column(DateTime)\n", "    data_details = Column(JSONB, nullable=False, server_default='[]')\n", "\n", "\n", "session = Session()\n", "df = pd.read_sql(sql = session.query(PetrolModel)\\\n", "                         .with_entities(\n", "                             PetrolModel.auto_id,\n", "                             PetrolModel.datetime,\n", "                             PetrolModel.data_details[0]['che_liang_id'],\n", "                             PetrolModel.data_details[0]['distant'],\n", "                             PetrolModel.data_details[0]['qty'],\n", "                             ).statement, \n", "                 con = session.bind)\n", "\n", "df = df.rename(columns={\n", "    \"anon_1\": 'che_liang_id',\n", "    \"anon_2\": 'distant',\n", "    \"anon_3\": 'qty',\n", "    })\n", "\n", "\n", "\n", "# mask = (df['datetime'] > '2021-12-30')\n", "mask = df[\"datetime\"].between('2021-10-01', '2022-02-05')\n", "df =df.loc[mask]\n", "# df = df[df['datetime'].dt.year == 2020]\n", "display(df)\n", "\n", "\n", "\n", "df2 = df[['che_liang_id']]\n", "df2 = df2.drop_duplicates(subset='che_liang_id', keep=\"first\")\n", "display(df2)\n", "\n", "\n", "\n", "df3 = df[df[\"che_liang_id\"] == 194]\n", "\n", "# km = df3['distant'].max() - df3['distant'].min()\n", "# lit = df3['qty'].sum()\n", "# km_lit = km / lit\n", "\n", "# df3['distant_2'] = df3['distant'].diff()\n", "# df3['km_lit'] = df3['distant_2'].shift(0) / df3['qty'].shift(1)\n", "\n", "df3['datetime'] = df3['datetime'].dt.strftime(\"%Y-%m-%d\")\n", "\n", "df3['previous_distant'] = df3['distant'].shift()\n", "def fx(x):\n", "    if x['previous_distant'] == 0:\n", "        return x['previous_distant']\n", "    elif x['distant'] == 0:\n", "        return x['distant']\n", "    else:\n", "        return x['distant'] - x['previous_distant']\n", "df3['distance_used'] = df3.apply(lambda x : fx(x),axis=1)\n", "df3['previous_qty'] = df3['qty'].shift()\n", "df3['km_lit'] = df3['distance_used'] / df3['previous_qty']\n", "df3['average'] = df3[df3['km_lit'] != 0][\"km_lit\"].mean()\n", "def fx2(x):\n", "    if x['km_lit'] == 0:\n", "        return x['km_lit']\n", "    else:\n", "        return (x.average - x.km_lit) / x.average * 100\n", "df3['average_percent'] = df3.apply(lambda x : fx2(x),axis=1).fillna(0)\n", "# df['new_val'] = df['new_val'].fillna(0)\n", "df3['sum'] = df3['average_percent'].sum()\n", "\n", "# df3['previous_distant'] = df3['distant'].shift()\n", "# df3['distance_used'] = df3['distant'] - df3['previous_distant']\n", "# df3['previous_qty'] = df3['qty'].shift()\n", "# df3['km_lit'] = df3['distance_used'] / df3['previous_qty']\n", "# df3['average'] = df3['km_lit'].mean()\n", "# df3['average_percent'] = (df3.average - df3.km_lit) / df3.average * 100\n", "display(df3)\n", "\n", "\n", "# df4 = pd.DataFrame(df3,columns=['datetime','km_lit'])\n", "# df5 = pd.DataFrame(df3,columns=['datetime','average'])\n", "# plt.plot('datetime','km_lit',data=df4,marker='o',color='blue',linewidth=2)\n", "# plt.plot('datetime','average',data=df5,color='red',linewidth=2)\n", "# plt.xticks(rotation=90)\n", "\n", "\n", "# df4 = df3.to_html()\n", "# display(df4)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import sqlalchemy\n", "from sqlalchemy import create_engine, tuple_\n", "from sqlalchemy.pool import NullPool\n", "import numpy as np\n", "import json\n", "from sqlalchemy.ext.declarative import declarative_base\n", "from sqlalchemy import Column, Integer, String, Date, DateTime, ForeignKey, TEXT\n", "from sqlalchemy.sql.expression import table, text\n", "from sqlalchemy.dialects.postgresql.json import JSONB\n", "from sqlalchemy.orm import sessionmaker\n", "from sqlalchemy import literal\n", "import matplotlib.pyplot as plt\n", "# import seaborn as sns\n", "# from sklearn.ensemble import IsolationForest\n", "# from sklearn.linear_model import LinearRegression\n", "\n", "DATABASE_URL = \"*************************************************/test_nern\"\n", "engine = create_engine(DATABASE_URL, poolclass=NullPool)\n", "Session = sessionmaker(bind=engine)\n", "\n", "Base = declarative_base()\n", "\n", "class PetrolModel(Base):\n", "    __tablename__ = 'petrol'\n", "    auto_id = Column(Integer, primary_key=True)\n", "    petrol_id = Column(TEXT(convert_unicode=True), server_default=text(\"'petrol'||lpad(NEXTVAL('petrol_sequence'::regclass)::text,8, '0')\"))\n", "    datetime = Column(DateTime)\n", "    data_details = Column(JSONB, nullable=False, server_default='[]')\n", "\n", "\n", "session = Session()\n", "df = pd.read_sql(sql = session.query(PetrolModel)\\\n", "                         .with_entities(\n", "                             PetrolModel.auto_id,\n", "                             PetrolModel.datetime,\n", "                             PetrolModel.data_details[0]['che_liang_id'],\n", "                             PetrolModel.data_details[0]['distant'],\n", "                             PetrolModel.data_details[0]['qty'],\n", "                             ).statement, \n", "                 con = session.bind)\n", "\n", "df = df.rename(columns={\n", "    \"anon_1\": 'che_liang_id',\n", "    \"anon_2\": 'distant',\n", "    \"anon_3\": 'qty',\n", "    })\n", "\n", "\n", "\n", "mask = df[\"datetime\"].between('2021-08-01', '2022-02-05')\n", "# year = datetime.now().year\n", "# mask = (df['datetime'] > str(year))\n", "df =df.loc[mask]\n", "display(df)\n", "\n", "\n", "\n", "df2 = df[['che_liang_id']]\n", "df2 = df2.drop_duplicates(subset='che_liang_id', keep=\"first\")\n", "display(df2)\n", "\n", "\n", "df3 = df[df[\"che_liang_id\"] == 22046]\n", "df3['datetime'] = df3['datetime'].dt.strftime(\"%Y-%m-%d\")\n", "df3['previous_distant'] = df3['distant'].shift()\n", "def fx(x):\n", "    if x['previous_distant'] == 0:\n", "        return x['previous_distant']\n", "    elif x['distant'] == 0:\n", "        return x['distant']\n", "    else:\n", "        return x['distant'] - x['previous_distant']\n", "df3['distance_used'] = df3.apply(lambda x : fx(x),axis=1)\n", "df3['previous_qty'] = df3['qty'].shift()\n", "df3['km_lit'] = df3['distance_used'] / df3['previous_qty']               \n", "df3['average'] = df3[df3['km_lit'] != 0][\"km_lit\"].mean()\n", "def fx2(x):\n", "    if x['km_lit'] == 0:\n", "        return x['km_lit']\n", "    else:\n", "        return (x.average - x.km_lit) / x.average * 100\n", "df3['average_percent'] = df3.apply(lambda x : fx2(x),axis=1).fillna(0)\n", "\n", "def remove_outliers(df3, q=0.05):\n", "    upper = df3.quantile(1-q)\n", "    lower = df3.quantile(q)\n", "    mask = (df3 < upper) & (df3 > lower)\n", "    return mask\n", "df3['outliers'] = remove_outliers(df3['km_lit'], 0.1)\n", "\n", "def fx3(x):\n", "    if x['outliers'] == False:\n", "        return None\n", "    else:\n", "        return x['distance_used'] / x['previous_qty']\n", "df3['km_lit_2'] = df3.apply(lambda x : fx3(x),axis=1)\n", "df3['average_2'] = df3[df3['km_lit_2'] != 0][\"km_lit_2\"].mean()\n", "def fx4(x):\n", "    if x['km_lit_2'] == None:\n", "        return x['km_lit_2']\n", "    else:\n", "        return (x.average_2 - x.km_lit_2) / x.average_2 * 100\n", "df3['average_percent_2'] = df3.apply(lambda x : fx4(x),axis=1).fillna(0)\n", "df3['mean'] = (df3.average - df3.average_2) / df3.average * 100\n", "display(df3)\n", "\n", "\n", "# df4 = pd.DataFrame(df3,columns=['datetime','km_lit'])\n", "# df5 = pd.DataFrame(df3,columns=['datetime','average'])\n", "# plt.plot('datetime','km_lit',data=df4,marker='o',color='blue',linewidth=2)\n", "# plt.plot('datetime','average',data=df5,color='red',linewidth=2)\n", "# plt.xticks(rotation=90)\n", "\n", "\n", "# df4 = pd.DataFrame(df3,columns=['datetime','km_lit_2'])\n", "# df5 = pd.DataFrame(df3,columns=['datetime','average_2'])\n", "# plt.plot('datetime','km_lit_2',data=df4,marker='o',color='blue',linewidth=2)\n", "# plt.plot('datetime','average_2',data=df5,color='red',linewidth=2)\n", "# plt.xticks(rotation=90)\n", "\n", "# distance_used\n", "# previous_distant\n", "\n", "# data = pd.DataFrame(df3)\n", "# data = data.dropna()\n", "# X = data[\"km_lit\"].values.reshape(-1, 1)\n", "# Y = data[\"previous_distant\"].values.reshape(-1, 1)\n", "# linear_regressor = LinearRegression() \n", "# linear_regressor.fit(X, Y)\n", "# Y_pred = linear_regressor.predict(X)  \n", "# plt.scatter(X, Y)\n", "# plt.plot(X, Y_pred, color='red')\n", "# plt.show()\n", "\n", "# print(X)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt  # To visualize\n", "import pandas as pd  # To read data\n", "from sklearn.linear_model import LinearRegression\n", "\n", "# 1, 2, 3, 4, 5, 6, 7, 8, 9, 10\n", "# 5, 8, 3, 1, 5, 6, 12, 8, 16, 16\n", "# data = pd.read_csv('data.csv')  # load data set\n", "dataset = {'mark1':[5, 8, 3, 1, 5, 6, 12, 8, 16, 16],\n", "        'mark2':[1, 2, 3, 4, 5, 6, 7, 8, 9, 10]}\n", "data = pd.DataFrame(dataset)\n", "X = data.iloc[:, 0].values.reshape(-1, 1)  # values converts it into a numpy array\n", "Y = data.iloc[:, 1].values.reshape(-1, 1)  # -1 means that calculate the dimension of rows, but have 1 column\n", "linear_regressor = LinearRegression()  # create object for the class\n", "linear_regressor.fit(X, Y)  # perform linear regression\n", "Y_pred = linear_regressor.predict(X)  # make predictions\n", "plt.scatter(X, Y)\n", "plt.plot(X, Y_pred, color='red')\n", "plt.show()\n", "\n", "print(X)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import sqlalchemy\n", "from sqlalchemy import create_engine, tuple_\n", "from sqlalchemy.pool import NullPool\n", "import numpy as np\n", "import json\n", "from sqlalchemy.ext.declarative import declarative_base\n", "from sqlalchemy import Column, Integer, String, Date, DateTime, ForeignKey, TEXT\n", "from sqlalchemy.sql.expression import table, text\n", "from sqlalchemy.dialects.postgresql.json import JSONB\n", "from sqlalchemy.orm import sessionmaker\n", "from sqlalchemy import literal\n", "import datetime\n", "\n", "\n", "DATABASE_URL = \"*************************************************/test_nern\"\n", "engine = create_engine(DATABASE_URL, poolclass=NullPool)\n", "Session = sessionmaker(bind=engine)\n", "\n", "Base = declarative_base()\n", "\n", "class PetrolModel(Base):\n", "    __tablename__ = 'petrol'\n", "    auto_id = Column(Integer, primary_key=True)\n", "    petrol_id = Column(TEXT(convert_unicode=True), server_default=text(\"'petrol'||lpad(NEXTVAL('petrol_sequence'::regclass)::text,8, '0')\"))\n", "    datetime = Column(DateTime)\n", "    data_details = Column(JSONB, nullable=False, server_default='[]')\n", "\n", "\n", "session = Session()\n", "df = pd.read_sql(sql = session.query(PetrolModel)\\\n", "                         .with_entities(\n", "                             PetrolModel.auto_id,\n", "                             PetrolModel.datetime,\n", "                             PetrolModel.data_details[0]['che_liang_id'],\n", "                             PetrolModel.data_details[0]['distant'],\n", "                             PetrolModel.data_details[0]['qty'],\n", "                             ).statement, \n", "                 con = session.bind)\n", "\n", "df = df.rename(columns={\n", "    \"anon_1\": 'che_liang_id',\n", "    \"anon_2\": 'distant',\n", "    \"anon_3\": 'qty',\n", "    })\n", "\n", "\n", "\n", "\n", "today = datetime.date.today()\n", "future_day = today.day\n", "future_month = (today.month - 6) % 12\n", "future_year = today.year + ((today.month - 6) // 12)\n", "six_months_later = datetime.date(future_year, future_month, future_day)\n", "mask = (df['datetime'] > str(six_months_later))\n", "df =df.loc[mask]\n", "display(df, six_months_later)\n", "\n", "\n", "\n", "df2 = df[['che_liang_id']]\n", "df2 = df2.drop_duplicates(subset='che_liang_id', keep=\"first\")\n", "display(df2)\n", "\n", "\n", "df3 = df[df[\"che_liang_id\"] == 194]\n", "df3['datetime'] = df3['datetime'].dt.strftime(\"%Y-%m-%d\")\n", "df3['previous_distant'] = df3['distant'].shift()\n", "def fx(x):\n", "    if x['previous_distant'] == 0:\n", "        return x['previous_distant']\n", "    elif x['distant'] == 0:\n", "        return x['distant']\n", "    else:\n", "        return x['distant'] - x['previous_distant']\n", "df3['distance_used'] = df3.apply(lambda x : fx(x),axis=1)\n", "df3['previous_qty'] = df3['qty'].shift()\n", "df3['km_lit'] = df3['distance_used'] / df3['previous_qty']               \n", "df3['average'] = df3[df3['km_lit'] != 0][\"km_lit\"].mean()\n", "def fx2(x):\n", "    if x['km_lit'] == 0:\n", "        return x['km_lit']\n", "    else:\n", "        return (x.average - x.km_lit) / x.average * 100\n", "df3['average_percent'] = df3.apply(lambda x : fx2(x),axis=1).fillna(0)\n", "\n", "def remove_outliers(df3, q=0.05):\n", "    upper = df3.quantile(1-q)\n", "    lower = df3.quantile(q)\n", "    mask = (df3 < upper) & (df3 > lower)\n", "    return mask\n", "df3['outliers'] = remove_outliers(df3['km_lit'], 0.1)\n", "\n", "def fx3(x):\n", "    if x['outliers'] == False:\n", "        return None\n", "    else:\n", "        return x['distance_used'] / x['previous_qty']\n", "df3['km_lit_2'] = df3.apply(lambda x : fx3(x),axis=1)\n", "df3['average_2'] = df3[df3['km_lit_2'] != 0][\"km_lit_2\"].mean()\n", "def fx4(x):\n", "    if x['km_lit_2'] == None:\n", "        return x['km_lit_2']\n", "    else:\n", "        return (x.average_2 - x.km_lit_2) / x.average_2 * 100\n", "df3['average_percent_2'] = df3.apply(lambda x : fx4(x),axis=1).fillna(0)\n", "df3['mean'] = (df3.average - df3.average_2) / df3.average * 100\n", "display(df3)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import sqlalchemy\n", "from sqlalchemy import create_engine, tuple_\n", "from sqlalchemy.pool import NullPool\n", "import numpy as np\n", "import json\n", "from sqlalchemy.ext.declarative import declarative_base\n", "from sqlalchemy import Column, Integer, String, Date, DateTime, ForeignKey, TEXT\n", "from sqlalchemy.sql.expression import table, text\n", "from sqlalchemy.dialects.postgresql.json import JSONB\n", "from sqlalchemy.orm import sessionmaker\n", "from sqlalchemy import literal\n", "import datetime\n", "\n", "\n", "DATABASE_URL = \"*************************************************/test_nern\"\n", "engine = create_engine(DATABASE_URL, poolclass=NullPool)\n", "Session = sessionmaker(bind=engine)\n", "\n", "Base = declarative_base()\n", "\n", "class PetrolModel(Base):\n", "    __tablename__ = 'petrol'\n", "    auto_id = Column(Integer, primary_key=True)\n", "    petrol_id = Column(TEXT(convert_unicode=True), server_default=text(\"'petrol'||lpad(NEXTVAL('petrol_sequence'::regclass)::text,8, '0')\"))\n", "    datetime = Column(DateTime)\n", "    data_details = Column(JSONB, nullable=False, server_default='[]')\n", "\n", "\n", "session = Session()\n", "\n", "\n", "page_size = 10\n", "page = 1\n", "page -= 1\n", "df10 = pd.read_sql(sql = session.query(PetrolModel.auto_id,\n", "                                     PetrolModel.datetime,\n", "                                     PetrolModel.data_details[0]['che_liang_id'],\n", "                                     ).order_by(PetrolModel.datetime.desc()).limit(page_size).offset(page*page_size).statement, con = session.bind)\n", "\n", "df10 = df10.rename(columns={\n", "    \"anon_1\": 'che_liang_id',\n", "    \"anon_2\": 'distant',\n", "    \"anon_3\": 'qty',\n", "    })\n", "\n", "dataList = [] \n", "for row in df10.itertuples(): \n", "    mylist = [row.che_liang_id]\n", "    dataList.extend(mylist)\n", "# aaa = dataList = 194\n", "display(df10)\n", "\n", "\n", "df20 = pd.read_sql(sql = session.query(PetrolModel.auto_id,\n", "                                     PetrolModel.petrol_id,\n", "                                     PetrolModel.datetime,\n", "                                     PetrolModel.data_details[0]['che_liang_id'],\n", "                                     PetrolModel.data_details[0]['distant'],\n", "                                     PetrolModel.data_details[0]['qty']\n", "                                     ) \n", "                    .filter(PetrolModel.data_details[0]['che_liang_id'].astext.cast(Integer).in_(dataList)).statement, \n", "                    con = session.bind)\n", "\n", "df20 = df20.rename(columns={\n", "    \"anon_1\": 'che_liang_id',\n", "    \"anon_2\": 'distant',\n", "    \"anon_3\": 'qty',\n", "    })\n", "# display(df20)\n", "\n", "\n", "\n", "today = datetime.date.today()\n", "future_day = today.day\n", "future_month = (today.month - 6) % 12\n", "future_year = today.year + ((today.month - 6) // 12)\n", "six_months_later = datetime.date(future_year, future_month, future_day)\n", "mask = (df20['datetime'] > str(six_months_later))\n", "df20 = df20.loc[mask]\n", "display(df20)\n", "\n", "\n", "# df21 = df20[df20['che_liang_id'] == 194]\n", "# display(df21)\n", "\n", "\n", "# df21['datetime'] = df21['datetime'].dt.strftime(\"%Y-%m-%d\")\n", "# df21['previous_distant'] = df21['distant'].shift()\n", "# def fx(x):\n", "#     if x['previous_distant'] == 0:\n", "#         return x['previous_distant']\n", "#     elif x['distant'] == 0:\n", "#         return x['distant']\n", "#     else:\n", "#         return x['distant'] - x['previous_distant']\n", "# df21['distance_used'] = df21.apply(lambda x : fx(x),axis=1)\n", "# df21['previous_qty'] = df21['qty'].shift()\n", "# df21['km_lit'] = df21['distance_used'] / df21['previous_qty']               \n", "# df21['average'] = df21[df21['km_lit'] != 0][\"km_lit\"].mean()\n", "# def fx2(x):\n", "#     if x['km_lit'] == 0:\n", "#         return x['km_lit']\n", "#     else:\n", "#         return (x.average - x.km_lit) / x.average * 100\n", "# df21['average_percent'] = df21.apply(lambda x : fx2(x),axis=1).fillna(0)\n", "\n", "# def remove_outliers(df21, q=0.05):\n", "#     upper = df21.quantile(1-q)\n", "#     lower = df21.quantile(q)\n", "#     mask = (df21 < upper) & (df21 > lower)\n", "#     return mask\n", "# df21['outliers'] = remove_outliers(df21['km_lit'], 0.1)\n", "\n", "# def fx3(x):\n", "#     if x['outliers'] == False:\n", "#         return None\n", "#     else:\n", "#         return x['distance_used'] / x['previous_qty']\n", "# df21['km_lit_2'] = df21.apply(lambda x : fx3(x),axis=1)\n", "# df21['average_2'] = df21[df21['km_lit_2'] != 0][\"km_lit_2\"].mean()\n", "# def fx4(x):\n", "#     if x['km_lit_2'] == None:\n", "#         return x['km_lit_2']\n", "#     else:\n", "#         return (x.average_2 - x.km_lit_2) / x.average_2 * 100\n", "# df21['average_percent_2'] = df21.apply(lambda x : fx4(x),axis=1).fillna(0)\n", "# df21['mean'] = (df21.average - df21.average_2) / df21.average * 100\n", "# display(df21)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import sqlalchemy\n", "from sqlalchemy import create_engine, tuple_\n", "from sqlalchemy.pool import NullPool\n", "import numpy as np\n", "import json\n", "from sqlalchemy.ext.declarative import declarative_base\n", "from sqlalchemy import Column, Integer, String, Date, DateTime, ForeignKey, TEXT\n", "from sqlalchemy.sql.expression import table, text\n", "from sqlalchemy.dialects.postgresql.json import JSONB\n", "from sqlalchemy.orm import sessionmaker\n", "from sqlalchemy import literal\n", "import datetime\n", "\n", "\n", "DATABASE_URL = \"*************************************************/test_nern\"\n", "engine = create_engine(DATABASE_URL, poolclass=NullPool)\n", "Session = sessionmaker(bind=engine)\n", "\n", "Base = declarative_base()\n", "\n", "class PetrolModel(Base):\n", "    __tablename__ = 'petrol'\n", "    auto_id = Column(Integer, primary_key=True)\n", "    petrol_id = Column(TEXT(convert_unicode=True), server_default=text(\"'petrol'||lpad(NEXTVAL('petrol_sequence'::regclass)::text,8, '0')\"))\n", "    datetime = Column(DateTime)\n", "    data_details = Column(JSONB, nullable=False, server_default='[]')\n", "\n", "\n", "session = Session()\n", "\n", "\n", "page_size = 10\n", "page = 2\n", "page -= 1\n", "df10 = pd.read_sql(sql = session.query(PetrolModel.auto_id,\n", "                                     PetrolModel.datetime,\n", "                                     PetrolModel.data_details[0]['che_liang_id'],\n", "                                     ).order_by(PetrolModel.datetime.desc()).limit(page_size).offset(page*page_size).statement, con = session.bind)\n", "\n", "df10 = df10.rename(columns={\n", "    \"anon_1\": 'che_liang_id',\n", "    \"anon_2\": 'distant',\n", "    \"anon_3\": 'qty',\n", "    })\n", "\n", "dataList = [] \n", "for row in df10.itertuples(): \n", "    mylist = [row.che_liang_id]\n", "    dataList.extend(mylist)\n", "# aaa = dataList = 194\n", "display(df10)\n", "\n", "\n", "\n", "\n", "\n", "df20 = pd.read_sql(sql = session.query(PetrolModel)\\\n", "                        .with_entities(\n", "                            PetrolModel.auto_id,\n", "                            PetrolModel.petrol_id,\n", "                            PetrolModel.datetime,\n", "                            PetrolModel.data_details[0]['che_liang_id'],\n", "                            PetrolModel.data_details[0]['distant'],\n", "                            PetrolModel.data_details[0]['qty'],\n", "                            )\n", "                        # .filter(PetrolModel.data_details[0]['che_liang_id'].astext.cast(Integer) == 194).statement,\n", "                        # .filter(PetrolModel.data_details[0]['che_liang_id'].astext.cast(Integer).in_(dataList)).statement, \n", "                        # .filter(PetrolModel.data_details[0]['che_liang_id'].astext.cast(Integer).in_([f for f in dataList])).statement, \n", "                con = session.bind)\n", "\n", "\n", "df20 = df20.rename(columns={\n", "    \"anon_1\": 'che_liang_id',\n", "    \"anon_2\": 'distant',\n", "    \"anon_3\": 'qty',\n", "    })\n", "\n", "display(df20)\n", "\n", "\n", "\n", "# today = datetime.date.today()\n", "# future_day = today.day\n", "# future_month = (today.month - 6) % 12\n", "# future_year = today.year + ((today.month - 6) // 12)\n", "# six_months_later = datetime.date(future_year, future_month, future_day)\n", "# mask = (df20['datetime'] > str(six_months_later))\n", "# df20 = df20.loc[mask]\n", "# df20 = df20.head(10)\n", "# df20['zz'] = 'aa'\n", "\n", "# df20['datetime'] = df20['datetime'].dt.strftime(\"%Y-%m-%d\")\n", "# df20['previous_distant'] = df20['distant'].shift()\n", "# def fx(x):\n", "#     if x['previous_distant'] == 0:\n", "#         return x['previous_distant']\n", "#     elif x['distant'] == 0:\n", "#         return x['distant']\n", "#     else:\n", "#         return x['distant'] - x['previous_distant']\n", "# df20['distance_used'] = df20.apply(lambda x : fx(x),axis=1)\n", "# df20['previous_qty'] = df20['qty'].shift()\n", "# df20['km_lit'] = df20['distance_used'] / df20['previous_qty']               \n", "# df20['average'] = df20[df20['km_lit'] != 0][\"km_lit\"].mean()\n", "# def fx2(x):\n", "#     if x['km_lit'] == 0:\n", "#         return x['km_lit']\n", "#     else:\n", "#         return (x.average - x.km_lit) / x.average * 100\n", "# df20['average_percent'] = df20.apply(lambda x : fx2(x),axis=1).fillna(0)\n", "\n", "# def remove_outliers(df20, q=0.05):\n", "#     upper = df20.quantile(1-q)\n", "#     lower = df20.quantile(q)\n", "#     mask = (df20 < upper) & (df20 > lower)\n", "#     return mask\n", "# df20['outliers'] = remove_outliers(df20['km_lit'], 0.1)\n", "\n", "# def fx3(x):\n", "#     if x['outliers'] == False:\n", "#         return None\n", "#     else:\n", "#         return x['distance_used'] / x['previous_qty']\n", "# df20['km_lit_2'] = df20.apply(lambda x : fx3(x),axis=1)\n", "# df20['average_2'] = df20[df20['km_lit_2'] != 0][\"km_lit_2\"].mean()\n", "# def fx4(x):\n", "#     if x['km_lit_2'] == None:\n", "#         return x['km_lit_2']\n", "#     else:\n", "#         return (x.average_2 - x.km_lit_2) / x.average_2 * 100\n", "# df20['average_percent_2'] = df20.apply(lambda x : fx4(x),axis=1).fillna(0)\n", "# df20['mean'] = (df20.average - df20.average_2) / df20.average * 100\n", "# display(df20)\n", "\n", "\n", "\n", "# df30 = df10.merge(df20, on='auto_id', how='left')\n", "# display(df30)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import sqlalchemy\n", "from sqlalchemy import create_engine, tuple_\n", "from sqlalchemy.pool import NullPool\n", "import numpy as np\n", "import json\n", "from sqlalchemy.ext.declarative import declarative_base\n", "from sqlalchemy import Column, Integer, String, Date, DateTime, ForeignKey, TEXT\n", "from sqlalchemy.sql.expression import table, text\n", "from sqlalchemy.dialects.postgresql.json import JSONB\n", "from sqlalchemy.orm import sessionmaker\n", "from sqlalchemy import and_, or_, not_\n", "from sqlalchemy import literal\n", "import datetime\n", "import itertools\n", "\n", "DATABASE_URL = \"*************************************************/test_nern\"\n", "engine = create_engine(DATABASE_URL, poolclass=NullPool)\n", "Session = sessionmaker(bind=engine)\n", "\n", "Base = declarative_base()\n", "\n", "class PetrolModel(Base):\n", "    __tablename__ = 'petrol'\n", "    auto_id = Column(Integer, primary_key=True)\n", "    petrol_id = Column(TEXT(convert_unicode=True), server_default=text(\"'petrol'||lpad(NEXTVAL('petrol_sequence'::regclass)::text,8, '0')\"))\n", "    datetime = Column(DateTime)\n", "    data_details = Column(JSONB, nullable=False, server_default='[]')\n", "\n", "\n", "session = Session()\n", "\n", "\n", "page_size = 2\n", "page = 5\n", "page -= 1\n", "df10 = pd.read_sql(sql = session.query(PetrolModel.auto_id,\n", "                                     PetrolModel.datetime,\n", "                                     PetrolModel.data_details[0]['che_liang_id'],\n", "                                     ).order_by(PetrolModel.datetime.desc()).limit(page_size).offset(page*page_size).statement, con = session.bind)\n", "\n", "df10 = df10.rename(columns={\n", "    \"anon_1\": 'che_liang_id',\n", "    \"anon_2\": 'distant',\n", "    \"anon_3\": 'qty',\n", "    })\n", "\n", "dataList = [] \n", "for row in df10.itertuples(): \n", "    mylist = [row.che_liang_id]\n", "    dataList.extend(mylist)\n", "# aaa = dataList = 194\n", "display(df10)\n", "\n", "\n", "\n", "today = datetime.date.today()\n", "future_day = today.day\n", "future_month = (today.month - 6) % 12\n", "future_year = today.year + ((today.month - 6) // 12)\n", "six_months_ago = datetime.date(future_year, future_month, future_day)\n", "df20 = pd.read_sql(sql = session.query(PetrolModel)\\\n", "                        .with_entities(\n", "                            PetrolModel.auto_id,\n", "                            PetrolModel.petrol_id,\n", "                            PetrolModel.datetime,\n", "                            PetrolModel.data_details[0]['che_liang_id'],\n", "                            PetrolModel.data_details[0]['distant'],\n", "                            PetrolModel.data_details[0]['qty'],\n", "                            )\n", "                        # .filter(PetrolModel.data_details[0]['che_liang_id'].astext.cast(Integer) == 194).statement,\n", "                        # .filter(PetrolModel.data_details[0]['che_liang_id'].astext.cast(Integer).in_(dataList)).statement, \n", "                            .filter(\n", "                                and_(\n", "                                    PetrolModel.datetime > six_months_ago,\n", "                                    PetrolModel.data_details[0]['che_liang_id'].astext.cast(Integer).in_(dataList),\n", "                                    )\n", "                                ).statement,\n", "                con = session.bind)\n", "\n", "\n", "df20 = df20.rename(columns={\n", "    \"anon_1\": 'che_liang_id',\n", "    \"anon_2\": 'distant',\n", "    \"anon_3\": 'qty',\n", "    })\n", "\n", "# display(df20)\n", "\n", "ccc = []\n", "mylist = list(dict.fromkeys(dataList))\n", "for i in mylist:\n", "    df21 = df20[df20['che_liang_id'] == i]\n", "    # display(df21)\n", "    # df21['previous_distant'] = df21['distant'].shift()\n", "    # def fx(x):\n", "    #     if x['previous_distant'] == 0:\n", "    #         return x['previous_distant']\n", "    #     elif x['distant'] == 0:\n", "    #         return x['distant']\n", "    #     else:\n", "    #         return x['distant'] - x['previous_distant']\n", "    # df21['distance_used'] = df21.apply(lambda x : fx(x),axis=1)\n", "    # df21['previous_qty'] = df21['qty'].shift()\n", "    # df21['km_lit'] = df21['distance_used'] / df21['previous_qty']               \n", "    # df21['average'] = df21[df21['km_lit'] != 0][\"km_lit\"].mean()\n", "    # def fx2(x):\n", "    #     if x['km_lit'] == 0:\n", "    #         return x['km_lit']\n", "    #     else:\n", "    #         return (x.average - x.km_lit) / x.average * 100\n", "    # df21['average_percent'] = df21.apply(lambda x : fx2(x),axis=1).fillna(0)\n", "\n", "    # def remove_outliers(df21, q=0.05):\n", "    #     upper = df21.quantile(1-q)\n", "    #     lower = df21.quantile(q)\n", "    #     mask = (df21 < upper) & (df21 > lower)\n", "    #     return mask\n", "    # df21['outliers'] = remove_outliers(df21['km_lit'], 0.1)\n", "\n", "    # def fx3(x):\n", "    #     if x['outliers'] == False:\n", "    #         return None\n", "    #     else:\n", "    #         return x['distance_used'] / x['previous_qty']\n", "    # df21['km_lit_2'] = df21.apply(lambda x : fx3(x),axis=1)\n", "    # df21['average_2'] = df21[df21['km_lit_2'] != 0][\"km_lit_2\"].mean()\n", "    # def fx4(x):\n", "    #     if x['km_lit_2'] == None:\n", "    #         return x['km_lit_2']\n", "    #     else:\n", "    #         return (x.average_2 - x.km_lit_2) / x.average_2 * 100\n", "    # df21['average_percent_2'] = df21.apply(lambda x : fx4(x),axis=1).fillna(0)\n", "    # df21['mean'] = (df21.average - df21.average_2) / df21.average * 100\n", "    \n", "    # df30 = df10.merge(df21, on='auto_id', how='inner')\n", "    \n", "    \n", "    df30 = df21.to_dict('records')\n", "    ccc.extend(df30)\n", "    display(ccc)\n", "# display(mylist)\n", "\n", "# df30 = df10.merge(df21, on='auto_id', how='inner')\n", "# display(df30)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import sqlalchemy\n", "from sqlalchemy import create_engine, tuple_\n", "from sqlalchemy.pool import NullPool\n", "import numpy as np\n", "import json\n", "from sqlalchemy.ext.declarative import declarative_base\n", "from sqlalchemy import Column, Integer, String, Date, DateTime, ForeignKey, TEXT\n", "from sqlalchemy.sql.expression import table, text\n", "from sqlalchemy.dialects.postgresql.json import JSONB\n", "from sqlalchemy.orm import sessionmaker\n", "from sqlalchemy import and_, or_, not_\n", "from sqlalchemy import literal\n", "import datetime\n", "import itertools\n", "\n", "DATABASE_URL = \"*************************************************/n_data\"\n", "engine = create_engine(DATABASE_URL, poolclass=NullPool)\n", "Session = sessionmaker(bind=engine)\n", "\n", "Base = declarative_base()\n", "\n", "class PetrolModel(Base):\n", "    __tablename__ = 'petrol'\n", "    auto_id = Column(Integer, primary_key=True)\n", "    petrol_id = Column(TEXT(convert_unicode=True), server_default=text(\"'petrol'||lpad(NEXTVAL('petrol_sequence'::regclass)::text,8, '0')\"))\n", "    datetime = Column(DateTime)\n", "    data_details = Column(JSONB, nullable=False, server_default='[]')\n", "    \n", "    \n", "class PetrolModel2(Base):\n", "    __tablename__ = 'petrol2'\n", "    auto_id = Column(Integer, primary_key=True)\n", "    petrol_id = Column(TEXT(convert_unicode=True), server_default=text(\"'petrol'||lpad(NEXTVAL('petrol_sequence'::regclass)::text,8, '0')\"))\n", "    datetime = Column(DateTime)\n", "    data_details = Column(JSONB, nullable=False, server_default='[]')\n", "    status_details = Column(JSONB, nullable=False, server_default='{}')    \n", "\n", "\n", "session = Session()\n", "\n", "\n", "page_size = 2\n", "page = 1\n", "page -= 1\n", "df10 = pd.read_sql(sql = session.query(PetrolModel.petrol_id,\n", "                                     PetrolModel.datetime,\n", "                                     PetrolModel.data_details[0]['che_liang_id'],\n", "                                     ).order_by(PetrolModel.datetime.desc()).limit(page_size).offset(page*page_size).statement, con = session.bind)\n", "\n", "df10 = df10.rename(columns={\n", "    \"anon_1\": 'che_liang_id',\n", "    \"anon_2\": 'distant',\n", "    \"anon_3\": 'qty',\n", "    })\n", "\n", "dataList = [] \n", "for row in df10.itertuples(): \n", "    mylist = [row.che_liang_id]\n", "    dataList.extend(mylist)\n", "# aaa = dataList = 194\n", "display(df10)\n", "\n", "\n", "\n", "today = datetime.date.today()\n", "future_day = today.day\n", "future_month = (today.month - 6) % 12\n", "future_year = today.year + ((today.month - 6) // 12)\n", "six_months_ago = datetime.date(future_year, future_month, future_day)\n", "\n", "\n", "df20 = pd.read_sql(sql = session.query(PetrolModel)\\\n", "                        .with_entities(\n", "                            PetrolModel.petrol_id,\n", "                            PetrolModel.datetime,\n", "                            PetrolModel.data_details[0]['che_liang_id'],\n", "                            PetrolModel.data_details[0]['distant'],\n", "                            PetrolModel.data_details[0]['qty'],\n", "                            )\n", "                        # .filter(PetrolModel.data_details[0]['che_liang_id'].astext.cast(Integer) == 194).statement,\n", "                        # .filter(PetrolModel.data_details[0]['che_liang_id'].astext.cast(Integer).in_(dataList)).statement, \n", "                            .filter(\n", "                                and_(\n", "                                    PetrolModel.datetime > six_months_ago,\n", "                                    PetrolModel.data_details[0]['che_liang_id'].astext.cast(Integer).in_(dataList),\n", "                                    )\n", "                                ).statement,\n", "                con = session.bind)\n", "\n", "\n", "df20 = df20.rename(columns={\n", "    \"anon_1\": 'che_liang_id',\n", "    \"anon_2\": 'distant',\n", "    \"anon_3\": 'qty',\n", "    })\n", "\n", "# display(df20)\n", "\n", "\n", "df30 = pd.read_sql(sql = session.query(PetrolModel2)\\\n", "                        .with_entities(\n", "                            # PetrolModel2.auto_id,\n", "                            PetrolModel2.petrol_id,\n", "                            PetrolModel2.datetime,\n", "                            PetrolModel2.data_details[0]['che_liang_id'],\n", "                            PetrolModel2.data_details[0]['distant'],\n", "                            PetrolModel2.data_details[0]['qty'],\n", "                            )\n", "                        # .filter(PetrolModel.data_details[0]['che_liang_id'].astext.cast(Integer) == 194).statement,\n", "                        # .filter(PetrolModel.data_details[0]['che_liang_id'].astext.cast(Integer).in_(dataList)).statement, \n", "                            .filter(\n", "                                and_(\n", "                                    PetrolModel2.datetime > six_months_ago,\n", "                                    PetrolModel2.data_details[0]['che_liang_id'].astext.cast(Integer).in_(dataList),\n", "                                    )\n", "                                ).statement,\n", "                con = session.bind)\n", "\n", "\n", "df30 = df30.rename(columns={\n", "    \"anon_1\": 'che_liang_id',\n", "    \"anon_2\": 'distant',\n", "    \"anon_3\": 'qty',\n", "    })\n", "\n", "# display(df30)\n", "\n", "\n", "df40 = pd.concat([df20, df30], ignore_index=True)\n", "# display(df40)\n", "\n", "\n", "ccc = []\n", "mylist = list(dict.fromkeys(dataList))\n", "for i in mylist:\n", "    df50 = df40[df40['che_liang_id'] == i ]\n", "    df50['previous_distant'] = df50['distant'].shift().fillna(0)\n", "    def fx(x):\n", "        if x['previous_distant'] == 0:\n", "            return x['previous_distant']\n", "        elif x['distant'] == 0:\n", "            return x['distant']\n", "        else:\n", "            return x['distant'] - x['previous_distant']\n", "    df50['distance_used'] = df50.apply(lambda x : fx(x),axis=1)\n", "    df50['previous_qty'] = df50['qty'].shift()\n", "    df50['km_lit'] = df50['distance_used'] / df50['previous_qty']               \n", "    df50['average'] = df50[df50['km_lit'] != 0][\"km_lit\"].mean()\n", "    def fx2(x):\n", "        if x['km_lit'] == 0:\n", "            return x['km_lit']\n", "        else:\n", "            return (x.average - x.km_lit) / x.average * 100\n", "    df50['average_percent'] = df50.apply(lambda x : fx2(x),axis=1).fillna(0)\n", "\n", "    def remove_outliers(df50, q=0.05):\n", "        upper = df50.quantile(1-q)\n", "        lower = df50.quantile(q)\n", "        mask = (df50 < upper) & (df50 > lower)\n", "        return mask\n", "    df50['outliers'] = remove_outliers(df50['km_lit'], 0.1)\n", "\n", "    def fx3(x):\n", "        if x['outliers'] == False:\n", "            return None\n", "        else:\n", "            return x['distance_used'] / x['previous_qty']\n", "    df50['km_lit_2'] = df50.apply(lambda x : fx3(x),axis=1)\n", "    df50['average_2'] = df50[df50['km_lit_2'] != 0][\"km_lit_2\"].mean()\n", "    def fx4(x):\n", "        if x['km_lit_2'] == None:\n", "            return x['km_lit_2']\n", "        else:\n", "            return (x.average_2 - x.km_lit_2) / x.average_2 * 100\n", "    df50['average_percent_2'] = df50.apply(lambda x : fx4(x),axis=1).fillna(0)\n", "    df50['mean'] = (df50.average - df50.average_2) / df50.average * 100\n", "    \n", "    # df60 = df10.merge(df50, on='petrol_id', how='inner')\n", "    # df60 =df60.to_dict(\"records\")\n", "    \n", "\n", "    \n", "    display(df50)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import sqlalchemy\n", "from sqlalchemy import create_engine, tuple_\n", "from sqlalchemy.pool import NullPool\n", "import numpy as np\n", "import json\n", "from sqlalchemy.ext.declarative import declarative_base\n", "from sqlalchemy import Column, Integer, String, Date, DateTime, ForeignKey, TEXT\n", "from sqlalchemy.sql.expression import table, text\n", "from sqlalchemy.dialects.postgresql.json import JSONB\n", "from sqlalchemy.orm import sessionmaker\n", "from sqlalchemy import and_, or_, not_\n", "from sqlalchemy import literal\n", "import datetime\n", "import itertools\n", "\n", "DATABASE_URL = \"*************************************************/n_data\"\n", "engine = create_engine(DATABASE_URL, poolclass=NullPool)\n", "Session = sessionmaker(bind=engine)\n", "\n", "Base = declarative_base()\n", "\n", "class PetrolModel(Base):\n", "    __tablename__ = 'petrol'\n", "    auto_id = Column(Integer, primary_key=True)\n", "    petrol_id = Column(TEXT(convert_unicode=True), server_default=text(\"'petrol'||lpad(NEXTVAL('petrol_sequence'::regclass)::text,8, '0')\"))\n", "    datetime = Column(DateTime)\n", "    data_details = Column(JSONB, nullable=False, server_default='[]')\n", "    \n", "    \n", "class PetrolModel2(Base):\n", "    __tablename__ = 'petrol2'\n", "    auto_id = Column(Integer, primary_key=True)\n", "    petrol_id = Column(TEXT(convert_unicode=True), server_default=text(\"'petrol'||lpad(NEXTVAL('petrol_sequence'::regclass)::text,8, '0')\"))\n", "    datetime = Column(DateTime)\n", "    data_details = Column(JSONB, nullable=False, server_default='[]')\n", "    status_details = Column(JSONB, nullable=False, server_default='{}')    \n", "\n", "\n", "session = Session()\n", "\n", "\n", "page_size = 2\n", "page = 2\n", "page -= 1\n", "df10 = pd.read_sql(sql = session.query(PetrolModel.petrol_id,\n", "                                     PetrolModel.datetime,\n", "                                     PetrolModel.data_details[0]['che_liang_id'],\n", "                                     ).order_by(PetrolModel.datetime.desc()).limit(page_size).offset(page*page_size).statement, con = session.bind)\n", "\n", "df10 = df10.rename(columns={\n", "    \"anon_1\": 'che_liang_id',\n", "    \"anon_2\": 'distant',\n", "    \"anon_3\": 'qty',\n", "    })\n", "\n", "dataList = [] \n", "for row in df10.itertuples(): \n", "    mylist = [row.che_liang_id]\n", "    dataList.extend(mylist)\n", "# aaa = dataList = 194\n", "display(\"df10\", df10)\n", "\n", "\n", "\n", "today = datetime.date.today()\n", "future_day = today.day\n", "future_month = (today.month - 2) % 12\n", "future_year = today.year + ((today.month - 2) // 12)\n", "six_months_ago = datetime.date(future_year, future_month, future_day)\n", "\n", "\n", "df20 = pd.read_sql(sql = session.query(PetrolModel)\\\n", "                        .with_entities(\n", "                            PetrolModel.petrol_id,\n", "                            PetrolModel.datetime,\n", "                            PetrolModel.data_details[0]['che_liang_id'],\n", "                            PetrolModel.data_details[0]['distant'],\n", "                            PetrolModel.data_details[0]['qty'],\n", "                            )\n", "                        # .filter(PetrolModel.data_details[0]['che_liang_id'].astext.cast(Integer) == 194).statement,\n", "                        # .filter(PetrolModel.data_details[0]['che_liang_id'].astext.cast(Integer).in_(dataList)).statement, \n", "                            .filter(\n", "                                and_(\n", "                                    PetrolModel.datetime > six_months_ago,\n", "                                    PetrolModel.data_details[0]['che_liang_id'].astext.cast(Integer).in_(dataList),\n", "                                    )\n", "                                ).statement,\n", "                con = session.bind)\n", "\n", "\n", "df20 = df20.rename(columns={\n", "    \"anon_1\": 'che_liang_id',\n", "    \"anon_2\": 'distant',\n", "    \"anon_3\": 'qty',\n", "    })\n", "\n", "# display(\"df20\", df20)\n", "\n", "\n", "df30 = pd.read_sql(sql = session.query(PetrolModel2)\\\n", "                        .with_entities(\n", "                            # PetrolModel2.auto_id,\n", "                            PetrolModel2.petrol_id,\n", "                            PetrolModel2.datetime,\n", "                            PetrolModel2.data_details[0]['che_liang_id'],\n", "                            PetrolModel2.data_details[0]['distant'],\n", "                            PetrolModel2.data_details[0]['qty'],\n", "                            )\n", "                        # .filter(PetrolModel.data_details[0]['che_liang_id'].astext.cast(Integer) == 194).statement,\n", "                        # .filter(PetrolModel.data_details[0]['che_liang_id'].astext.cast(Integer).in_(dataList)).statement, \n", "                            .filter(\n", "                                and_(\n", "                                    PetrolModel2.datetime > six_months_ago,\n", "                                    PetrolModel2.data_details[0]['che_liang_id'].astext.cast(Integer).in_(dataList),\n", "                                    )\n", "                                ).statement,\n", "                con = session.bind)\n", "\n", "\n", "df30 = df30.rename(columns={\n", "    \"anon_1\": 'che_liang_id',\n", "    \"anon_2\": 'distant',\n", "    \"anon_3\": 'qty',\n", "    })\n", "\n", "# display(\"df30\", df30)\n", "\n", "\n", "df40 = pd.concat([df20, df30], ignore_index=True)\n", "df40 = df40.sort_values(['datetime'], ascending=[True])\n", "# display(df40)\n", "\n", "\n", "df50 = df40[df40['che_liang_id'] ==  20141]\n", "display(df50)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import sqlalchemy\n", "from sqlalchemy import create_engine, tuple_\n", "from sqlalchemy.pool import NullPool\n", "import numpy as np\n", "import json\n", "from sqlalchemy.ext.declarative import declarative_base\n", "from sqlalchemy import Column, Integer, String, Date, DateTime, ForeignKey, TEXT\n", "from sqlalchemy.sql.expression import table, text\n", "from sqlalchemy.dialects.postgresql.json import JSONB\n", "from sqlalchemy.orm import sessionmaker\n", "from sqlalchemy import and_, or_, not_\n", "from sqlalchemy import literal\n", "import datetime\n", "import itertools\n", "\n", "DATABASE_URL = \"*************************************************/n_data\"\n", "engine = create_engine(DATABASE_URL, poolclass=NullPool)\n", "Session = sessionmaker(bind=engine)\n", "\n", "Base = declarative_base()\n", "\n", "class PetrolModel(Base):\n", "    __tablename__ = 'petrol'\n", "    auto_id = Column(Integer, primary_key=True)\n", "    petrol_id = Column(TEXT(convert_unicode=True), server_default=text(\"'petrol'||lpad(NEXTVAL('petrol_sequence'::regclass)::text,8, '0')\"))\n", "    datetime = Column(DateTime)\n", "    data_details = Column(JSONB, nullable=False, server_default='[]')\n", "    \n", "    \n", "class PetrolModel2(Base):\n", "    __tablename__ = 'petrol2'\n", "    auto_id = Column(Integer, primary_key=True)\n", "    petrol_id = Column(TEXT(convert_unicode=True), server_default=text(\"'petrol'||lpad(NEXTVAL('petrol_sequence'::regclass)::text,8, '0')\"))\n", "    datetime = Column(DateTime)\n", "    data_details = Column(JSONB, nullable=False, server_default='[]')\n", "    status_details = Column(JSONB, nullable=False, server_default='{}')    \n", "\n", "\n", "session = Session()\n", "\n", "\n", "\n", "today = datetime.date.today()\n", "future_day = today.day\n", "future_month = (today.month - 12) % 12\n", "future_year = today.year + ((today.month - 12) // 12)\n", "six_months_ago = datetime.date(future_year, future_month, future_day)\n", "\n", "\n", "df20 = pd.read_sql(sql = session.query(PetrolModel)\\\n", "                        .with_entities(\n", "                            PetrolModel.petrol_id,\n", "                            PetrolModel.datetime,\n", "                            PetrolModel.data_details[0]['che_liang_id'],\n", "                            PetrolModel.data_details[0]['distant'],\n", "                            PetrolModel.data_details[0]['qty'],\n", "                            )\n", "                            .filter(PetrolModel.datetime > six_months_ago).statement,\n", "                con = session.bind)\n", "\n", "\n", "df20 = df20.rename(columns={\n", "    \"anon_1\": 'che_liang_id',\n", "    \"anon_2\": 'distant',\n", "    \"anon_3\": 'qty',\n", "    })\n", "\n", "# display(\"df20\", df20)\n", "\n", "\n", "df30 = pd.read_sql(sql = session.query(PetrolModel2)\\\n", "                        .with_entities(\n", "                            PetrolModel2.petrol_id,\n", "                            PetrolModel2.datetime,\n", "                            PetrolModel2.data_details[0]['che_liang_id'],\n", "                            PetrolModel2.data_details[0]['distant'],\n", "                            PetrolModel2.data_details[0]['qty'],\n", "                            )\n", "                            .filter( PetrolModel2.datetime > six_months_ago).statement,\n", "                con = session.bind)\n", "\n", "\n", "df30 = df30.rename(columns={\n", "    \"anon_1\": 'che_liang_id',\n", "    \"anon_2\": 'distant',\n", "    \"anon_3\": 'qty',\n", "    })\n", "\n", "# display(\"df30\", df30)\n", "\n", "\n", "df40 = pd.concat([df20, df30], ignore_index=True)\n", "df40 = df40.sort_values(['datetime'], ascending=[True])\n", "# display(df40)\n", "\n", "\n", "df50 = df40[df40['che_liang_id'] ==  37650 ]\n", "df50['previous_distant'] = df50['distant'].shift()\n", "def fx(x):\n", "    if x['previous_distant'] == 0:\n", "        return x['previous_distant']\n", "    elif x['distant'] == 0:\n", "        return x['distant']\n", "    else:\n", "        return x['distant'] - x['previous_distant']\n", "df50['distance_used'] = df50.apply(lambda x : fx(x),axis=1)\n", "df50['previous_qty'] = df50['qty'].shift()\n", "df50['km_lit'] = (df50['distance_used'] / df50['previous_qty'])           \n", "df50['average'] = df50[df50['km_lit'] != 0][\"km_lit\"].mean()\n", "def fx2(x):\n", "    if x['km_lit'] == 0:\n", "        return x['km_lit']\n", "    else:\n", "        return (x.average - x.km_lit) / x.average * 100\n", "df50['average_percent'] = df50.apply(lambda x : fx2(x),axis=1)\n", "\n", "def remove_outliers(df50, q=0.05):\n", "    upper = df50.quantile(1-q)\n", "    lower = df50.quantile(q)\n", "    mask = (df50 < upper) & (df50 > lower)\n", "    return mask\n", "df50['outliers'] = remove_outliers(df50['km_lit'], 0.1)\n", "\n", "def fx3(x):\n", "    if x['outliers'] == False:\n", "        return None\n", "    else:\n", "        return x['distance_used'] / x['previous_qty']\n", "df50['km_lit_2'] = df50.apply(lambda x : fx3(x),axis=1)\n", "df50['average_2'] = df50[df50['km_lit_2'] != 0][\"km_lit_2\"].mean()\n", "def fx4(x):\n", "    if x['km_lit_2'] == None:\n", "        return x['km_lit_2']\n", "    else:\n", "        return (x.average_2 - x.km_lit_2) / x.average_2 * 100\n", "df50['average_percent_2'] = df50.apply(lambda x : fx4(x),axis=1)\n", "df50['mean'] = (df50.average - df50.average_2) / df50.average * 100\n", "df50 = df50.fillna(0)\n", "display(df50)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import sqlalchemy\n", "from sqlalchemy import create_engine, tuple_\n", "from sqlalchemy.pool import NullPool\n", "import numpy as np\n", "import json\n", "from sqlalchemy.ext.declarative import declarative_base\n", "from sqlalchemy import Column, Integer, String, Date, DateTime, ForeignKey, TEXT\n", "from sqlalchemy.sql.expression import table, text\n", "from sqlalchemy.dialects.postgresql.json import JSONB\n", "from sqlalchemy.orm import sessionmaker\n", "from sqlalchemy import and_, or_, not_\n", "from sqlalchemy import literal\n", "import datetime\n", "import itertools\n", "\n", "DATABASE_URL = \"*************************************************/n_data\"\n", "engine = create_engine(DATABASE_URL, poolclass=NullPool)\n", "Session = sessionmaker(bind=engine)\n", "\n", "Base = declarative_base()\n", "\n", "class PetrolModel(Base):\n", "    __tablename__ = 'petrol'\n", "    auto_id = Column(Integer, primary_key=True)\n", "    petrol_id = Column(TEXT(convert_unicode=True), server_default=text(\"'petrol'||lpad(NEXTVAL('petrol_sequence'::regclass)::text,8, '0')\"))\n", "    datetime = Column(DateTime)\n", "    data_details = Column(JSONB, nullable=False, server_default='[]')\n", "    \n", "    \n", "class PetrolModel2(Base):\n", "    __tablename__ = 'petrol2'\n", "    auto_id = Column(Integer, primary_key=True)\n", "    petrol_id = Column(TEXT(convert_unicode=True), server_default=text(\"'petrol'||lpad(NEXTVAL('petrol_sequence'::regclass)::text,8, '0')\"))\n", "    datetime = Column(DateTime)\n", "    data_details = Column(JSONB, nullable=False, server_default='[]')\n", "    status_details = Column(JSONB, nullable=False, server_default='{}')    \n", "\n", "\n", "session = Session()\n", "\n", "\n", "\n", "today = datetime.date.today()\n", "future_day = today.day\n", "future_month = (today.month - 12) % 12\n", "future_year = today.year + ((today.month - 12) // 12)\n", "six_months_ago = datetime.date(future_year, future_month, future_day)\n", "\n", "\n", "df20 = pd.read_sql(sql = session.query(PetrolModel)\\\n", "                        .with_entities(\n", "                            PetrolModel.petrol_id,\n", "                            PetrolModel.datetime,\n", "                            PetrolModel.data_details[0]['che_liang_id'],\n", "                            PetrolModel.data_details[0]['distant'],\n", "                            PetrolModel.data_details[0]['qty'],\n", "                            )\n", "                            .filter(PetrolModel.datetime > six_months_ago).statement,\n", "                con = session.bind)\n", "\n", "\n", "df20 = df20.rename(columns={\n", "    \"anon_1\": 'che_liang_id',\n", "    \"anon_2\": 'distant',\n", "    \"anon_3\": 'qty',\n", "    })\n", "\n", "# display(\"df20\", df20)\n", "\n", "\n", "df30 = pd.read_sql(sql = session.query(PetrolModel2)\\\n", "                        .with_entities(\n", "                            PetrolModel2.petrol_id,\n", "                            PetrolModel2.datetime,\n", "                            PetrolModel2.data_details[0]['che_liang_id'],\n", "                            PetrolModel2.data_details[0]['distant'],\n", "                            PetrolModel2.data_details[0]['qty'],\n", "                            )\n", "                            .filter( PetrolModel2.datetime > six_months_ago).statement,\n", "                con = session.bind)\n", "\n", "\n", "df30 = df30.rename(columns={\n", "    \"anon_1\": 'che_liang_id',\n", "    \"anon_2\": 'distant',\n", "    \"anon_3\": 'qty',\n", "    })\n", "\n", "# display(\"df30\", df30)\n", "\n", "\n", "df40 = pd.concat([df20, df30], ignore_index=True)\n", "df40 = df40.sort_values(['datetime'], ascending=[True])\n", "# display(df40)\n", "\n", "\n", "df50 = df40[df40['che_liang_id'] ==  20455 ]\n", "df50['previous_distant'] = df50['distant'].shift()\n", "def fx(x):\n", "    if x['previous_distant'] == 0:\n", "        return x['previous_distant']\n", "    elif x['distant'] == 0:\n", "        return x['distant']\n", "    else:\n", "        return x['distant'] - x['previous_distant']\n", "df50['distance_used'] = df50.apply(lambda x : fx(x),axis=1)\n", "\n", "df50['duplicated'] = df50['distance_used'].duplicated(keep=False)\n", "df50['count'] = df50['distance_used']\n", "\n", "df50['previous_qty'] = df50['qty'].shift()\n", "df50['km_lit'] = (df50['distance_used'] / df50['previous_qty'])           \n", "df50['average'] = df50[df50['km_lit'] != 0][\"km_lit\"].mean()\n", "def fx2(x):\n", "    if x['km_lit'] == 0:\n", "        return x['km_lit']\n", "    else:\n", "        return (x.km_lit - x.average) / x.average * 100\n", "df50['average_percent'] = df50.apply(lambda x : fx2(x),axis=1).round(0)\n", "\n", "def remove_outliers(df50, q=0.05):\n", "    upper = df50.quantile(1-q)\n", "    lower = df50.quantile(q)\n", "    mask = (df50 < upper) & (df50 > lower)\n", "    return mask\n", "df50['outliers'] = remove_outliers(df50['km_lit'], 0.1)\n", "\n", "def fx3(x):\n", "    if x['outliers'] == False:\n", "        return None\n", "    else:\n", "        return x['distance_used'] / x['previous_qty']\n", "df50['km_lit_2'] = df50.apply(lambda x : fx3(x),axis=1)\n", "df50['average_2'] = df50[df50['km_lit_2'] != 0][\"km_lit_2\"].mean()\n", "def fx4(x):\n", "    if x['km_lit_2'] == None:\n", "        return x['km_lit_2']\n", "    else:\n", "        return (x.km_lit_2 - x.average_2) / x.average_2 * 100\n", "df50['average_percent_2'] = df50.apply(lambda x : fx4(x),axis=1)\n", "df50['mean'] = ((df50.average_2 - df50.average) / df50.average * 100).round(0)\n", "df50['average_mean'] = df50[\"average_percent\"].astype(str) + ' , ' + df50[\"mean\"].astype(str)\n", "# display(df50)\n", "\n", "\n", "df60 = df50.pivot_table(columns=['distance_used'], aggfunc='size')\n", "df60 = pd.DataFrame(df60)\n", "df60 = df60.rename(columns={0: 'countoo'}).reset_index()\n", "# display(df60)\n", "\n", "\n", "df50['count'] = df50[['count']].replace(to_replace =df60['distance_used'].to_list(), \n", "                        value =df60['countoo'].to_list())\n", "df50['show'] = df50[\"average_mean\"].astype(str) + ' | ' + df50[\"count\"].astype(str)\n", "df50 = df50.fillna(0)\n", "display(df50)\n"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/plain": ["'df20'"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>auto_id</th>\n", "      <th>brand_id</th>\n", "      <th>brand_name</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>5</td>\n", "      <td>NaN</td>\n", "      <td>cbptain</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>3</td>\n", "      <td>10003.0</td>\n", "      <td>captain</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1</td>\n", "      <td>10001.0</td>\n", "      <td>captain</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>10004.0</td>\n", "      <td>beger</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2</td>\n", "      <td>10002.0</td>\n", "      <td>beger</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   auto_id  brand_id brand_name\n", "0        5       NaN    cbptain\n", "1        3   10003.0    captain\n", "2        1   10001.0    captain\n", "3        4   10004.0      beger\n", "4        2   10002.0      beger"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import warnings\n", "warnings.simplefilter('ignore')\n", "\n", "import pandas as pd\n", "import sqlalchemy\n", "from sqlalchemy import create_engine, tuple_\n", "from sqlalchemy.pool import NullPool\n", "import numpy as np\n", "import json\n", "from sqlalchemy.ext.declarative import declarative_base\n", "from sqlalchemy import Column, Integer, String, Date, DateTime, ForeignKey, TEXT, VARCHAR, DECIMAL\n", "from sqlalchemy.sql.expression import table, text\n", "from sqlalchemy.dialects.postgresql.json import JSONB\n", "from sqlalchemy.orm import sessionmaker\n", "from sqlalchemy import and_, or_, not_\n", "from sqlalchemy import extract, cast\n", "from sqlalchemy import literal\n", "from datetime import date, datetime, timedelta\n", "import datetime\n", "import itertools\n", "from sklearn.ensemble import IsolationForest\n", "from sqlalchemy.orm.attributes import flag_modified\n", "import requests\n", "from functools import reduce\n", "\n", "\n", "DATABASE_URL = \"*************************************************/test_nern\"\n", "engine = create_engine(DATABASE_URL, poolclass=NullPool)\n", "# engine = create_engine(DATABASE_URL)\n", "Session = sessionmaker(bind=engine)\n", "\n", "Base = declarative_base()\n", "\n", "class color_devices(Base):\n", "    __tablename__ = 'color_devices'\n", "    auto_id = Column(Integer, primary_key=True)\n", "    brand_id = Column(Integer)\n", "    brand_name = Column(VARCHAR)\n", "    color_type_unit = Column(Integer)\n", "    w1 = Column(DECIMAL)        \n", "    w2 = Column(DECIMAL)     \n", "    volume = Column(DECIMAL)   \n", "\n", "\n", "session = Session()\n", "\n", "\n", "\n", "df20 = pd.read_sql(sql = session.query(color_devices).order_by(color_devices.brand_name, color_devices.brand_name.desc())\n", "                            .with_entities(\n", "                                    color_devices.auto_id,\n", "                                    color_devices.brand_id,\n", "                                    color_devices.brand_name,\n", "                                    )\n", "                            .statement, con = session.bind)\n", "display('df20', df20)\n", "\n", "session.close()\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"interpreter": {"hash": "916dbcbb3f70747c44a77c7bcd40155683ae19c65e1c03b4aa3499c5328201f1"}, "kernelspec": {"display_name": "Python 3.8.10 64-bit", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}