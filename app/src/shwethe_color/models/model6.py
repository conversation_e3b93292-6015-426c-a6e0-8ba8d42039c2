from typing import Text
from unicodedata import decimal
import uuid
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy import Column, Integer, String, Date, DateTime, ForeignKey, TEXT, VARCHAR, Float, DECIMAL
from sqlalchemy import String, MetaData, create_engine, DDL, Sequence, TEXT
from sqlalchemy.sql.expression import table, text
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql.json import JSONB
from sqlalchemy.sql.sqltypes import ARRAY, VARCHAR, Unicode
from src.Connect.postgresql_connect import DATABASE_URL_test_nern
import datetime
from sqlalchemy.ext.hybrid import hybrid_property, hybrid_method

Base = declarative_base()


class color_devices_list(Base):
    __tablename__ = 'color_devices_list'
    auto_id = Column(Integer, primary_key=True)
    devices_code = Column(VARCHAR)
    fen_dian_id = Column(Integer)
    color_devices_id = Column(Integer)   


class color_devices(Base):
    __tablename__ = 'color_devices'
    auto_id = Column(Integer, primary_key=True)
    brand_id = Column(Integer)
    brand_name = Column(VARCHAR)
    color_type_unit = Column(Integer)
    w1 = Column(DECIMAL)        
    w2 = Column(DECIMAL)     
    volume = Column(DECIMAL)        
   


class color_devices_sub_components(Base):
    __tablename__ = 'color_devices_sub_components'
    auto_id = Column(Integer, primary_key=True)
    color_devices_id = Column(Integer)
    color_id = Column(Integer)
    price = Column(Float)
    
    
class color_components(Base):
    __tablename__ = 'color_components'
    auto_id = Column(Integer, primary_key=True)
    color_devices_id = Column(Integer)
    product_id_a = Column(Integer)
    product_id_b = Column(Integer)
    fen_dian_id = Column(Integer)
    datetime = Column(DateTime)
    

class color_sub_components(Base):
    __tablename__ = 'color_sub_components'
    auto_id = Column(Integer, primary_key=True)
    color_components_id = Column(Integer)
    color_id = Column(Integer)
    unit_use = Column(DECIMAL)        




engine = create_engine(DATABASE_URL_test_nern)


# fff = """ DROP SEQUENCE if exists petrol_sequence; """
# engine.execute(fff)


# aaa = """ CREATE SEQUENCE petrol_sequence
#    INCREMENT 1
#    START 1
#    MINVALUE 1; """
# engine.execute(aaa)


# bbb = """ CREATE TABLE a2 (
#     auto_id serial PRIMARY KEY,
#     petrol_id   TEXT DEFAULT 'petrol'||lpad(NEXTVAL('petrol_sequence'::regclass)::text,8, '0' ),
#     datetime timestamp NOT NULL,
#     data_detail jsonb
# ) """
# engine.execute(bbb)


# aaa = """ CREATE TABLE if not exists petrol (
#     id INTEGER NOT NULL,
#     name VARCHAR(10),
#     PRIMARY KEY (id),
# ) """
# engine.execute(aaa)


# ADD COLUMN
# engine.execute('alter table color_components add column datetime timestamp')
# CHANGE COLUMN TYPE
# engine.execute('ALTER TABLE color_components ALTER COLUMN product_id_a TYPE Integer USING (product_id_a::Integer);')
# CHANGE COLUMN TYPE
# engine.execute('ALTER TABLE petrol ALTER COLUMN petrol_id TYPE varchar')
# RENAME COLUMN
# engine.execute('ALTER TABLE color_devices_sub_components RENAME COLUMN prices TO price')
# engine.execute('alter table books drop info')
# engine.execute('ALTER TABLE books ADD CONSTRAINT ids UNIQUE(ids)')
# Drop COLUMN
# engine.execute("ALTER SEQUENCE a1 drop a2")
# engine.execute("ALTER table petrol drop column status")


# Base.metadata.bind = create_engine(DATABASE_URL_test_nern)
# Base.metadata.create_all()
# drop_all
# create_all

# -------------------------------------------------------------------

# from sqlalchemy import String, MetaData, create_engine
# from migrate.versioning.schema import Table, Column

# db_engine =  create_engine(DATABASE_URL_test_nern)
# db_meta = MetaData(bind=db_engine)


# table = Table('petrol' , db_meta)
# col = Column('vvv', String(20), default='foo')
# col.create(table)
