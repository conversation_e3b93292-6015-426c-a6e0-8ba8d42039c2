from typing import Text
import uuid
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy import Column, Float, Integer, String, Date, DateTime, ForeignKey, TEXT
from sqlalchemy import String, MetaData, create_engine, DDL, Sequence, TEXT
from sqlalchemy.sql.expression import table, text
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql.json import JSONB
from sqlalchemy.sql.sqltypes import ARRAY, VARCHAR, Unicode
from src.Connect.postgresql_connect import DATABASE_URL
import datetime
from sqlalchemy.ext.hybrid import hybrid_property, hybrid_method

Base = declarative_base()

# USER_ID_SEQ = Sequence('user_id_seq')


class PetrolModel(Base):
    __tablename__ = 'petrol'
    auto_id = Column(Integer, primary_key=True)
    # petrol_id = Column(TEXT(convert_unicode=True), USER_ID_SEQ, server_default=text("'petrol'||lpad(NEXTVAL('user_id_seq'::regclass)::text,8, '0')"))
    petrol_id = Column(TEXT(convert_unicode=True), server_default=text("'petrol'||lpad(NEXTVAL('petrol_sequence'::regclass)::text,8, '0')"))
    datetime = Column(DateTime)
    data_details = Column(JSONB, nullable=False, server_default='[]')
    status_details = Column(JSONB, nullable=False, server_default='{}')
    update_time = Column(DateTime)
    datetime_bill = Column(DateTime)


class PetrolModel2(Base):
    __tablename__ = 'petrol2'
    auto_id = Column(Integer, primary_key=True)
    # petrol_id = Column(TEXT(convert_unicode=True), USER_ID_SEQ, server_default=text("'petrol'||lpad(NEXTVAL('user_id_seq'::regclass)::text,8, '0')"))
    petrol_id = Column(TEXT(convert_unicode=True), server_default=text("'petrol'||lpad(NEXTVAL('petrol_sequence'::regclass)::text,8, '0')"))
    datetime = Column(DateTime)
    data_details = Column(JSONB, nullable=False, server_default='[]')
    status_details = Column(JSONB, nullable=False, server_default='{}')

class TypeCar(Base):
    __tablename__ = 'car_type'
    auto_id = Column(Integer, primary_key=True)
    jia_yi_id = Column(Integer)
    data_sub = Column(JSONB, nullable=False, server_default='[]')


class TypeLei(Base):
    __tablename__ = 'lei_type'
    auto_id = Column(Integer, primary_key=True)
    lei_a = Column(Integer)
    lei_b = Column(Integer)
    lei_name = Column(String)
    data_sub = Column(JSONB, nullable=False, server_default='{}')
    
class TypeBi_zhi(Base):
    __tablename__ = 'bi_zhi'
    auto_id = Column(Integer, primary_key=True)
    bi_zhi = Column(Integer)
    idname = Column(String)   

 

class Calculate(Base):
    __tablename__ = 'petrol_calculate'
    auto_id = Column(Integer, primary_key=True)
    petrol_id = Column(TEXT)
    calOne = Column(JSONB, nullable=False, server_default='{}')
    calTwo = Column(JSONB, nullable=False, server_default='{}')


class Calculate2(Base):
    __tablename__ = 'petrol_calculate_2'
    auto_id = Column(Integer, primary_key=True)
    datetime = Column(DateTime)
    oneMonth = Column(JSONB, nullable=False, server_default='{}')
    
# class IpPrinter(Base):
#     __tablename__ = 'ip_printer'
#     auto_id = Column(Integer, primary_key=True)
#     name = Column(TEXT)
#     fen_dian_id = Column(Integer)


engine = create_engine(DATABASE_URL)


# fff = """ DROP SEQUENCE if exists petrol_sequence; """
# engine.execute(fff)


# aaa = """ CREATE SEQUENCE petrol_sequence
#    INCREMENT 1
#    START 1
#    MINVALUE 1; """
# engine.execute(aaa)


# bbb = """ CREATE TABLE a2 (
#     auto_id serial PRIMARY KEY,
#     petrol_id   TEXT DEFAULT 'petrol'||lpad(NEXTVAL('petrol_sequence'::regclass)::text,8, '0' ),
#     datetime timestamp NOT NULL,
#     data_detail jsonb
# ) """
# engine.execute(bbb)


# aaa = """ CREATE TABLE if not exists petrol (
#     id INTEGER NOT NULL,
#     name VARCHAR(10),
#     PRIMARY KEY (id),
# ) """
# engine.execute(aaa)


# ADD COLUMN
# engine.execute('alter table ip_printer add column fen_dian_id Integer')
# CHANGE COLUMN TYPE
# engine.execute('ALTER TABLE books ALTER COLUMN ids TYPE Integer USING (ids::Integer);')
# CHANGE COLUMN TYPE
# engine.execute('ALTER TABLE petrol ALTER COLUMN petrol_id TYPE varchar')
# RENAME COLUMN
# engine.execute('ALTER TABLE car_type RENAME COLUMN jia_yi_fang_id TO jia_yi_id')
# engine.execute('alter table books drop info')
# engine.execute('ALTER TABLE books ADD CONSTRAINT ids UNIQUE(ids)')
# Drop COLUMN
# engine.execute("ALTER SEQUENCE a1 drop a2")
# engine.execute("ALTER table petrol drop column status")


# Base.metadata.bind = create_engine(DATABASE_URL)
# Base.metadata.create_all()
# drop_all
# create_all

# -------------------------------------------------------------------

# from sqlalchemy import String, MetaData, create_engine
# from migrate.versioning.schema import Table, Column

# db_engine =  create_engine(DATABASE_URL)
# db_meta = MetaData(bind=db_engine)


# table = Table('petrol' , db_meta)
# col = Column('vvv', String(20), default='foo')
# col.create(table)
