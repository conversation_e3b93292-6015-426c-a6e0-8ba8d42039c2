from fastapi import FastAPI, APIRouter, Body, Response, BackgroundTasks, Header
from pydantic import BaseModel
from sqlalchemy.orm.attributes import flag_modified
from helper import generate_id, generate_datetime, generate_datetime_id, generate_datetime_selie, get_bookszxzx
from typing import List, Optional
import json
import requests
import pandas as pd
import time


from sqlalchemy import create_engine, tuple_
from sqlalchemy.orm import sessionmaker
from fastapi.responses import JSONResponse
from datetime import date, datetime
from src.Connect.postgresql_connect import DATABASE_URL
from src.shwethe_petrol.models.model import PetrolModel, TypeCar
from src.shwethe_petrol.schema.schema import Put, Post


router = APIRouter()


engine = create_engine(DATABASE_URL)
Session = sessionmaker(bind=engine)


# @router.get("/books_01")
# async def get_books_01():
#     df = pd.read_sql_table(
#         'books', DATABASE_URL, columns=["info", "id"])
#     df = df.to_dict('records')
#     df = pd.json_normalize(df)
#     df = df.to_json(orient='records')
#     return json.loads(df)


@router.get("/get2")
async def get2():
    df = pd.read_sql_table('car_type', engine)
    df = df.loc[df['data_sub'] == dict(type_name='partner_id')]
    df = df.to_dict('records')
    df = pd.json_normalize(df)
    df = df.to_json(orient='records')
    return json.loads(df)
# ---------------------------------------------------------


@router.post("/post")
async def post(post: Post):
    session = Session()
    a, b, c = await get_bookszxzx()
    db_user = PetrolModel(
        datetime=c,
        # data_details=post.data_details,
        data_details=[
            dict(sub_id=b, r_id=a, u_id=post.u_id, store=post.store, car=post.car, petrol=post.petrol, qty=post.qty, price=post.price, distant=post.distant)]
    )
    session.add(db_user)
    session.commit()
    session.refresh(db_user)
    session.close()
    return db_user


@router.post("/post2")
async def post2(petrol_id: str, post: Post):
    session = Session()
    user = session.query(PetrolModel).filter(
        PetrolModel.petrol_id == petrol_id).first()
    for var in user.data_details:
        print(var)
        var


    return var


@router.get("/get")
async def get():
    # df = pd.read_sql_table('petrol', engine)
    # df = df.to_dict('records')
    # return df
    df = pd.read_sql_table('petrol', engine)
    df = df.to_dict('records')
    df = pd.json_normalize(df, 'data_details', ['petrol_id'])
    df = df.to_json(orient='records')
    return json.loads(df)


@router.delete("/delete/{petrol_id}")
async def delete(petrol_id: str):
    session = Session()
    myquery = session.query(PetrolModel).filter(
        PetrolModel.petrol_id == petrol_id).first()
    session.delete(myquery)
    session.commit()
    session.close()
    return myquery


@router.put("/put/{petrol_id}/{r_id}", response_model=Put)
async def put(petrol_id: str, r_id: str, put: Put):
    session = Session()
    # user = session.query(PetrolModel).filter(PetrolModel.petrol_id == petrol_id).first()
    # print(user.data_details)
    # user.data_details.update(dict(name=put.name))
    # flag_modified(user, 'data_details')
    # session.commit()

    user = session.query(PetrolModel).filter(
        PetrolModel.petrol_id == petrol_id).first()
    for var in user.data_details:
        print(var['r_id'])
        if var['r_id'] == r_id:
            var.update(dict(ke_bian=put.ke_bian, bi_zhi=put.bi_zhi))
    flag_modified(user, 'data_details')
    session.commit()

    return JSONResponse(status_code=200, content={
        "status_code": 200,
        "message": "success"
    })


# @router.get("/product_search_price", status_code = 200)
# def product_search(idname : str):

#     import src.Connect.postgresql_connect as psycopg2_conn_insert_data_s
#     import importlib
#     importlib.reload(psycopg2_conn_insert_data_s)
#     from src.Connect.postgresql_connect import psycopg2_conn_insert_data_s
#     import pandas as pd
#     sql = """  select id,idname,d_name,mm_name,price from product_name where idname = '%s'  """ % idname
#     AS100001 = pd.read_sql(sql,psycopg2_conn_insert_data_s)

#     AS100002 = AS100001.rename(columns={'price': 'product_price','id': 'product_id','idname': 'product_idname','d_name': 'product_d_name','mm_name': 'product_mm_name'})

#     AS100003 = AS100002.to_json(orient='records')

#     psycopg2_conn_insert_data_s.close()

#     return json.loads(AS100003)


# @router.get("/jia_yi_search", status_code = 200)
# def product_search(idname : str):

#     import src.Connect.postgresql_connect as psycopg2_conn_insert_data_s
#     import importlib
#     importlib.reload(psycopg2_conn_insert_data_s)
#     from src.Connect.postgresql_connect import psycopg2_conn_insert_data_s
#     import pandas as pd
#     sql = """  select id,idname,mm_name from jia_yi_name where idname = '%s'  """ % idname
#     AS100001 = pd.read_sql(sql,psycopg2_conn_insert_data_s)

#     AS100002 = AS100001.rename(columns={'id': 'jia_yi_id','idname': 'jia_yi_idname','mm_name': 'jia_yi_mmname'})

#     AS100003 = AS100002.to_json(orient='records')

#     psycopg2_conn_insert_data_s.close()

#     return json.loads(AS100003)


# @router.get("/jia_yi_search_pos", status_code = 200)
# def product_search(idname : str):

#     import src.Connect.postgresql_connect as psycopg2_conn_insert_data_s
#     import importlib
#     importlib.reload(psycopg2_conn_insert_data_s)
#     from src.Connect.postgresql_connect import psycopg2_conn_insert_data_s
#     import pandas as pd
#     sql = """  select id,idname,mm_name from jia_yi_name where idname = '%s'  """ % idname
#     AS100001 = pd.read_sql(sql,psycopg2_conn_insert_data_s)

#     # AS100002 = AS100001.rename(columns={'id': 'jia_yi_id','idname': 'jia_yi_idname','mm_name': 'jia_yi_mmname'})

#     AS100003 = AS100001.to_json(orient='records')

#     psycopg2_conn_insert_data_s.close()

#     return json.loads(AS100003)


# @router.get("/jia_yi_search_pos", status_code = 200)
# def product_search(idname : str):

#     import src.Connect.postgresql_connect as psycopg2_conn_insert_data_s
#     import importlib
#     importlib.reload(psycopg2_conn_insert_data_s)
#     from src.Connect.postgresql_connect import psycopg2_conn_insert_data_s
#     import pandas as pd
#     sql = """  select id,idname,mm_name from jia_yi_name where idname = '%s'  """ % idname
#     AS100001 = pd.read_sql(sql,psycopg2_conn_insert_data_s)

#     # AS100002 = AS100001.rename(columns={'id': 'jia_yi_id','idname': 'jia_yi_idname','mm_name': 'jia_yi_mmname'})

#     AS100003 = AS100001.to_json(orient='records')

#     psycopg2_conn_insert_data_s.close()

#     return json.loads(AS100003)


# @router.get("/test", status_code = 200)
# def product_search():

#     import src.Connect.postgresql_connect as psycopg2_conn_insert_data_s
#     import importlib
#     importlib.reload(psycopg2_conn_insert_data_s)
#     from src.Connect.postgresql_connect import psycopg2_conn_insert_data_s
#     import pandas as pd
#     sql = """  select id,info,ids,created_at,data_sub from books   """
#     df = pd.read_sql(sql,psycopg2_conn_insert_data_s)

#     df_merge_co2 = df.to_dict('records')

#     df_merge_co3 = pd.json_normalize(df_merge_co2)
#     # print(df)
#     print(df_merge_co3)
#     print(df_merge_co3.info())
#     df_merge_co4 = df_merge_co3.to_json(orient='records')
#     print(df_merge_co4)
#     return json.loads(df_merge_co4)

#     psycopg2_conn_insert_data_s.close()

#     return json.loads(AS100003)
