import numpy as np
from fastapi import FastAPI, APIRouter, Body, Response, BackgroundTasks, Header
from pydantic import BaseModel
from sqlalchemy.orm.attributes import flag_modified
from helper import generate_id, generate_datetime, generate_datetime_id, generate_datetime_selie, get_bookszxzx
from typing import List, Optional
import json
import requests
import pandas as pd



from sqlalchemy import create_engine, tuple_
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import NullPool
from fastapi.responses import JSONResponse
from datetime import date, datetime
import datetime
from src.Connect.postgresql_connect import DATABASE_URL
from src.Connect.https_connect import Arter_api, shwethe_mysql_api
from src.shwethe_petrol.models.model5 import PetrolModel, TypeCar, TypeLei, PetrolModel2
from src.shwethe_petrol.schema.schema import Put, Put_distant, formSubmit, addStore, addCar, confirm_status, PutCarMotor, PutMasakBill
from functools import reduce
from fastapi.responses import J<PERSON>NResponse
from fastapi.encoders import jsonable_encoder
from sqlalchemy import and_, or_, not_
from sqlalchemy import Column, Integer, String, Date, DateTime, ForeignKey, TEXT, FLOAT
from sqlalchemy.ext.declarative import declarative_base
import schedule
import time


router = APIRouter()


# engine = create_engine(DATABASE_URL_test_nern, poolclass=NullPool)
engine = create_engine(DATABASE_URL)
Session = sessionmaker(bind=engine)





import logging
import time
from fastapi import FastAPI
from fastapi_utils.tasks import repeat_every

logger = logging.getLogger(__name__)
# app = FastAPI()
router = APIRouter()
counter = 0


# async def mydatabase():
#     session = Session()
#     a, b, c = await get_bookszxzx()
#     db_user = PetrolModel(
#         datetime=c
#     )
#     session.add(db_user)
#     session.commit()
#     session.refresh(db_user)
#     session.close()
#     return db_user


# --------------------------------------------------

async def update_jia_yi_fang_a():
    session = Session()

    import datetime as DT
    today = DT.date.today()
    week_ago = today - DT.timedelta(days=3)
    print(week_ago)

    df20 = pd.read_sql(sql = session.query(PetrolModel) 
                           .filter(PetrolModel.datetime > week_ago)
                           .filter(PetrolModel.status_details['status'].astext.cast(String) == "success")
                           .statement, con = session.bind)
    df20 = df20.to_dict('records')
    json_nor = pd.json_normalize(df20, "data_details", ["auto_id", "petrol_id", "datetime"])
    json_nor = json_nor.to_dict('records')

    filtered_data = []
    for d in json_nor:
        if d["jia_yi_fang_a"] == 0:
           print(d)
           print(d['jia_yi_fang_a'])
           user = session.query(PetrolModel).filter(PetrolModel.petrol_id == d['petrol_id']).first()
           for var in user.data_details:
                print(var['r_id'])
                if var['r_id'] == d['r_id']:
                    var.update(dict(jia_yi_fang_a=d['warehouse_id']))
           flag_modified(user, 'data_details')
           session.commit()
           res = "update success"
        else:
            # print("jia_yi_fang_a not equal to 0")
            res = "no update"
    
    return res


async def insert_arterDatabase():
    session = Session()
    
    import datetime as DT
    today = DT.date.today()
    week_ago = today - DT.timedelta(days=15)
    print(week_ago)
    
    # today = datetime.date.today()
    # future_day = today.day -1
    # future_month = (today.month) % 12
    # future_year = today.year + ((today.month) // 12)
    # six_months_ago = datetime.date(future_year, future_month, future_day)
    # print(six_months_ago)
    # print(today, future_day, future_month, future_year)
    
    df20 = pd.read_sql(sql = session.query(PetrolModel) 
                           .filter(PetrolModel.datetime > week_ago)
                           .filter(PetrolModel.status_details['status'].astext.cast(String) == "success")
                           .statement, con = session.bind)
    df20 = df20.to_dict('records')
    json_nor = pd.json_normalize(df20, "data_details", ["auto_id", "petrol_id", "datetime"])
    json_nor = json_nor.to_dict('records')
    
    # INSERT TO ARTER DATABASE
    aaa = []
    for ioo in json_nor:
        d = {
            'a_id': ioo['auto_id'],
            'b_id': ioo['sub_id'],
            'datetime': str(ioo['datetime']),
            'jin_huo_dang': ioo['petrol_id'],
            'uid': ioo['user_id'],
            'product_id': ioo['product_id'],
            'product_qty': ioo['qty'],
            'product_price_x': ioo['price'],
            'product_price_y': ioo['price'],
            'jia_yi_fang_a': ioo['jia_yi_fang_a'],
            'jia_yi_fang_b': ioo['jia_yi_fang_b'],
            'lei_a': ioo['lei_a'],
            'lei_b': ioo['lei_b'],
            'line': 1005,
            'ke_bian': ioo['ke_bian'],
            'che_liang': ioo['che_liang_id'],
            'kind': 10003,
            'bi_zhi': ioo['bi_zhi']
            }
        aaa.append(d)
    df1 = pd.DataFrame(aaa) 
    mata = {
    'data' : df1.to_dict(orient='records')
    }
        
    dd = requests.post(shwethe_mysql_api + '/api/v2/table/mysql', data= json.dumps(mata))
    print(counter, dd)

    return dd.status_code


@router.on_event("startup")
@repeat_every(seconds=60 * 10, logger=logger, wait_first=True)
async def periodic():
    global counter
    counter += 1
    
    
    cdf = await update_jia_yi_fang_a()
    abc = await insert_arterDatabase()
    
    return cdf, abc


# --------------------------------------------------

# async def aaa():
#     print("dfcsdcsdc")
#     return 'dd.status_code'


# @router.on_event("startup")
# @repeat_every(seconds=10, logger=logger, wait_first=True)
# async def periodic200():
#     global counter
#     counter += 1

#     print(counter)
    
#     abc = await aaa()
    
#     return abc