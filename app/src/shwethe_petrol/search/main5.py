import numpy as np
from fastapi import FastAPI, APIRouter, Body, Response, BackgroundTasks, Header, Depends
from pydantic import BaseModel
from sqlalchemy.orm.attributes import flag_modified
from helper import generate_id, generate_datetime, generate_datetime_id, generate_datetime_selie, get_bookszxzx
from typing import List, Optional, Union
import json
import requests
import pandas as pd

import warnings
warnings.simplefilter('ignore')

from sqlalchemy import create_engine, tuple_
from sqlalchemy.orm import Session, sessionmaker
from sqlalchemy.pool import NullPool
from fastapi.responses import JSONResponse
from datetime import date, datetime, timedelta
import datetime
from src.Connect.postgresql_connect import DATABASE_URL, DATABASE_URL_test_nern
from src.Connect.https_connect import Arter_api, shwethe_mysql_api, pv_api
from src.shwethe_petrol.models.model5 import PetrolModel, TypeCar, TypeLei, PetrolModel2, Calculate
from src.shwethe_petrol.schema.schema import Put, Put_distant, check_km, formSubmit, addStore, addCar, addDriver, confirm_status, PutCarMotor, PutMasakBill
from functools import reduce
from fastapi.responses import JSONResponse
from fastapi.encoders import jsonable_encoder
from sqlalchemy import and_, or_, not_
from sqlalchemy import extract, cast
from sqlalchemy import Column, Integer, String, Date, DateTime, ForeignKey, TEXT, FLOAT
from sqlalchemy.ext.declarative import declarative_base
from sklearn.ensemble import IsolationForest
from sqlalchemy import func  # Add this import statement

router = APIRouter()


# engine = create_engine(DATABASE_URL_test_nern, poolclass=NullPool)
engine = create_engine(DATABASE_URL)
# Session = sessionmaker(bind=engine)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()
        

@router.get("/getIndayItem/{che_liang_id}")
async def getIndayItem(che_liang_id: int, session: Session = Depends(get_db)):
    # session = Session()
    try:
        df20 = pd.read_sql(sql = session.query(PetrolModel)
                            .filter(PetrolModel.data_details[0]['che_liang_id'].astext.cast(Integer) == che_liang_id)
                            # .limit(10)
                            # .offset(10)
                            .order_by(PetrolModel.datetime.desc())
                            .limit(15)
                            .with_entities(
                                    PetrolModel.data_details[0]['fen_dian_id'],
                                    PetrolModel.datetime,
                                    PetrolModel.datetime_bill,
                                    PetrolModel.petrol_id,
                                    PetrolModel.data_details[0]['che_liang_id'],
                                    PetrolModel.data_details[0]['distant'],
                                    PetrolModel.data_details[0]['bill'],
                                    PetrolModel.status_details['status'],
                                    PetrolModel.data_details[0]['imageKm'],
                                    PetrolModel.data_details[0]['imageAccept'],
                                    PetrolModel.data_details[0]['imageKmDate'],
                                    PetrolModel.data_details[0]['imageKm2'],
                                    PetrolModel.data_details[0]['imageBill'],
                                    PetrolModel.data_details[0]['qty']
                                    )
                            .statement, con = session.bind)
        df20 = df20.rename(columns=dict(anon_1='fen_dian_id', anon_2='che_liang_id', 
                                        anon_3='distant', anon_4='bill', anon_5='status', 
                                        anon_6='imageKm', anon_7='imageAccept', anon_8='imageKmDate',
                                        anon_9='imageKm2', anon_10='imageBill', anon_11='qty'))
        # display('df20', df20)
        
        df40 = df20
        df40 = df40.loc[:, df40.columns.isin(['che_liang_id', 'petrol_id', 'product_id'])]
        df40 = df40.rename(columns={"che_liang_id": 'jia_yi_id'})
        # # display('df40', df40)

        url = Arter_api + 'jia_yi_name_list_id'
        to_dict = df40.to_dict('records')
        body_raw = {"data_api": to_dict}
        df50 = requests.get(url=url, json=body_raw).json()
        df50 = pd.DataFrame(df50)
        # # display('df50', df50)
        
        mergeA = df20.merge(df50, left_on='che_liang_id', right_on='jia_yi_id', how='left')
        merge = mergeA.fillna("").to_dict("records")
        # display('merge', merge)
    except:
        merge = []
           
    # session.close()
    return merge


@router.get("/summaryAll")
async def summaryAll(session: Session = Depends(get_db)):
    # session = Session()
    
    df20 = pd.read_sql(sql = session.query(PetrolModel)\
                        .with_entities(
                            PetrolModel.datetime,
                            PetrolModel.data_details[0]['che_liang_id'],
                            PetrolModel.data_details[0]['distant'],
                            PetrolModel.data_details[0]['qty'],
                            PetrolModel.data_details[0]['bill'],
                            )
                            # .filter(PetrolModel.datetime > six_months_ago)
                            .statement,
                con = session.bind)
    df20 = df20.rename(columns={
        "anon_1": 'che_liang_id',
        "anon_2": 'distant',
        "anon_3": 'qty',
        "anon_4": 'bill',
        })
    # display("df20", df20)

    df30 = pd.read_sql(sql = session.query(PetrolModel2)\
                            .with_entities(
                                PetrolModel2.datetime,
                                PetrolModel2.data_details[0]['che_liang_id'],
                                PetrolModel2.data_details[0]['distant'],
                                PetrolModel2.data_details[0]['qty'],
                                )
                                # .filter( PetrolModel2.datetime > six_months_ago)
                                .statement,
                    con = session.bind)
    df30 = df30.rename(columns={
        "anon_1": 'che_liang_id',
        "anon_2": 'distant',
        "anon_3": 'qty',
        })

    # display("df30", df30)


    df40 = pd.concat([df20, df30], ignore_index=True)
    df40 = df40.sort_values(['che_liang_id', 'datetime'], ascending=[True, True])
    # display(df40)

    # df50 = df40[df40['che_liang_id'] ==  25371 ]
    # return che_liang_id 104 and 25371
    # df50 = df40[df40['che_liang_id'].isin([25371, 13417, 26594])]
    df50 = df40
    
    df50['previous_distant'] = df50.groupby('che_liang_id')['distant'].transform(lambda x : x.shift())
    df50['distance_used'] = df50.groupby('che_liang_id').apply(lambda x: x['previous_distant'] if x['previous_distant'].any() == 0 else (x['distant'] if x['distant'].any() == 0 else x['distant'] - x['previous_distant'])).reset_index('che_liang_id',drop=True)
    df50['mean_distance_used'] = df50.groupby('che_liang_id')['distance_used'].transform(lambda x : x.mean())
    df50['previous_qty'] = df50.groupby('che_liang_id')['qty'].transform(lambda x : x.shift())    
    df50['km_lit'] = df50.groupby('che_liang_id').apply(lambda x : x['distance_used'] / x['previous_qty']  ).reset_index('che_liang_id',drop=True)  
    df50['average'] = df50.groupby('che_liang_id')['km_lit'].transform(lambda x : x.mean()) 
    df50['average_percent'] = df50.groupby('che_liang_id').apply(lambda x: x['km_lit'] if x['km_lit'].any() == 0 else ((x.distance_used - x.mean_distance_used) / x.mean_distance_used) * 100).reset_index('che_liang_id',drop=True) 

    def train_isolation_group(pass_distance_used):
        outlier_detector = IsolationForest(random_state=42)
        np_scaled = pass_distance_used.values.reshape(-1, 1)
        distance_used_fillna = pd.DataFrame(np_scaled).fillna(0)
        outlier_detector.fit(distance_used_fillna)
        prediction = outlier_detector.predict(distance_used_fillna)
        prediction_strings = ['Outlier' if x < 0 else 'Standard' for x in prediction]
        return prediction_strings
    df50['outlier_flag'] = df50.groupby('che_liang_id')['distance_used'].transform(train_isolation_group)

    df50['distance_used_2'] = df50.apply(lambda x: None if x['outlier_flag'] == 'Outlier' else x['distance_used'], axis=1)
    df50['mean_distance_used_2'] = df50.groupby('che_liang_id')['distance_used_2'].transform(lambda x : x.mean()) 
    df50['km_lit_2'] = df50.apply(lambda x: None if x['outlier_flag'] == 'Outlier' else x['distance_used'] / x['previous_qty'], axis=1)
    df50['average_2'] = df50.groupby('che_liang_id')['km_lit_2'].transform(lambda x : x.mean())
    df50['average_percent_2'] = df50.groupby('che_liang_id').apply(lambda x: x['km_lit_2'] if x['km_lit_2'].any() == None else ((x.distance_used - x.mean_distance_used_2) / x.mean_distance_used_2) * 100).reset_index('che_liang_id',drop=True) 
    df50['mean'] = df50.groupby('che_liang_id').apply(lambda x: (x.mean_distance_used - x.mean_distance_used_2) / x.mean_distance_used_2 * 100).reset_index('che_liang_id',drop=True) 
    df50['count'] = 'calculate'

    def sumfunc_value(x):
        qty = x['qty'].mean()
        distance_used = x['distance_used'].mean()
        km_lit = x['km_lit'].mean()
        distance_used_2 = x['distance_used_2'].mean()
        km_lit_2 = x['km_lit_2'].mean()
        mean = (x['distance_used_2'].mean() - x['distance_used'].mean()) / x['distance_used'].mean() * 100
        minute = 60 * x['km_lit_2'].mean()  
        oneKmLit =  x['distance_used_2'].mean() if x['distance_used_2'].any() == 0  else x['qty'].mean() / x['distance_used_2'].mean()
        hundredKmLit = x['km_lit_2'].mean() if x['km_lit_2'].any() == 0  else 100 / x['km_lit_2'].mean()
        count = x['che_liang_id'].count()
        
        return pd.Series([qty, distance_used, km_lit, distance_used_2, km_lit_2, mean, minute, oneKmLit, hundredKmLit, count], 
                         index=['qty', 'distance_used', 'km_lit', 'distance_used_2', 'km_lit_2', 'mean', 'minute', 'oneKmLit', 'hundredKmLit', 'count'])
    df60 = df50.groupby(['che_liang_id', 'bill', pd.Grouper(key='datetime', freq='MS')]).apply(sumfunc_value).reset_index()
    
    def train_isolation_group(pass_distance_used):
        outlier_detector = IsolationForest(random_state=42)
        np_scaled = pass_distance_used.values.reshape(-1, 1)
        distance_used_fillna = pd.DataFrame(np_scaled).fillna(0)
        outlier_detector.fit(distance_used_fillna)
        prediction = outlier_detector.predict(distance_used_fillna)
        prediction_strings = ['Outlier' if x < 0 else 'Standard' for x in prediction]
        return prediction_strings
    df60['outlier_flag'] = df60.groupby('che_liang_id')['count'].transform(train_isolation_group)
    df60['outlier_flag_dis2'] = df60.apply(lambda x: None if x['outlier_flag'] == 'Outlier' else x['distance_used_2'], axis=1)
    df60['mean_distance_used_2'] = df60.groupby('che_liang_id')['outlier_flag_dis2'].transform(lambda x : x.mean())
    df60['average_2'] = df60.groupby('che_liang_id').apply(lambda x: (x.distance_used_2 - x.mean_distance_used_2) / x.mean_distance_used_2 * 100).reset_index('che_liang_id',drop=True) 
    
    
    df70 = df60
    df70 = df70.loc[:, df70.columns.isin(['che_liang_id'])]
    df70 = df70.rename(columns={"che_liang_id": 'jia_yi_id'})
    
    url = Arter_api + 'jia_yi_name_list_id'
    to_dict = df70.to_dict('records')
    body_raw = {"data_api": to_dict}
    df70 = requests.get(url=url, json=body_raw).json()
    df70 = pd.DataFrame(df70)

    merge = df60.merge(df70, left_on='che_liang_id', right_on='jia_yi_id', how='left')
    merge['bill'] = np.where(merge['bill'] == "", 'Car', merge['bill'])
    merge[['che_liang_id', 'jia_yi_mm_name']] = np.where(merge[['che_liang_id', 'jia_yi_mm_name']].shift() == merge[['che_liang_id', 'jia_yi_mm_name']], '', merge[['che_liang_id', 'jia_yi_mm_name']])

    
    df80 = merge.fillna(0).round(decimals = 1).to_dict('records')
    return df80


@router.get("/summaryAllYS")
async def summaryAllYS(session: Session = Depends(get_db)):
    # session = Session()
    
    df20 = pd.read_sql(sql = session.query(PetrolModel)\
                        .with_entities(
                            PetrolModel.datetime,
                            PetrolModel.data_details[0]['che_liang_id'],
                            PetrolModel.data_details[0]['distant'],
                            PetrolModel.data_details[0]['qty'],
                            PetrolModel.data_details[0]['bill'],
                            )
                            # .filter(PetrolModel.datetime > six_months_ago)
                            .statement,
                con = session.bind)
    df20 = df20.rename(columns={
        "anon_1": 'che_liang_id',
        "anon_2": 'distant',
        "anon_3": 'qty',
        "anon_4": 'bill',
        })
    # display("df20", df20)

    df30 = pd.read_sql(sql = session.query(PetrolModel2)\
                            .with_entities(
                                PetrolModel2.datetime,
                                PetrolModel2.data_details[0]['che_liang_id'],
                                PetrolModel2.data_details[0]['distant'],
                                PetrolModel2.data_details[0]['qty'],
                                )
                                # .filter( PetrolModel2.datetime > six_months_ago)
                                .statement,
                    con = session.bind)
    df30 = df30.rename(columns={
        "anon_1": 'che_liang_id',
        "anon_2": 'distant',
        "anon_3": 'qty',
        })

    # display("df30", df30)


    df40 = pd.concat([df20, df30], ignore_index=True)
    df40 = df40.sort_values(['che_liang_id', 'datetime'], ascending=[True, True])
    # display(df40)

    # df50 = df40[df40['che_liang_id'] ==  25371 ]
    # return che_liang_id 104 and 25371
    # df50 = df40[df40['che_liang_id'].isin([25371, 13417, 26594])]
    df50 = df40
    
    df50['previous_distant'] = df50.groupby('che_liang_id')['distant'].transform(lambda x : x.shift())
    df50['distance_used'] = df50.groupby('che_liang_id').apply(lambda x: x['previous_distant'] if x['previous_distant'].any() == 0 else (x['distant'] if x['distant'].any() == 0 else x['distant'] - x['previous_distant'])).reset_index('che_liang_id',drop=True)
    df50['mean_distance_used'] = df50.groupby('che_liang_id')['distance_used'].transform(lambda x : x.mean())
    df50['previous_qty'] = df50.groupby('che_liang_id')['qty'].transform(lambda x : x.shift())    
    df50['km_lit'] = df50.groupby('che_liang_id').apply(lambda x : x['distance_used'] / x['previous_qty']  ).reset_index('che_liang_id',drop=True)  
    df50['average'] = df50.groupby('che_liang_id')['km_lit'].transform(lambda x : x.mean()) 
    df50['average_percent'] = df50.groupby('che_liang_id').apply(lambda x: x['km_lit'] if x['km_lit'].any() == 0 else ((x.distance_used - x.mean_distance_used) / x.mean_distance_used) * 100).reset_index('che_liang_id',drop=True) 

    def train_isolation_group(pass_distance_used):
        outlier_detector = IsolationForest(random_state=42)
        np_scaled = pass_distance_used.values.reshape(-1, 1)
        distance_used_fillna = pd.DataFrame(np_scaled).fillna(0)
        outlier_detector.fit(distance_used_fillna)
        prediction = outlier_detector.predict(distance_used_fillna)
        prediction_strings = ['Outlier' if x < 0 else 'Standard' for x in prediction]
        return prediction_strings
    df50['outlier_flag'] = df50.groupby('che_liang_id')['distance_used'].transform(train_isolation_group)

    df50['distance_used_2'] = df50.apply(lambda x: None if x['outlier_flag'] == 'Outlier' else x['distance_used'], axis=1)
    df50['mean_distance_used_2'] = df50.groupby('che_liang_id')['distance_used_2'].transform(lambda x : x.mean()) 
    df50['km_lit_2'] = df50.apply(lambda x: None if x['outlier_flag'] == 'Outlier' else x['distance_used'] / x['previous_qty'], axis=1)
    df50['average_2'] = df50.groupby('che_liang_id')['km_lit_2'].transform(lambda x : x.mean())
    df50['average_percent_2'] = df50.groupby('che_liang_id').apply(lambda x: x['km_lit_2'] if x['km_lit_2'].any() == None else ((x.distance_used - x.mean_distance_used_2) / x.mean_distance_used_2) * 100).reset_index('che_liang_id',drop=True) 
    df50['mean'] = df50.groupby('che_liang_id').apply(lambda x: (x.mean_distance_used - x.mean_distance_used_2) / x.mean_distance_used_2 * 100).reset_index('che_liang_id',drop=True) 
    df50['count'] = 'calculate'

    def sumfunc_value(x):
        qty = x['qty'].mean()
        distance_used = x['distance_used'].mean()
        km_lit = x['km_lit'].mean()
        distance_used_2 = x['distance_used_2'].mean()
        km_lit_2 = x['km_lit_2'].mean()
        mean = (x['distance_used_2'].mean() - x['distance_used'].mean()) / x['distance_used'].mean() * 100
        minute = 60 * x['km_lit_2'].mean()  
        oneKmLit =  x['distance_used_2'].mean() if x['distance_used_2'].any() == 0  else x['qty'].mean() / x['distance_used_2'].mean()
        hundredKmLit = x['km_lit_2'].mean() if x['km_lit_2'].any() == 0  else 100 / x['km_lit_2'].mean()
        count = x['che_liang_id'].count()
        
        return pd.Series([qty, distance_used, km_lit, distance_used_2, km_lit_2, mean, minute, oneKmLit, hundredKmLit, count], 
                         index=['qty', 'distance_used', 'km_lit', 'distance_used_2', 'km_lit_2', 'mean', 'minute', 'oneKmLit', 'hundredKmLit', 'count'])
    df60 = df50.groupby(['che_liang_id', pd.Grouper(key='datetime', freq='YS')]).apply(sumfunc_value).reset_index()
    
    def train_isolation_group(pass_distance_used):
        outlier_detector = IsolationForest(random_state=42)
        np_scaled = pass_distance_used.values.reshape(-1, 1)
        distance_used_fillna = pd.DataFrame(np_scaled).fillna(0)
        outlier_detector.fit(distance_used_fillna)
        prediction = outlier_detector.predict(distance_used_fillna)
        prediction_strings = ['Outlier' if x < 0 else 'Standard' for x in prediction]
        return prediction_strings
    df60['outlier_flag'] = df60.groupby('che_liang_id')['count'].transform(train_isolation_group)
    df60['outlier_flag_dis2'] = df60.apply(lambda x: None if x['outlier_flag'] == 'Outlier' else x['distance_used_2'], axis=1)
    df60['mean_distance_used_2'] = df60.groupby('che_liang_id')['outlier_flag_dis2'].transform(lambda x : x.mean())
    df60['average_2'] = df60.groupby('che_liang_id').apply(lambda x: (x.distance_used_2 - x.mean_distance_used_2) / x.mean_distance_used_2 * 100).reset_index('che_liang_id',drop=True) 
    
    
    df70 = df60
    df70 = df70.loc[:, df70.columns.isin(['che_liang_id'])]
    df70 = df70.rename(columns={"che_liang_id": 'jia_yi_id'})
    
    url = Arter_api + 'jia_yi_name_list_id'
    to_dict = df70.to_dict('records')
    body_raw = {"data_api": to_dict}
    df70 = requests.get(url=url, json=body_raw).json()
    df70 = pd.DataFrame(df70)

    merge = df60.merge(df70, left_on='che_liang_id', right_on='jia_yi_id', how='left')
    # merge['bill'] = np.where(merge['bill'] == "", 'Car', merge['bill'])
    merge[['che_liang_id', 'jia_yi_mm_name']] = np.where(merge[['che_liang_id', 'jia_yi_mm_name']].shift() == merge[['che_liang_id', 'jia_yi_mm_name']], '', merge[['che_liang_id', 'jia_yi_mm_name']])

    
    df80 = merge.fillna(0).round(decimals = 1).to_dict('records')
    return df80


@router.get("/summaryLastMonth/{choose}")
async def summaryLastMonth(choose: int, session: Session = Depends(get_db)):
    # session = Session()
    
    df20 = pd.read_sql(sql = session.query(PetrolModel)\
                        .with_entities(
                            PetrolModel.datetime,
                            PetrolModel.data_details[0]['che_liang_id'],
                            PetrolModel.data_details[0]['distant'],
                            PetrolModel.data_details[0]['qty'],
                            PetrolModel.data_details[0]['bill'],
                            )
                            # .filter(PetrolModel.datetime > six_months_ago)
                            .statement,
                con = session.bind)
    df20 = df20.rename(columns={
        "anon_1": 'che_liang_id',
        "anon_2": 'distant',
        "anon_3": 'qty',
        "anon_4": 'bill',
        })
    # display("df20", df20)

    df30 = pd.read_sql(sql = session.query(PetrolModel2)\
                            .with_entities(
                                PetrolModel2.datetime,
                                PetrolModel2.data_details[0]['che_liang_id'],
                                PetrolModel2.data_details[0]['distant'],
                                PetrolModel2.data_details[0]['qty'],
                                )
                                # .filter( PetrolModel2.datetime > six_months_ago)
                                .statement,
                    con = session.bind)
    df30 = df30.rename(columns={
        "anon_1": 'che_liang_id',
        "anon_2": 'distant',
        "anon_3": 'qty',
        })

    # display("df30", df30)


    df40 = pd.concat([df20, df30], ignore_index=True)
    df40 = df40.sort_values(['che_liang_id', 'datetime'], ascending=[True, True])
    # display(df40)

    # df50 = df40[df40['che_liang_id'] ==  25371 ]
    # return che_liang_id 104 and 25371
    # df50 = df40[df40['che_liang_id'].isin([25371, 13417, 26594])]
    df50 = df40
    
    df50['previous_distant'] = df50.groupby('che_liang_id')['distant'].transform(lambda x : x.shift())
    df50['distance_used'] = df50.groupby('che_liang_id').apply(lambda x: x['previous_distant'] if x['previous_distant'].any() == 0 else (x['distant'] if x['distant'].any() == 0 else x['distant'] - x['previous_distant'])).reset_index('che_liang_id',drop=True)
    df50['mean_distance_used'] = df50.groupby('che_liang_id')['distance_used'].transform(lambda x : x.mean())
    df50['previous_qty'] = df50.groupby('che_liang_id')['qty'].transform(lambda x : x.shift())    
    df50['km_lit'] = df50.groupby('che_liang_id').apply(lambda x : x['distance_used'] / x['previous_qty']  ).reset_index('che_liang_id',drop=True)  
    df50['average'] = df50.groupby('che_liang_id')['km_lit'].transform(lambda x : x.mean()) 
    df50['average_percent'] = df50.groupby('che_liang_id').apply(lambda x: x['km_lit'] if x['km_lit'].any() == 0 else ((x.distance_used - x.mean_distance_used) / x.mean_distance_used) * 100).reset_index('che_liang_id',drop=True) 

    def train_isolation_group(pass_distance_used):
        outlier_detector = IsolationForest(random_state=42)
        np_scaled = pass_distance_used.values.reshape(-1, 1)
        distance_used_fillna = pd.DataFrame(np_scaled).fillna(0)
        outlier_detector.fit(distance_used_fillna)
        prediction = outlier_detector.predict(distance_used_fillna)
        prediction_strings = ['Outlier' if x < 0 else 'Standard' for x in prediction]
        return prediction_strings
    df50['outlier_flag'] = df50.groupby('che_liang_id')['km_lit'].transform(train_isolation_group)

    df50['km_lit_2'] = df50.apply(lambda x: None if x['outlier_flag'] == 'Outlier' else x['km_lit'], axis=1)
    df50['average_2'] = df50.groupby('che_liang_id')['km_lit_2'].transform(lambda x : x.mean())
    df50['mean_distance_used_2'] = df50.groupby('che_liang_id').apply(lambda x: (x['average_2'] * x['previous_qty']) + 0.01).reset_index('che_liang_id',drop=True) 
    df50['average_percent_2'] = df50.groupby('che_liang_id').apply(lambda x: (x.distance_used - x.mean_distance_used_2) / x.mean_distance_used_2 * 100).reset_index('che_liang_id',drop=True) 
    df50['mean'] = df50.groupby('che_liang_id').apply(lambda x: ((x.average_2 - x.average) / x.average) * 100).reset_index('che_liang_id',drop=True) 
    df50['count'] = 'calculate'

    def sumfunc_value(x):
        qty = x['qty'].mean()
        distance_used = x['distance_used'].mean()
        km_lit = x['km_lit'].mean()
        km_lit_2 = x['km_lit_2'].mean()
        distance_used_2 = (x['km_lit_2'] * x['previous_qty']).mean() + 0.01
        mean = (x['km_lit_2'].mean() - x['km_lit'].mean()) / x['km_lit'].mean() * 100
        minute = 60 * x['km_lit_2'].mean()  
        hundredKmLit = x['km_lit_2'].mean() if x['km_lit_2'].any() == 0  else 100 / x['km_lit_2'].mean()
        count = x['che_liang_id'].count()
        
        return pd.Series([qty, distance_used, km_lit, km_lit_2, distance_used_2, mean, minute, hundredKmLit, count], 
                            index=['qty', 'distance_used', 'km_lit', 'km_lit_2', 'distance_used_2', 'mean', 'minute', 'hundredKmLit', 'count'])
    df60 = df50.groupby(['che_liang_id', 'bill', pd.Grouper(key='datetime', freq='MS')]).apply(sumfunc_value).reset_index()

    def train_isolation_group(pass_distance_used):
        outlier_detector = IsolationForest(random_state=42)
        np_scaled = pass_distance_used.values.reshape(-1, 1)
        distance_used_fillna = pd.DataFrame(np_scaled).fillna(0)
        outlier_detector.fit(distance_used_fillna)
        prediction = outlier_detector.predict(distance_used_fillna)
        prediction_strings = ['Outlier' if x < 0 else 'Standard' for x in prediction]
        return prediction_strings
    df60['outlier_flag'] = df60.groupby('che_liang_id')['distance_used_2'].transform(train_isolation_group)
    df60['outlier_flag_dis2'] = df60.apply(lambda x: None if x['outlier_flag'] == 'Outlier' else x['distance_used_2'], axis=1)
    df60['mean_distance_used_2'] = df60.groupby('che_liang_id')['outlier_flag_dis2'].transform(lambda x : x.mean())
    df60['average_percent_2'] = df60.apply(lambda x: (x.distance_used_2 - x.mean_distance_used_2) / x.mean_distance_used_2 * 100, axis=1)
    df60 = df60.groupby('che_liang_id').nth(choose).reset_index() 
    
    df70 = df60
    df70 = df70.loc[:, df70.columns.isin(['che_liang_id'])]
    df70 = df70.rename(columns={"che_liang_id": 'jia_yi_id'})
    
    url = Arter_api + 'jia_yi_name_list_id'
    to_dict = df70.to_dict('records')
    body_raw = {"data_api": to_dict}
    df70 = requests.get(url=url, json=body_raw).json()
    df70 = pd.DataFrame(df70)

    merge = df60.merge(df70, left_on='che_liang_id', right_on='jia_yi_id', how='left')
    merge['bill'] = np.where(merge['bill'] == "", 'Car', merge['bill'])
    merge[['che_liang_id', 'jia_yi_mm_name']] = np.where(merge[['che_liang_id', 'jia_yi_mm_name']].shift() == merge[['che_liang_id', 'jia_yi_mm_name']], '', merge[['che_liang_id', 'jia_yi_mm_name']])

    
    df80 = merge.fillna(0).round(decimals = 1).to_dict('records')
    return df80


@router.get("/getFuelPrice")
async def getFuelPrice(session: Session = Depends(get_db)):
    # session = Session()
    df10 = pd.read_sql(sql = session.query(PetrolModel)\
                        .with_entities(
                            PetrolModel.petrol_id,
                            PetrolModel.datetime,
                            PetrolModel.data_details[0]['product_id'],
                            PetrolModel.data_details[0]['price']
                            )
                        .filter(PetrolModel.data_details[0]['price'].astext.cast(String) != "0.0")
                        .order_by(PetrolModel.data_details[0]['product_id'], PetrolModel.datetime.desc())
                        .distinct(PetrolModel.data_details[0]['product_id'])
                        .statement,
                con = session.bind)
    df10 = df10.rename(columns={
        "anon_1": 'product_id', "anon_2": 'price',
        })
    df10['ids'] = pd.Series([100,200])

    url_link = 'https://xn--42cah7d0cxcvbbb9x.com/%E0%B8%A3%E0%B8%B2%E0%B8%84%E0%B8%B2%E0%B8%99%E0%B9%89%E0%B8%B3%E0%B8%A1%E0%B8%B1%E0%B8%99%E0%B8%A7%E0%B8%B1%E0%B8%99%E0%B8%99%E0%B8%B5%E0%B9%89/'
    url = url_link
    df20 = pd.read_html(url, header = 0)
    df20 = df20[0]
    df20['ids'] = pd.Series([100,200], index=[1, 6])
    
    merge = df20.merge(df10, on='ids', how='left')
    merge = merge.rename(columns={ "Unnamed: 0": 'petrol', "ปตท.": 'petrol_price',})
    merge = merge.fillna('-').to_dict('records')
    return merge


@router.get("/getMasadHourLiter")
async def getMasadHourLiter(che_liang_id: int, session: Session = Depends(get_db)):
    # session = Session()
    df10 = pd.read_sql(sql = session.query(Calculate)
                    .with_entities(
                            Calculate.calTwo['che_liang_id'],
                            Calculate.calTwo['datetime'],
                            Calculate.calTwo['oneKmLit'],
                            Calculate.calTwo['minute'],
                            )
                    .filter(Calculate.calTwo['che_liang_id'].astext.cast(Integer) == che_liang_id)
                    .statement, con = session.bind)
    df10 = df10.rename(columns=dict(anon_1='che_liang_id', anon_2='datetime', anon_3='oneKmLit', anon_4='minute'))

    return df10.to_dict("records")


@router.get("/monthlyDetailsCar/{che_liang_id}")
async def monthlyDetailsCar(che_liang_id: int, session: Session = Depends(get_db)):
    # session = Session()
    # today = datetime.date.today()
    # future_day = today.day
    # future_month = (today.month - 12) % 12
    # future_year = today.year + ((today.month - 12) // 12)
    # six_months_ago = datetime.date(future_year, future_month, future_day)
    import datetime as DT
    today = DT.date.today()
    week_ago = today - DT.timedelta(days=90)
    print(week_ago)

    df20 = pd.read_sql(sql = session.query(PetrolModel)\
                            .with_entities(
                                PetrolModel.petrol_id,
                                PetrolModel.datetime,
                                PetrolModel.data_details[0]['che_liang_id'],
                                PetrolModel.data_details[0]['distant'],
                                PetrolModel.data_details[0]['qty'],
                                PetrolModel.data_details[0]['price'],
                                PetrolModel.data_details[0]['product_id'],
                                )
                                .filter(PetrolModel.datetime > week_ago).statement,
                    con = session.bind)
    df20 = df20.rename(columns={
        "anon_1": 'che_liang_id',
        "anon_2": 'distant',
        "anon_3": 'qty',
        "anon_4": 'price',
        "anon_5": 'product_id',
        })
    # display("df20", df20)

    df30 = pd.read_sql(sql = session.query(PetrolModel2)\
                            .with_entities(
                                PetrolModel2.petrol_id,
                                PetrolModel2.datetime,
                                PetrolModel2.data_details[0]['che_liang_id'],
                                PetrolModel2.data_details[0]['distant'],
                                PetrolModel2.data_details[0]['qty'],
                                PetrolModel2.data_details[0]['price'],
                                PetrolModel2.data_details[0]['product_id'],
                                )
                                .filter( PetrolModel2.datetime > week_ago).statement,
                    con = session.bind)
    df30 = df30.rename(columns={
        "anon_1": 'che_liang_id',
        "anon_2": 'distant',
        "anon_3": 'qty',
        "anon_4": 'price',
        "anon_5": 'product_id',
        })

    # display("df30", df30)


    df40 = pd.concat([df20, df30], ignore_index=True)
    df40 = df40.sort_values(['datetime'], ascending=[True])
    # display(df40)

    df50 = df40[df40['che_liang_id'] ==  che_liang_id ]
    df50['amount'] = df50['qty'] * df50['price']
    df50['count'] = 'calculate'
    df50['previous_distant'] = df50['distant'].shift()
    def fx(x):
            if x['previous_distant'] == 0:
                return x['previous_distant']
            elif x['distant'] == 0:
                return x['distant']
            else:
                return x['distant'] - x['previous_distant']
    df50['distance_used'] = df50.apply(lambda x : fx(x),axis=1)
    df50['previous_qty'] = df50['qty'].shift()
    df50['km_lit'] = (df50['distance_used'] / df50['previous_qty']) 
    # display(df50)


    df60 = df50.set_index('datetime', inplace=True)
    df60 = df50.resample('MS').agg({'che_liang_id': np.max, 'qty': np.sum, 'price': np.mean, 'amount': np.sum, 'count': np.size, 'km_lit': np.mean, 'distance_used': np.mean}).reset_index().round(decimals = 1).fillna("")
    # display(df60)
    return df60.to_dict("records")


@router.get("/Inday/{get_date}")
async def Inday(get_date: str, session: Session = Depends(get_db)):
    # session = Session()
    try:
        df20 = pd.read_sql(sql = session.query(PetrolModel).order_by(PetrolModel.datetime.desc())
                            .filter(cast(PetrolModel.datetime, Date) == get_date)
                            .with_entities(
                                    PetrolModel.data_details[0]['fen_dian_id'],
                                    PetrolModel.datetime,
                                    PetrolModel.datetime_bill,
                                    PetrolModel.petrol_id,
                                    PetrolModel.data_details[0]['che_liang_id'],
                                    PetrolModel.data_details[0]['distant'],
                                    PetrolModel.data_details[0]['bill'],
                                    PetrolModel.status_details['status'],
                                    PetrolModel.data_details[0]['imageKm'],
                                    PetrolModel.data_details[0]['imageKm2'],
                                    PetrolModel.data_details[0]['imageAccept'],
                                    PetrolModel.data_details[0]['imageKmDate'],
                                    PetrolModel.data_details[0]['qty'],
                                    PetrolModel.data_details[0]['imageBill'],
                                    PetrolModel.data_details[0]['driver'],
                                    )
                            .statement, con = session.bind)
        df20 = df20.rename(columns=dict(anon_1='fen_dian_id', anon_2='che_liang_id', 
                                        anon_3='distant', anon_4='bill', anon_5='status', 
                                        anon_6='imageKm', anon_7='imageKm2', anon_8='imageAccept', anon_9='imageKmDate',
                                        anon_10='qty', anon_11='imageBill', anon_12='driver'))
        try: 
            df20['imageKmDate'] = pd.to_datetime(df20['imageKmDate']).dt.tz_convert('Asia/Yangon')
        except: 
            df20['imageKmDate'] = None
        df21 = df20['petrol_id'].tolist()
        # display('df20', df20)
        
        df30 = pd.read_sql(sql = session.query(Calculate)
                            .filter(Calculate.calOne['petrol_id'].astext.cast(String).in_(df21))
                            .with_entities(
                                    Calculate.calOne['petrol_id'],
                                    Calculate.calOne['distance_used'],
                                    Calculate.calOne['mean_distance_used_2'],
                                    Calculate.calOne['average_percent_2'],
                                    Calculate.calOne['mean'],
                                    Calculate.calOne['count'],
                                    Calculate.calOne['uniqleQty'],
                                    Calculate.calOne['FirstuniqleQty'],
                                    Calculate.calOne['LastuniqleQty'],
                                    Calculate.calOne['diff_datetime_bill'],
                                    Calculate.calOne['mean_datetime_bill'],
                                    Calculate.calOne['percent_date_bill'],
                                    Calculate.calOne['uniqleDriver'],
                                    Calculate.calOne['FirstuniqleDriver'],
                                    Calculate.calOne['LastuniqleDriver'],
                                    Calculate.calOne['driverCount'],
                                    )
                            .statement, con = session.bind)
        df30 = df30.rename(columns=dict(anon_1='petrol_id', anon_2='distance_used', anon_3='mean_distance_used_2', anon_4='average_percent_2', anon_5='mean', anon_6='count',
                                        anon_7='uniqleQty', anon_8='FirstuniqleQty', anon_9='LastuniqleQty', anon_10='diff', anon_11='mean', anon_12='percent', anon_13='uniqleDriver', 
                                        anon_14='FirstuniqleDriver', anon_15='LastuniqleDriver', anon_16='driverCount'))
        # display('df30', df30)

        df40 = df20
        df40 = df40.loc[:, df40.columns.isin(['che_liang_id', 'petrol_id', 'product_id'])]
        df40 = df40.rename(columns={"che_liang_id": 'jia_yi_id'})
        # display('df40', df40)

        # url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'
        url = f'{pv_api}/mongodb_data_api/api/v2/search/jia_yi_name_list_id'
        to_dict = df40.to_dict('records')
        body_raw = {"data_api": to_dict}
        df50 = requests.get(url=url, json=body_raw).json()
        df50 = pd.DataFrame(df50)
        # display('df50', df50)
        
        merge = df20.merge(df50, left_on='che_liang_id', right_on='jia_yi_id', how='left').merge(df30, on='petrol_id', how='outer')
        merge = merge.fillna("").to_dict("records")
        # display('merge', merge)
    except:
        # merge = pd.DataFrame()
        merge = []
           
    # session.close()
    return merge

@router.get("/listOfCar")
async def listOfCar(session: Session = Depends(get_db)):
    # session = Session()
    df81 = pd.read_sql(sql = session.query(PetrolModel)
                        .with_entities(
                            PetrolModel.data_details[0]['che_liang_id'],
                        )
                        .distinct(PetrolModel.data_details[0]['che_liang_id'])
                        .filter(PetrolModel.data_details[0]['bill'].astext.cast(String) == "")
                        .statement, con = session.bind)
    df81 = df81.rename(columns={
        "anon_1": 'che_liang_id',
        })
    # display(df81)


    df91 = pd.read_sql(sql = session.query(Calculate)
        .with_entities(
            Calculate.calTwo['petrol_id'],
            Calculate.calTwo['datetime'],
            Calculate.calTwo['che_liang_id'],
            Calculate.calTwo['average'],
            Calculate.calTwo['minute'],
            Calculate.calTwo['oneKmLit'],
            Calculate.calTwo['hundredKmLit'],
            Calculate.calTwo['mean_distance_used_2'],
            Calculate.calTwo['average_2'],
            Calculate.calTwo['mean'],
            Calculate.calTwo['kmLitStd'],
            Calculate.calTwo['kmLitMin'],
            Calculate.calTwo['kmLit25Per'],
            Calculate.calTwo['kmLit50Per'],
            Calculate.calTwo['kmLit75Per'],
            Calculate.calTwo['kmLitMax'],
            Calculate.calTwo['countDuplicate'],
            Calculate.calTwo['che_liang_id_Count'],
            Calculate.calTwo['qty'],
            Calculate.calTwo['meanQty'],
            Calculate.calTwo['sumQty'],
            Calculate.calTwo['mean_datetime_bill'],
        )               
        .distinct(Calculate.calTwo['che_liang_id'])
        .order_by(Calculate.calTwo['che_liang_id'], Calculate.calTwo['datetime'].desc())
        .statement, con = session.bind)

    df91 = df91.rename(columns={
        "anon_1": 'petrol_id',
        "anon_2": 'datetime',
        "anon_3": 'che_liang_id',
        "anon_4": 'average',
        "anon_5": 'minute',
        "anon_6": 'oneKmLit',
        "anon_7": 'hundredKmLit',
        "anon_8": 'mean_distance_used_2',
        "anon_9": 'average_2',
        "anon_10": 'mean',
        "anon_11": 'kmLitStd',
        "anon_12": 'kmLitMin',
        "anon_13": 'kmLit25Per',
        "anon_14": 'kmLit50Per',
        "anon_15": 'kmLit75Per',
        "anon_16": 'kmLitMax',
        "anon_17": 'countDuplicate',
        "anon_18": 'che_liang_id_Count',
        "anon_19": 'qty',
        "anon_20": 'meanQty',
        "anon_21": 'sumQty',
        "anon_22": 'mean_datetime_bill',

    }).round(decimals = 1)
    # display(df91)


    df100 = df81
    df100 = df100.rename(columns={"che_liang_id": 'jia_yi_id'})
    # display(df100)

    # url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'
    url = Arter_api + 'jia_yi_name_list_id'
    to_dict = df100.to_dict('records')
    body_raw = {"data_api": to_dict}
    df110 = requests.get(url=url, json=body_raw).json()
    df110 = pd.DataFrame(df110)
    # display(df110)


    merge = df81.merge(df91, on='che_liang_id', how='left').merge(df110, left_on='che_liang_id', right_on='jia_yi_id', how='left')
    merge = merge.fillna("")
    # display(merge)
    
    merge = merge.to_dict('records')
    return merge


@router.get("/sendListOfCar")
async def sendListOfCar(session: Session = Depends(get_db)):
    # session = Session()
    df81 = pd.read_sql(sql = session.query(PetrolModel)
                        .with_entities(
                            PetrolModel.datetime,
                            PetrolModel.data_details[0]['che_liang_id'],
                            PetrolModel.data_details[0]['bill']
                        )
                        .distinct(PetrolModel.data_details[0]['che_liang_id'])
                        .order_by(PetrolModel.data_details[0]['che_liang_id'], PetrolModel.datetime.desc())
                        .statement, con = session.bind)
    df81 = df81.rename(columns={
        "anon_1": 'che_liang_id',
        "anon_2": 'bill'
        })

    df81['type'] = np.where(df81['bill'] == 'success', 'masad', 'car')

    merge = df81.to_dict('records')
    return merge


@router.get("/list_details_masad/{che_liang_id}")
async def list_details_masad(che_liang_id: int, session: Session = Depends(get_db)):
    # session = Session()
    # today = datetime.date.today()
    # future_day = today.day
    # future_month = (today.month - 3) % 12
    # future_year = today.year + ((today.month - 3) // 12)
    # six_months_ago = datetime.date(future_year, future_month, future_day)
    import datetime as DT
    today = DT.date.today()
    week_ago = today - DT.timedelta(days=180)
    print(week_ago)
    
    df20 = pd.read_sql(sql = session.query(PetrolModel)\
                            .with_entities(
                                PetrolModel.petrol_id,
                                PetrolModel.datetime,
                                PetrolModel.datetime_bill,
                                PetrolModel.data_details[0]['che_liang_id'],
                                PetrolModel.data_details[0]['distant'],
                                PetrolModel.data_details[0]['qty'],
                                PetrolModel.data_details[0]['driver'],
                                )
                                .filter(PetrolModel.datetime > week_ago).statement,
                    con = session.bind)
    df20 = df20.rename(columns={
        "anon_1": 'che_liang_id',
        "anon_2": 'distant',
        "anon_3": 'qty',
        "anon_4": 'driver',
        })

    # display("df20", df20)

    df30 = pd.read_sql(sql = session.query(PetrolModel2)\
                            .with_entities(
                                PetrolModel2.petrol_id,
                                PetrolModel2.datetime,
                                PetrolModel2.data_details[0]['che_liang_id'],
                                PetrolModel2.data_details[0]['distant'],
                                PetrolModel2.data_details[0]['qty'],
                                )
                                .filter( PetrolModel2.datetime > week_ago).statement,
                    con = session.bind)
    df30 = df30.rename(columns={
        "anon_1": 'che_liang_id',
        "anon_2": 'distant',
        "anon_3": 'qty',
        })

    # display("df30", df30)

    df40 = pd.concat([df20, df30], ignore_index=True)
    df40 = df40.sort_values(['datetime'], ascending=[True])
    # display(df40)

    df50 = df40[df40['che_liang_id'] == che_liang_id]

    df50['previous_datetime_bill'] = df50['datetime_bill'].shift()
    df50['diff_datetime_bill'] = (df50['datetime_bill'] - df50['previous_datetime_bill']).dt.days
    df50['mean_datetime_bill'] = df50['diff_datetime_bill'].mean()
    df50['percent_date_bill'] = df50.apply(lambda x: x.mean_datetime_bill and (x.diff_datetime_bill - x.mean_datetime_bill) / x.mean_datetime_bill * 100, axis=1)

    df50['previous_distant'] = df50['distant'].shift()
    df50['distance_used'] = df50.apply(lambda x: x['previous_distant'] if x['previous_distant'] == 0 else (x['distant'] if x['distant'] == 0 else x['distant'] - x['previous_distant']), axis=1)
    df50['mean_distance_used'] = df50['distance_used'].mean()
    df50['previous_qty'] = df50['qty'].shift()
    df50['count'] = df50['distance_used']
    df50['km_lit'] = (df50['distance_used'] / df50['previous_qty'])         
    df50['average'] = df50[df50['km_lit'] != 0]["km_lit"].mean()
    df50['average_percent'] = df50.apply(lambda x: x['km_lit'] if x['km_lit'] == 0 else x.mean_distance_used and ((x.distance_used - x.mean_distance_used) / x.mean_distance_used) * 100, axis=1)

    outlier_detector = IsolationForest(random_state=42)
    distance_used_fillna = df50[['km_lit']].fillna(0)
    outlier_detector.fit(distance_used_fillna)
    prediction = outlier_detector.predict(distance_used_fillna)
    prediction_strings = ['Outlier' if x < 0 else 'Standard' for x in prediction]
    df50['outlier_flag'] = prediction_strings
    
    df50['km_lit_2'] = df50.apply(lambda x: None if x['outlier_flag'] == 'Outlier' else x['km_lit'], axis=1)
    df50['average_2'] = df50[df50['km_lit_2'] != 0]["km_lit_2"].mean()
    df50['mean_distance_used_2'] = (df50['average_2'] * df50['previous_qty']) + 0.01
    df50['average_percent_2'] = df50.apply(lambda x: (x.distance_used - x.mean_distance_used_2) / x.mean_distance_used_2 * 100, axis=1)
    # df50['mean'] = ((df50.average_2 - df50.average) / df50.average) * 100
    df50['mean'] = df50['average_percent_2'].abs().sum() / len(df50)

    df60 = df50.pivot_table(columns=['distance_used'], aggfunc='size')
    df60 = pd.DataFrame(df60)
    df60 = df60.rename(columns={0: 'countoo'}).reset_index()
    df50['count'] = df50[['count']].replace(to_replace =df60['distance_used'].to_list(), value =df60['countoo'].to_list())
    
    df50 = df50.fillna(0).round(decimals = 1)
    df50 = df50.to_dict('records')
    return df50

# df50['mean'] = df50['average_percent_2'].abs().sum()

@router.get("/listOfMasad")
async def listOfMasad(session: Session = Depends(get_db)):
    # session = Session()
    df81 = pd.read_sql(sql = session.query(PetrolModel)
                        .with_entities(
                            PetrolModel.data_details[0]['che_liang_id'],
                            PetrolModel.data_details[0]['fen_dian_id'],
                        )
                        .distinct(PetrolModel.data_details[0]['che_liang_id'])
                        .order_by(PetrolModel.data_details[0]['che_liang_id'],PetrolModel.datetime.desc())
                        .filter(
                            not_(
                                PetrolModel.data_details[0]['bill'].astext.cast(String) == "",
                                )
                            )
                        .statement, con = session.bind)


    df81 = df81.rename(columns={
        "anon_1": 'che_liang_id',
        "anon_2": 'fen_dian_id',
        })
    # display(df81)


    df91 = pd.read_sql(sql = session.query(Calculate)
        .with_entities(
            Calculate.calTwo['petrol_id'],
            Calculate.calTwo['datetime'],
            Calculate.calTwo['che_liang_id'],
            Calculate.calTwo['average'],
            Calculate.calTwo['minute'],
            Calculate.calTwo['oneKmLit'],
            Calculate.calTwo['hundredKmLit'],
            Calculate.calTwo['mean_distance_used_2'],
            Calculate.calTwo['average_2'],
            Calculate.calTwo['mean'],
            Calculate.calTwo['kmLitStd'],
            Calculate.calTwo['kmLitMin'],
            Calculate.calTwo['kmLit25Per'],
            Calculate.calTwo['kmLit50Per'],
            Calculate.calTwo['kmLit75Per'],
            Calculate.calTwo['kmLitMax'],
            Calculate.calTwo['countDuplicate'],
            Calculate.calTwo['che_liang_id_Count'],
            Calculate.calTwo['mean_distance_used'],
            Calculate.calTwo['qty'],
            Calculate.calTwo['meanQty'],
            Calculate.calTwo['sumQty'],
            Calculate.calTwo['mean_datetime_bill'],
        )               
        .distinct(Calculate.calTwo['che_liang_id'])
        .order_by(Calculate.calTwo['che_liang_id'], Calculate.calTwo['datetime'].desc())
        .statement, con = session.bind)

    df91 = df91.rename(columns={
        "anon_1": 'petrol_id',
        "anon_2": 'datetime',
        "anon_3": 'che_liang_id',
        "anon_4": 'average',
        "anon_5": 'minute',
        "anon_6": 'oneKmLit',
        "anon_7": 'hundredKmLit',
        "anon_8": 'mean_distance_used_2',
        "anon_9": 'average_2',
        "anon_10": 'mean',
        "anon_11": 'kmLitStd',
        "anon_12": 'kmLitMin',
        "anon_13": 'kmLit25Per',
        "anon_14": 'kmLit50Per',
        "anon_15": 'kmLit75Per',
        "anon_16": 'kmLitMax',
        "anon_17": 'countDuplicate',
        "anon_18": 'che_liang_id_Count',
        "anon_19": 'mean_distance_used',
        "anon_20": 'qty',
        "anon_21": 'meanQty',
        "anon_22": 'sumQty',
        "anon_23": 'mean_datetime_bill',
    }).round(decimals = 1)
    # display(df91)


    df100 = df81
    df100 = df100.rename(columns={"che_liang_id": 'jia_yi_id'})
    # display(df100)

    # url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'
    url = Arter_api + 'jia_yi_name_list_id'
    to_dict = df100.to_dict('records')
    body_raw = {"data_api": to_dict}
    df110 = requests.get(url=url, json=body_raw).json()
    df110 = pd.DataFrame(df110)
    # display(df110)


    merge = df81.merge(df91, on='che_liang_id', how='left').merge(df110, left_on='che_liang_id', right_on='jia_yi_id', how='left')
    merge = merge.fillna("")
    # display(merge)
    
    merge = merge.to_dict('records')
    return merge




@router.put("/confirm_status/{petrol_id}")
async def confirmStatus(petrol_id: str, put: confirm_status, session: Session = Depends(get_db)):
    # UPDATE STATUS TO SUCCESS
    # session = Session()
    user = session.query(PetrolModel).filter(
        PetrolModel.petrol_id == petrol_id).first()
    ccc = user.status_details
    ccc.update({"status": put.status})
    flag_modified(user, 'status_details')
    session.commit()
    
    # RETURN VALUE
    get_petrol_id = user.petrol_id
    get_id = user.data_details[0]["che_liang_id"]
    get_datetime = user.datetime
    
    # GET MEAN
    # today = datetime.date.today()
    # future_day = today.day
    # future_month = (today.month - 3) % 12
    # future_year = today.year + ((today.month - 3) // 12)
    # six_months_ago = datetime.date(future_year, future_month, future_day) 
    import datetime as DT
    today = DT.date.today()
    week_ago = today - DT.timedelta(days=90)
    print(week_ago)
    df20 = pd.read_sql(sql = session.query(PetrolModel)\
                            .with_entities(
                                PetrolModel.petrol_id,
                                PetrolModel.datetime,
                                PetrolModel.datetime_bill,
                                PetrolModel.data_details[0]['che_liang_id'],
                                PetrolModel.data_details[0]['distant'],
                                PetrolModel.data_details[0]['qty'],
                                )
                                .filter(PetrolModel.datetime > week_ago).statement,
                    con = session.bind)
    df20 = df20.rename(columns={
        "anon_1": 'che_liang_id',
        "anon_2": 'distant',
        "anon_3": 'qty',
        })
    # display("df20", df20)
    df30 = pd.read_sql(sql = session.query(PetrolModel2)\
                            .with_entities(
                                PetrolModel2.petrol_id,
                                PetrolModel2.datetime,
                                PetrolModel2.data_details[0]['che_liang_id'],
                                PetrolModel2.data_details[0]['distant'],
                                PetrolModel2.data_details[0]['qty'],
                                )
                                .filter( PetrolModel2.datetime > week_ago).statement,
                    con = session.bind)
    df30 = df30.rename(columns={
        "anon_1": 'che_liang_id',
        "anon_2": 'distant',
        "anon_3": 'qty',
        })
    # # display("df30", df30)
    df40 = pd.concat([df20, df30], ignore_index=True)
    df40 = df40.sort_values(['datetime'], ascending=[True])
    # # display(df40)
    df50 = df40[df40['che_liang_id'] == get_id]
    df50['previous_distant'] = df50['distant'].shift()
    df50['distance_used'] = df50.apply(lambda x: x['previous_distant'] if x['previous_distant'] == 0 else (x['distant'] if x['distant'] == 0 else x['distant'] - x['previous_distant']), axis=1)
    df50['mean_distance_used'] = df50['distance_used'].mean()
    df50['qty'] = df50['qty']
    df50['meanQty'] = df50['qty'].mean()
    df50['sumQty'] = df50['qty'].sum()

    df50['previous_datetime_bill'] = df50['datetime_bill'].shift()
    df50['diff_datetime_bill'] = (df50['datetime_bill'] - df50['previous_datetime_bill']).dt.days
    df50['mean_datetime_bill'] = df50['diff_datetime_bill'].mean()

    df50['previous_qty'] = df50['qty'].shift()
    df50['count'] = df50['distance_used']
    df50['km_lit'] = (df50['distance_used'] / df50['previous_qty'])         
    df50['average'] = df50[df50['km_lit'] != 0]["km_lit"].mean()
    df50['average_percent'] = df50.apply(lambda x: x['km_lit'] if x['km_lit'] == 0 else x.mean_distance_used and ((x.distance_used - x.mean_distance_used) / x.mean_distance_used) * 100, axis=1)
    
    outlier_detector = IsolationForest(random_state=42)
    distance_used_fillna = df50[['km_lit']].fillna(0)
    outlier_detector.fit(distance_used_fillna)
    prediction = outlier_detector.predict(distance_used_fillna)
    prediction_strings = ['Outlier' if x < 0 else 'Standard' for x in prediction]
    df50['outlier_flag'] = prediction_strings
    
    df50['km_lit_2'] = df50.apply(lambda x: None if x['outlier_flag'] == 'Outlier' else x['km_lit'], axis=1)
    df50['average_2'] = df50[df50['km_lit_2'] != 0]["km_lit_2"].mean()
    df50['mean_distance_used_2'] = (df50['average_2'] * df50['previous_qty']) + 0.01
    df50['average_percent_2'] = df50.apply(lambda x: (x.distance_used - x.mean_distance_used_2) / x.mean_distance_used_2 * 100, axis=1)
    # df50['mean'] = ((df50.average_2 - df50.average) / df50.average) * 100
    df50['mean'] = df50['average_percent_2'].abs().sum() / len(df50)

    df60 = df50.pivot_table(columns=['distance_used'], aggfunc='size')
    df60 = pd.DataFrame(df60)
    df60 = df60.rename(columns={0: 'countoo'}).reset_index()
    df50['count'] = df50[['count']].replace(to_replace =df60['distance_used'].to_list(), value =df60['countoo'].to_list())

    df50['oneKmLit'] = 1 / df50['average_2']
    df50['hundredKmLit'] = 100 / df50['average_2']
    df50['minute'] = 60 * df50['average_2']
    df50['kmLitStd'] = df50['km_lit'].std()
    df50['countDuplicate'] = df50['count'].max()
    df50['che_liang_id_Count'] = df50['che_liang_id'].count()

    df50 = df50.fillna(0).round(decimals = 1)
    df50 = df50.to_dict('records')
    
    df70 = dict(petrol_id=get_petrol_id, datetime=str(get_datetime), che_liang_id=df50[0]["che_liang_id"], average=df50[0]["average"], oneKmLit=df50[0]["oneKmLit"], minute=df50[0]["minute"],
                hundredKmLit=df50[0]["hundredKmLit"], mean_distance_used=df50[0]["mean_distance_used"], mean_distance_used_2=df50[0]["mean_distance_used_2"], average_2=df50[0]["average_2"], mean=df50[0]["mean"],
                kmLitStd=df50[0]['kmLitStd'], countDuplicate=df50[0]['countDuplicate'], che_liang_id_Count=df50[0]['che_liang_id_Count'], qty=df50[0]['qty'], meanQty=df50[0]['meanQty'] , sumQty=df50[0]['sumQty'],
                mean_datetime_bill=df50[0]['mean_datetime_bill'])
    
    
    # INSERT INTO CALCULATE TABLE
    user = session.query(Calculate).filter(Calculate.petrol_id == get_petrol_id).first()
    user.calTwo.update(df70)
    flag_modified(user, "calTwo")    
    session.commit()
    # session.close()
    
    return df70




@router.put("/confirmBill/{petrol_id}/{r_id}")
async def confirmBill(petrol_id: str, r_id: str, session: Session = Depends(get_db)):
    # session = Session()

    user = session.query(PetrolModel).filter(
        PetrolModel.petrol_id == petrol_id).first()
    for var in user.data_details:
        # print(var['r_id'])
        if var['r_id'] == r_id:
            var.update(dict(bill="success"))
    flag_modified(user, 'data_details')
    session.commit()
    
    return JSONResponse(status_code=200, content={
        "status_code": 200,
        "message": "success"
    })  
 
 

@router.get("/getToken/{type_user_jia_yi}")
async def getToken(type_user_jia_yi: int):
    dict = [
            {
            "jia_yi_id": type_user_jia_yi
            }
        ]
    # url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'
    url = Arter_api + 'jia_yi_name_list_id'
    body_raw = {"data_api": dict}
    df40 = requests.get(url=url, json=body_raw).json()
    df40 = pd.DataFrame(df40)
    df40 = df40.to_dict('records')
    return df40 


@router.get("/getForm_wareHouse")
async def getForm_wareHouse(session: Session = Depends(get_db)):
    
    # session = Session()
    try:
        # today = datetime.date.today()
        # future_day = today.day
        # future_month = (today.month) % 12
        # future_year = today.year + ((today.month) // 12)
        # six_months_ago = datetime.date(future_year, future_month, future_day)
        # print(six_months_ago)
        # print(type(six_months_ago))
        import datetime as DT
        today = DT.date.today()
        week_ago = today - DT.timedelta(days=90)
        print(week_ago)
        df20 = pd.read_sql(sql = session.query(PetrolModel).order_by(PetrolModel.datetime.desc())
                        .filter(PetrolModel.datetime > week_ago,).statement, con = session.bind)
        df20 = df20.to_dict('records')
        df20 = pd.json_normalize(df20, "data_details", ["auto_id", "petrol_id", "datetime", "status_details"])
        df20 = df20.fillna(0)
        # df20 = df20.to_dict('records')
        
        df30 = df20
        df30 = df30.loc[:, df30.columns.isin(['che_liang_id', 'petrol_id', 'product_id'])]
        df30 = df30.rename(columns={"che_liang_id": 'jia_yi_id'})
        # display('df30', df30)

        # url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'
        url = Arter_api + 'jia_yi_name_list_id'
        to_dict = df30.to_dict('records')
        body_raw = {"data_api": to_dict}
        df40 = requests.get(url=url, json=body_raw).json()
        df40 = pd.DataFrame(df40)
        # display('df40', df40)

        # url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/product_list_id'
        url = Arter_api + 'product_list_id'
        to_dict = df30.to_dict('records')
        body_raw = {"data_api": to_dict}
        df50 = requests.get(url=url, json=body_raw).json()
        df50 = pd.DataFrame(df50)
        # display('df50', df50)

        merge = df20.merge(df40, left_on='che_liang_id', right_on='jia_yi_id', how='left').merge(df50, on='product_id', how='left')
        merge = merge.to_dict('records')
        # display('merge', merge)
    except:
        print("An exception occurred") 
        merge = "No list"   
    # session.close()
    return merge



@router.get("/calDistantUpdate/{che_liang_id}")
async def calDistantUpdate(che_liang_id: int):
    df = pd.read_sql_table('petrol', engine)
    df = df.to_dict('records')
    df = pd.json_normalize(df, "data_details", ["petrol_id", "datetime", "auto_id"])
    df = df.loc[df['che_liang_id'] == che_liang_id]
    df = df.iloc[-2:-1]
    df = df.to_dict('records')
    return df

@router.get("/calDistantInsert/{che_liang_id}")
async def calDistantInsert(che_liang_id: int):
    df = pd.read_sql_table('petrol', engine)
    df = df.to_dict('records')
    df = pd.json_normalize(df, "data_details", ["petrol_id", "datetime", "auto_id"])
    df = df.loc[df['che_liang_id'] == che_liang_id]
    df = df.iloc[-1:]
    df = df.to_dict('records')
    
    new_value = []
    if_yes = False
    if df == []:
       print("yes")
       new_value.append(if_yes)
       new_value = str(new_value)[1:-1] 
    else:
       print("no")  
       new_value.extend(df)  
    print(new_value)   
    return new_value

@router.get("/getPayment")
async def getPayment():
    df = pd.read_sql_table('lei_type', engine)
    # df = df.loc[:, ~df.columns.isin(['data_sub'])]
    df = df.drop([('data_sub')], axis=1)
    df = df.sort_values(by=['auto_id'])
    df = df.to_dict('records')
    return df
    # session = Session()
    # books = session.query(TypeLei.lei_a, TypeLei.lei_b, TypeLei.lei_name).order_by(TypeLei.lei_a.desc()).all()
    session.close()
    # return books

@router.get("/getCurrency")
async def getCurrency():
    df = pd.read_sql_table('bi_zhi', engine)
    df = df.to_dict('records')
    return df


@router.put("/put/{petrol_id}/{r_id}", response_model=Put)
async def put(petrol_id: str, r_id: str, put: Put, session: Session = Depends(get_db)):
    # session = Session()

    user = session.query(PetrolModel).filter(
        PetrolModel.petrol_id == petrol_id).first()
    for var in user.data_details:
        # print(var['r_id'])
        if var['r_id'] == r_id:
            var.update(dict(jia_yi_fang_b=put.car, product_id=put.petrol, 
                            qty=put.qty, che_liang_id=put.che_liang_id, price=put.price
                           ))
    flag_modified(user, 'data_details')
    session.commit()
    
    return JSONResponse(status_code=200, content={
        "status_code": 200,
        "message": "success"
    })
 

@router.put("/PutCarMotor/{petrol_id}/{r_id}")
async def PutCarMotor(petrol_id: str, r_id: str, put: PutCarMotor, session: Session = Depends(get_db)):
    # # session = Session()

    # # UPDATE
    # user = session.query(PetrolModel).filter(
    #     PetrolModel.petrol_id == petrol_id).first()
    # user.datetime_bill = put.datetime_bill
    # for var in user.data_details:
    #     # print(var['r_id'])
    #     if var['r_id'] == r_id:
    #         var.update(dict(jia_yi_fang_a=put.store, jia_yi_fang_b=put.car, driver=put.driver, product_id=put.petrol, 
    #                         qty=put.qty, price=put.price, distant=put.distant, che_liang_id=put.che_liang_id,
    #                         bi_zhi=put.currency, lei_a=put.lei_a, lei_b=put.lei_b, ke_bian=put.ke_bian, 
    #                         imageKm=put.imageKm, imageKm2=put.imageKm2, imageKmDate=put.imageKmDate, imageAccept=put.imageAccept,
    #                         imageBill=put.imageBill))
    # flag_modified(user, 'data_details')
    # session.commit()
    
    # # RETURN VALUE
    # get_petrol_id = user.petrol_id
    # get_id = user.data_details[0]["che_liang_id"]
    
    # # GET MEAN
    # # today = datetime.date.today()
    # # future_day = today.day
    # # future_month = (today.month - 3) % 12
    # # future_year = today.year + ((today.month - 3) // 12)
    # # six_months_ago = datetime.date(future_year, future_month, future_day) 
    # import datetime as DT
    # today = DT.date.today()
    # week_ago = today - DT.timedelta(days=90)
    # print(week_ago)
    # df20 = pd.read_sql(sql = session.query(PetrolModel)\
    #                         .with_entities(
    #                             PetrolModel.petrol_id,
    #                             PetrolModel.datetime,
    #                             PetrolModel.datetime_bill,
    #                             PetrolModel.data_details[0]['che_liang_id'],
    #                             PetrolModel.data_details[0]['distant'],
    #                             PetrolModel.data_details[0]['qty'],
    #                             PetrolModel.data_details[0]['driver'],
    #                             )
    #                             .filter(PetrolModel.datetime > week_ago).statement,
    #                 con = session.bind)
    # df20 = df20.rename(columns={
    #     "anon_1": 'che_liang_id',
    #     "anon_2": 'distant',
    #     "anon_3": 'qty',
    #     "anon_4": 'driver',
    #     })
    # # display("df20", df20)
    # df30 = pd.read_sql(sql = session.query(PetrolModel2)\
    #                         .with_entities(
    #                             PetrolModel2.petrol_id,
    #                             PetrolModel2.datetime,
    #                             PetrolModel2.data_details[0]['che_liang_id'],
    #                             PetrolModel2.data_details[0]['distant'],
    #                             PetrolModel2.data_details[0]['qty'],
    #                             )
    #                             .filter( PetrolModel2.datetime > week_ago).statement,
    #                 con = session.bind)
    # df30 = df30.rename(columns={
    #     "anon_1": 'che_liang_id',
    #     "anon_2": 'distant',
    #     "anon_3": 'qty',
    #     })
    # # # display("df30", df30)
    # df40 = pd.concat([df20, df30], ignore_index=True)
    # df40 = df40.sort_values(['datetime'], ascending=[True])
    # # # display(df40)
    # df50 = df40[df40['che_liang_id'] == get_id]

    # df50['previous_datetime_bill'] = df50['datetime_bill'].shift()
    # df50['diff_datetime_bill'] = (df50['datetime_bill'] - df50['previous_datetime_bill']).dt.days
    # df50['mean_datetime_bill'] = df50['diff_datetime_bill'].mean()
    # df50['percent_date_bill'] = df50.apply(lambda x: x.mean_datetime_bill and (x.diff_datetime_bill - x.mean_datetime_bill) / x.mean_datetime_bill * 100, axis=1)

    # df50['previous_distant'] = df50['distant'].shift()
    # df50['distance_used'] = df50.apply(lambda x: x['previous_distant'] if x['previous_distant'] == 0 else (x['distant'] if x['distant'] == 0 else x['distant'] - x['previous_distant']), axis=1)
    # df50['mean_distance_used'] = df50['distance_used'].mean()
    # df50['previous_qty'] = df50['qty'].shift()
    # df50['count'] = df50['distance_used']
    # df50['km_lit'] = (df50['distance_used'] / df50['previous_qty'])         
    # df50['average'] = df50[df50['km_lit'] != 0]["km_lit"].mean()
    # df50['average_percent'] = df50.apply(lambda x: x['km_lit'] if x['km_lit'] == 0 else x.mean_distance_used and ((x.distance_used - x.mean_distance_used) / x.mean_distance_used) * 100, axis=1)

    # outlier_detector = IsolationForest(random_state=42)
    # distance_used_fillna = df50[['km_lit']].fillna(0)
    # outlier_detector.fit(distance_used_fillna)
    # prediction = outlier_detector.predict(distance_used_fillna)
    # prediction_strings = ['Outlier' if x < 0 else 'Standard' for x in prediction]
    # df50['outlier_flag'] = prediction_strings
    
    # df50['km_lit_2'] = df50.apply(lambda x: None if x['outlier_flag'] == 'Outlier' else x['km_lit'], axis=1)
    # df50['average_2'] = df50[df50['km_lit_2'] != 0]["km_lit_2"].mean()
    # df50['mean_distance_used_2'] = (df50['average_2'] * df50['previous_qty']) + 0.01
    # df50['average_percent_2'] = df50.apply(lambda x: (x.distance_used - x.mean_distance_used_2) / x.mean_distance_used_2 * 100, axis=1)
    # df50['mean'] = ((df50.average_2 - df50.average) / df50.average) * 100

    # df60 = df50.pivot_table(columns=['distance_used'], aggfunc='size')
    # df60 = pd.DataFrame(df60)
    # df60 = df60.rename(columns={0: 'countoo'}).reset_index()
    # df50['count'] = df50[['count']].replace(to_replace =df60['distance_used'].to_list(), value =df60['countoo'].to_list())
    # qty_list = df50['qty'].astype(float).round(decimals = 1)
    # uniqleQtyToList = qty_list.unique().tolist()
    # df50['uniqleQty'] = str(uniqleQtyToList)
    # df50['FirstuniqleQty'] = str(uniqleQtyToList[0])
    # df50['LastuniqleQty'] = str(uniqleQtyToList[-1])
    # driver_list = df50['driver'].astype(float).round(decimals = 1)
    # driver_list = df50['driver'].dropna().unique().tolist()
    # df50['uniqleDriver'] = str(driver_list)
    # df50['FirstuniqleDriver'] = driver_list[0]
    # df50['LastuniqleDriver'] = driver_list[-1]

    # df50 = df50.fillna(0).round(decimals = 1)
    
    # # SELECT CURREN PETROLID
    # df70 = df50[df50['petrol_id'] == get_petrol_id]
    # df70 = df70.to_dict('records')
    # df70 = {"petrol_id": df70[0]['petrol_id'], "datetime": str(df70[0]['datetime']), "che_liang_id": df70[0]["che_liang_id"], "distant": df70[0]["distant"],
    #         "distance_used": df70[0]["distance_used"], "mean_distance_used": df70[0]["mean_distance_used"], "average_percent": df70[0]["average_percent"], "mean": df70[0]["mean"],
    #         "mean_distance_used_2": df70[0]["mean_distance_used_2"], "average_percent_2": df70[0]["average_percent_2"], "count": df70[0]["count"],
    #         "qty": df70[0]["qty"], "uniqleQty": df70[0]["uniqleQty"], "FirstuniqleQty": df70[0]["FirstuniqleQty"], "LastuniqleQty": df70[0]["LastuniqleQty"],
    #         "driver": df70[0]["driver"], "uniqleDriver": df70[0]["uniqleDriver"], "FirstuniqleDriver": df70[0]["FirstuniqleDriver"], "LastuniqleDriver": df70[0]["LastuniqleDriver"],
    #         "diff_datetime_bill": df70[0]["diff_datetime_bill"], "mean_datetime_bill": df70[0]["mean_datetime_bill"], "percent_date_bill": df70[0]["percent_date_bill"]
    #         }

    # # UPDATE INTO CALCULATE TABLE
    # user = session.query(Calculate).filter(Calculate.petrol_id == get_petrol_id).first()
    # print(user.calOne)
    # user.calOne.update(df70)
    # flag_modified(user, 'calOne')
    # session.commit()

    
    # return JSONResponse(status_code=200, content={
    #     "status_code": 200,
    #     "message": "success"
    # }) 
    return JSONResponse(status_code=200, content={
        "status_code": 200,
        "message": "success"
    })
 
   
   
@router.put("/put_distant/{petrol_id}/{r_id}", response_model=Put_distant)
async def put_distant(petrol_id: str, r_id: str, put: Put_distant, session: Session = Depends(get_db)):
    # session = Session()

    user = session.query(PetrolModel).filter(
        PetrolModel.petrol_id == petrol_id).first()
    for var in user.data_details:
        # print(var['r_id'])
        if var['r_id'] == r_id:
            var.update(dict(distant=put.distant))
    flag_modified(user, 'data_details')
    session.commit()
    
    return JSONResponse(status_code=200, content={
        "status_code": 200,
        "message": "success"
    })    


@router.put("/PutMasakBill/{petrol_id}/{r_id}", response_model=PutMasakBill)
async def PutMasakBill(petrol_id: str, r_id: str, put: PutMasakBill, session: Session = Depends(get_db)):
    # session = Session()

    user = session.query(PetrolModel).filter(
        PetrolModel.petrol_id == petrol_id).first()
    for var in user.data_details:
        # print(var['r_id'])
        if var['r_id'] == r_id:
            var.update(dict(fen_dian_id=put.fen_dian_id, user_id=put.user_id, 
                            warehouse_id=put.warehouse_id, jia_yi_fang_a=put.store, 
                            jia_yi_fang_b=put.car, product_id=put.petrol, 
                            qty=put.qty, che_liang_id=put.che_liang_id,
                            lei_a=put.lei_a, lei_b=put.lei_b))
    flag_modified(user, 'data_details')
    session.commit()
    
    return JSONResponse(status_code=200, content={
        "status_code": 200,
        "message": "success"
    }) 


@router.get("/Index_CarMoto")
async def Index_CarMoto(page_size: int = 10, page: int = 1, session: Session = Depends(get_db)):

    page -= 1
    # session = Session()
    # books = session.query(PetrolModel).order_by(PetrolModel.datetime.desc()) \
    # .filter(PetrolModel.data_details[0]['bill'].astext.cast(String) == "") \
    # .limit(page_size).offset(page*page_size).all()
    try:
        df20 = pd.read_sql(sql = session.query(PetrolModel).order_by(PetrolModel.datetime.desc()).limit(page_size).offset(page*page_size).statement, con = session.bind)
        df20 = df20.to_dict('records')
        df20 = pd.json_normalize(df20, "data_details", ["auto_id", "petrol_id", "datetime", "status_details"])
        df20 = df20.to_dict('records')
        df20 = pd.json_normalize(df20)
        df20 = df20.rename(columns={"status_details.status": 'status'})
        # df20 = df20.fillna("555555555555555555")
        df20[['bill']] = df20[['bill']].fillna(value="")
        df20[['user_id', 'fen_dian_id', 'warehouse_id']] = df20[['user_id','fen_dian_id', 'warehouse_id']].fillna(value=0)
        df20 = df20[df20['bill'] == "" ]
        df20 = df20.to_dict('records')
    except:
        df20 = []    
    # session.close()
    return df20


@router.get("/IndexData")
async def IndexData(fen_dian_id: int, getDate: str, session: Session = Depends(get_db)):

    # session = Session()
    get_date = getDate
    try:
        df20 = pd.read_sql(sql = session.query(PetrolModel).order_by(PetrolModel.datetime.desc())
                           .filter(cast(PetrolModel.datetime, Date) == get_date,)
                           .filter(PetrolModel.data_details[0]['fen_dian_id'].astext.cast(Integer) == fen_dian_id)
                           .filter(PetrolModel.data_details[0]['bill'].astext.cast(String) == "")
                           .statement, con = session.bind)
        df20 = df20.to_dict('records')
        df20 = pd.json_normalize(df20, "data_details", ["auto_id", "petrol_id", "datetime", "status_details"])
        df20 = df20.to_dict('records')
        df20 = pd.json_normalize(df20)
        df20 = df20.rename(columns={"status_details.status": 'status'})
        # df20[['bill']] = df20[['bill']].fillna(value="")
        df20[['user_id', 'fen_dian_id', 'warehouse_id']] = df20[['user_id','fen_dian_id', 'warehouse_id']].fillna(value=0)
        
        df30 = df20
        df30 = df30.loc[:, df30.columns.isin(['che_liang_id', 'petrol_id', 'product_id'])]
        df30 = df30.rename(columns={"che_liang_id": 'jia_yi_id'})
        # display('df30', df30)

        url = Arter_api + 'jia_yi_name_list_id'
        to_dict = df30.to_dict('records')
        body_raw = {"data_api": to_dict}
        df40 = requests.get(url=url, json=body_raw).json()
        df40 = pd.DataFrame(df40)
        # display('df40', df40)
        
        merge = df20.merge(df40, left_on='che_liang_id', right_on='jia_yi_id', how='left')
        # display('merge', merge)
    except:
        merge = pd.DataFrame()
        # merge = []    
    # session.close()
    return merge.fillna(value="").to_dict('records')


@router.get("/index_bill")
async def index_bill(fen_dian_id: int, getDate: str, session: Session = Depends(get_db)):
    # Session = sessionmaker(bind=engine)
    # session = Session()
    try:
        # today = datetime.date.today()
        # future_day = today.day
        # future_month = (today.month) % 12
        # future_year = today.year + ((today.month) // 12)
        # six_months_ago = datetime.date(future_year, future_month, future_day)
        # print(six_months_ago)
        # print(type(six_months_ago))
        get_date = getDate
        df20 = pd.read_sql(sql = session.query(PetrolModel)
                            .order_by(PetrolModel.datetime.desc())
                            .filter(
                                and_(
                                    # PetrolModel.datetime > six_months_ago,
                                    cast(PetrolModel.datetime, Date) == get_date,
                                    PetrolModel.data_details[0]['fen_dian_id'].astext.cast(Integer) == fen_dian_id
                                    )
                                )
                            .filter(
                                not_(
                                    PetrolModel.data_details[0]['bill'].astext.cast(String) == "",
                                    )
                                )
                            .statement, con = session.bind)
        df20['datetime_bill'] = pd.to_datetime(df20['datetime_bill']).dt.date
        df20 = df20.to_dict('records')
        # df20 = pd.json_normalize(df20, "data_details", ["auto_id", "petrol_id", "datetime"])
        df20 = pd.json_normalize(df20, "data_details", ["auto_id", "petrol_id", "datetime", "status_details", "datetime_bill"])
        df20 = df20.to_dict('records')
        df20 = pd.json_normalize(df20)
        df20 = df20.rename(columns={"status_details.status": 'status'})
        df20 = df20.fillna(0)
        # df20 = df20.to_dict('records')
        
        df30 = df20
        df30 = df30.loc[:, df30.columns.isin(['che_liang_id', 'petrol_id', 'product_id'])]
        df30 = df30.rename(columns={"che_liang_id": 'jia_yi_id'})
        # display('df30', df30)

        # url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'
        url = Arter_api + 'jia_yi_name_list_id'
        to_dict = df30.to_dict('records')
        body_raw = {"data_api": to_dict}
        df40 = requests.get(url=url, json=body_raw).json()
        df40 = pd.DataFrame(df40)
        # display('df40', df40)

        # url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/product_list_id'
        url = Arter_api + 'product_list_id'
        to_dict = df30.to_dict('records')
        body_raw = {"data_api": to_dict}
        df50 = requests.get(url=url, json=body_raw).json()
        df50 = pd.DataFrame(df50)
        # display('df50', df50)

        merge = df20.merge(df40, left_on='che_liang_id', right_on='jia_yi_id', how='left').merge(df50, on='product_id', how='left')
        merge = merge.to_dict('records')
        # display('merge', merge)
    except:
        print("An exception occurred") 
        merge = "No list"   
    # session.close()
    return merge




    
# @router.get("/contentData/{petrol_id}")
# async def contentData(petrol_id: str, session: Session = Depends(get_db)):

#     day = pd.DataFrame({'day':[pd.to_datetime("today")]})
#     day["day"] = day["day"].dt.strftime("%Y-%m-%d")
#     day = day.to_string().replace("          day\n0  ","")
#     # display("day", day)


#     day2 = pd.DataFrame({'day2':[pd.to_datetime("today")]})
#     day2["day2"] = day2["day2"].dt.strftime("%Y-%m-%d %H:%M:%S")
#     day2 = day2.to_string().replace("                  day2\n0  ","")
#     # display("day2", day2)


#     day3 = pd.DataFrame({'day3':[pd.to_datetime("today")]})
#     day3["day3"] = day3["day3"].dt.strftime("%Y-%m-%d 12:05:00")
#     day3 = day3.to_string().replace("                  day3\n0  ","")
#     # display("day3", day3)


#     yesterday = pd.DataFrame({'yesterday':[pd.to_datetime("today").normalize() - timedelta(1)]})
#     yesterday = yesterday.to_string().replace("   yesterday\n0 ","")
#     # display("yesterday", yesterday)
    
    
#     auto_id = petrol_id
#     sql = """SELECT  * from petrol where petrol_id = %s"""
#     database = pd.read_sql_query(sql, engine, params=[auto_id])
#     to_record = database.to_dict('records')
#     json_nor = pd.json_normalize(to_record, "data_details", ["petrol_id", "datetime", "status_details"])
    

#     df1 = json_nor
#     df1 = df1.loc[:, df1.columns.isin(['jia_yi_fang_a', 'petrol_id'])]
#     df1 = df1.rename(columns={"jia_yi_fang_a": 'jia_yi_id'})
#     df1 = pd.DataFrame(df1)
#     # display('df1', df1)
#     try: 
#         # url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'
#         url = f'{pv_api}/mongodb_data_api/api/v2/search/jia_yi_name_list_id'
#         to_dict = df1.to_dict('records')
#         body_raw = {"data_api": to_dict}
#         df13 = requests.get(url=url, json=body_raw).json()
#         df13 = pd.DataFrame(df13)
#         # display('df13', df13)
#     except:
#         d = {'jia_yi_id': [0], 'jia_yi_idname': [""], 'jia_yi_mm_name': [""]}
#         df13 = pd.DataFrame(d)
#         # display('df13', df13)
#     merge_store = pd.merge(df1, df13, on='jia_yi_id', how='left')
#     # display('merge_store', merge_store)


#     df2 = json_nor
#     df2 = df2.loc[:, df2.columns.isin(['jia_yi_fang_b', 'petrol_id'])]
#     df2 = df2.rename(columns={"jia_yi_fang_b": 'jia_yi_id'})
#     df2 = pd.DataFrame(df2)
#     # display('df2', df2)
#     # url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'
#     url = Arter_api + 'jia_yi_name_list_id'
#     to_dict = df2.to_dict('records')
#     body_raw = {"data_api": to_dict}
#     df23 = requests.get(url=url, json=body_raw).json()
#     df23 = pd.DataFrame(df23)
#     # display('df23', df23)
#     merge_car = pd.merge(df2, df23, on='jia_yi_id', how='left')
#     # display('merge_car', merge_car)


#     df21 = json_nor
#     df21 = df21.loc[:, df21.columns.isin(['driver', 'petrol_id'])]
#     df21 = df21.rename(columns={"driver": 'jia_yi_id'})
#     df21 = pd.DataFrame(df21)
#     # display('df21', df21)
#     try:
#         # url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'
#         url = f'{pv_api}/mongodb_data_api/api/v2/search/jia_yi_name_list_id'
#         # url = Arter_api + 'jia_yi_name_list_id'
#         to_dict = df21.to_dict('records')
#         body_raw = {"data_api": to_dict}
#         df231 = requests.get(url=url, json=body_raw).json()
#         df231 = pd.DataFrame(df231)
#         merge_driver = pd.merge(df21, df231, on='jia_yi_id', how='left')
#         merge_driver = merge_driver.rename(columns=dict(jia_yi_id='driverJia_yi_id', 
#                                     jia_yi_idname='driverJia_yi_idname_x',
#                                     jia_yi_mm_name='driverJia_yi_mm_name_x',
#                                     ))
#     except:
#         d = {'jia_yi_id': [0], 'petrol_id': merge_car['petrol_id'], 'jia_yi_idname': [""], 'jia_yi_mm_name': [""], "ri_qi": [""]}
#         df231 = pd.DataFrame(d)
#         merge_driver = df231.rename(columns=dict(jia_yi_id='driverJia_yi_id', 
#                                     jia_yi_idname='driverJia_yi_idname_x',
#                                     jia_yi_mm_name='driverJia_yi_mm_name_x',
#                                     ))
#     # display('df231', df231)
    
#     # display('merge_driver', merge_driver)


#     df3 = json_nor
#     df3 = df3.loc[:, df3.columns.isin(['product_id', 'petrol_id'])]
#     df3 = pd.DataFrame(df3)
#     # display('df3', df3)
#     # url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/product_list_id'
#     url = Arter_api + 'product_list_id'
#     to_dict = df3.to_dict('records')
#     body_raw = {"data_api": to_dict}
#     df33 = requests.get(url=url, json=body_raw).json()
#     df33 = pd.DataFrame(df33)
#     # display('df33', df33)
#     merge_petrol = pd.merge(df3, df33, on='product_id', how='left')
#     # display('merge_petrol', merge_petrol)


#     df4 = json_nor
#     df4 = df4.loc[:, df4.columns.isin(['bi_zhi', 'petrol_id'])]
#     # display('df4', df4)
#     df42 = pd.read_sql_table('bi_zhi', engine)
#     df42 = df42.loc[:, ~df42.columns.isin(['auto_id'])]
#     # # display('df42', df42)
#     merge_bi_zhi = pd.merge(df4, df42, on='bi_zhi', how='left')
#     # display('merge_bi_zhi', merge_bi_zhi)


#     df5 = json_nor
#     df5 = df5.loc[:, df5.columns.isin(['lei_a', 'ke_bian', 'petrol_id'])]
#     df5 = df5.rename(columns={"ke_bian": 'jia_yi_id'})
#     df5 = pd.DataFrame(df5)
#     # display('df5', df5)
#     df52 = pd.read_sql_table('lei_type', engine)
#     df52 = df52.loc[:, ~df52.columns.isin(['auto_id', 'data_sub'])]
#     df52 = pd.DataFrame(df52)
#     # display('df52', df52)
#     # url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'
#     url = Arter_api + 'jia_yi_name_list_id'
#     to_dict = df5.to_dict('records')
#     body_raw = {"data_api": to_dict}
#     df53 = requests.get(url=url, json=body_raw).json()
#     df53 = [{"jia_yi_id": df53}]
#     new_value = []
#     for data in df53:
#         if data['jia_yi_id'] == False:
#             new_value.append(data)
#         else:
#             new_value.extend(data['jia_yi_id'])
#     df53 = pd.DataFrame(new_value)
#     # display('df53', df53)
#     merge_lei_a_ke_bian = df5.merge(df52, on='lei_a', how='left').merge(df53, on='jia_yi_id', how='left')
#     # display('merge_lei_a_ke_bian', merge_lei_a_ke_bian)


#     data = json_nor
#     data = data.loc[:, ~data.columns.isin(['jia_yi_fang_a', 'jia_yi_fang_b', 'product_id', 'bi_zhi', 'lei_a', 'lei_b', 'ke_bian'])]
#     data = pd.DataFrame(data)
#     # display('data', data)
#     merge = [data, merge_store, merge_car, merge_petrol, merge_bi_zhi, merge_lei_a_ke_bian, merge_driver]
#     merge = reduce(lambda left,right: pd.merge(left,right,on='petrol_id'), merge)
#     merge = merge.rename(columns=dict(jia_yi_id_x='storeJia_yi_id', 
#                                     jia_yi_idname_x='storeJia_yi_idname_x',
#                                     jia_yi_mm_name_x='storeJia_yi_mm_name_x',
#                                     jia_yi_id_y='carJia_yi_id', 
#                                     jia_yi_idname_y='carJia_yi_idname_x',
#                                     jia_yi_mm_name_y='carJia_yi_mm_name_x',
#                                     product_id='petrolProduct_id',
#                                     product_idname='petrolProduct_idname',
#                                     product_mm_name='petrolProduct_mm_name', 
#                                     product_d_name='petrolProduct_d_name',
#                                     th_name='petrolTh_name',
#                                     idname='bi_zhiIdname',
#                                     jia_yi_id='ke_bianJia_yi_id',
#                                     jia_yi_idname='ke_bianJia_yi_idname',
#                                     jia_yi_mm_name='ke_bianJia_yi_mm_name'
#                                     ))
#     merge["datetime"] = merge['datetime'].dt.strftime("%Y-%m-%d")
#     if day == merge["datetime"].all():
#         print("day", day, merge["datetime"])
#         merge["status_update"] = True
#     elif yesterday == merge["datetime"].all():
#         print("yesterday", yesterday, merge["datetime"])
#         if day2 < day3:
#             print("less up", day2, day3)
#             merge["status_update"] = True
#         else:
#             print("more no", day2, day3)
#             merge["status_update"] = False    
#     else:
#         print("else")
#         merge["status_update"] = False
#     # Cal_id = pd.DataFrame(merge)
#     # merge = merge.to_dict('records')
#     # display('merge', merge)
    
#     # session = Session()
#     # today = datetime.date.today()
#     # future_day = today.day - 1
#     # future_month = (today.month - 3) % 12
#     # future_year = today.year + ((today.month - 3) // 12)
#     # six_months_ago = datetime.date(future_year, future_month, future_day)
#     # print(future_day, future_month, future_year, six_months_ago)

#     import datetime as DT
#     today = DT.date.today()
#     week_ago = today - DT.timedelta(days=90)
#     print(week_ago)
    
#     df20 = pd.read_sql(sql = session.query(PetrolModel)\
#                             .with_entities(
#                                 PetrolModel.petrol_id,
#                                 PetrolModel.datetime,
#                                 PetrolModel.datetime_bill,
#                                 PetrolModel.data_details[0]['che_liang_id'],
#                                 PetrolModel.data_details[0]['distant'],
#                                 PetrolModel.data_details[0]['qty'],
#                                 )
#                                 .filter(PetrolModel.datetime > week_ago).statement,
#                     con = session.bind)
#     df20 = df20.rename(columns={
#         "anon_1": 'che_liang_id',
#         "anon_2": 'distant',
#         "anon_3": 'qty',
#         })

#     # display("df20", df20)

#     df30 = pd.read_sql(sql = session.query(PetrolModel2)\
#                             .with_entities(
#                                 PetrolModel2.petrol_id,
#                                 PetrolModel2.datetime,
#                                 PetrolModel2.data_details[0]['che_liang_id'],
#                                 PetrolModel2.data_details[0]['distant'],
#                                 PetrolModel2.data_details[0]['qty'],
#                                 )
#                                 .filter( PetrolModel2.datetime > week_ago).statement,
#                     con = session.bind)
#     df30 = df30.rename(columns={
#         "anon_1": 'che_liang_id',
#         "anon_2": 'distant',
#         "anon_3": 'qty',
#         })

#     # display("df30", df30)

#     df40 = pd.concat([df20, df30], ignore_index=True)
#     df40 = df40.sort_values(['datetime'], ascending=[True])
#     # display(df40)

#     Ca_id = merge.to_dict('records')
#     df50 = df40[df40['che_liang_id'] == Ca_id[0]['che_liang_id']]
#     df50['datetime_bill'] = pd.to_datetime(df50['datetime_bill']).dt.date
#     df50['previous_distant'] = df50['distant'].shift()
#     df50['distance_used'] = df50.apply(lambda x: x['previous_distant'] if x['previous_distant'] == 0 else (x['distant'] if x['distant'] == 0 else x['distant'] - x['previous_distant']), axis=1)
#     df50['mean_distance_used'] = df50['distance_used'].mean()
#     df50['previous_qty'] = df50['qty'].shift()
#     df50['count'] = df50['distance_used']
#     df50['km_lit'] = (df50['distance_used'] / df50['previous_qty'])         
#     df50['average'] = df50[df50['km_lit'] != 0]["km_lit"].mean()
#     df50['average_percent'] = df50.apply(lambda x: x['km_lit'] if x['km_lit'] == 0 else x.mean_distance_used and ((x.distance_used - x.mean_distance_used) / x.mean_distance_used) * 100, axis=1)

#     outlier_detector = IsolationForest(random_state=42)
#     distance_used_fillna = df50[['km_lit']].fillna(0)
#     outlier_detector.fit(distance_used_fillna)
#     prediction = outlier_detector.predict(distance_used_fillna)
#     prediction_strings = ['Outlier' if x < 0 else 'Standard' for x in prediction]
#     df50['outlier_flag'] = prediction_strings
    
#     df50['km_lit_2'] = df50.apply(lambda x: None if x['outlier_flag'] == 'Outlier' else x['km_lit'], axis=1)
#     df50['average_2'] = df50[df50['km_lit_2'] != 0]["km_lit_2"].mean()
#     df50['mean_distance_used_2'] = (df50['average_2'] * df50['previous_qty']) + 0.01
#     df50['average_percent_2'] = df50.apply(lambda x: (x.distance_used - x.mean_distance_used_2) / x.mean_distance_used_2 * 100, axis=1)
#     df50['mean'] = ((df50.average_2 - df50.average) / df50.average) * 100

#     df60 = df50.pivot_table(columns=['distance_used'], aggfunc='size')
#     df60 = pd.DataFrame(df60)
#     df60 = df60.rename(columns={0: 'countoo'}).reset_index()
#     df50['count'] = df50[['count']].replace(to_replace =df60['distance_used'].to_list(), value =df60['countoo'].to_list())
#     df50 = df50.fillna(0).round(decimals = 1)
#     # df50 = df50.to_dict('records')
     
#     df70 = merge.merge(df50, on='petrol_id', how='inner')
#     df70 = df70.rename(columns={
#         "qty_x": 'qty',
#         "distant_x": 'distant',
#         "che_liang_id_x": 'che_liang_id',
#         "datetime_x": 'datetime',
#         "distant_x": 'distant',
#     })
    
#     df70 = df70.to_dict('records')
#     return df70

@router.get("/contentData/{petrol_id}")
async def contentData(petrol_id: str, session: Session = Depends(get_db)):

    day = pd.DataFrame({'day':[pd.to_datetime("today")]})
    day["day"] = day["day"].dt.strftime("%Y-%m-%d")
    day = day.to_string().replace("          day\n0  ","")
    # display("day", day)


    day2 = pd.DataFrame({'day2':[pd.to_datetime("today")]})
    day2["day2"] = day2["day2"].dt.strftime("%Y-%m-%d %H:%M:%S")
    day2 = day2.to_string().replace("                  day2\n0  ","")
    # display("day2", day2)


    day3 = pd.DataFrame({'day3':[pd.to_datetime("today")]})
    day3["day3"] = day3["day3"].dt.strftime("%Y-%m-%d 12:05:00")
    day3 = day3.to_string().replace("                  day3\n0  ","")
    # display("day3", day3)


    yesterday = pd.DataFrame({'yesterday':[pd.to_datetime("today").normalize() - timedelta(1)]})
    yesterday = yesterday.to_string().replace("   yesterday\n0 ","")
    # display("yesterday", yesterday)
    
    
    auto_id = petrol_id
    sql = """SELECT  * from petrol where petrol_id = %s"""
    database = pd.read_sql_query(sql, engine, params=[auto_id])
    to_record = database.to_dict('records')
    json_nor = pd.json_normalize(to_record, "data_details", ["petrol_id", "datetime", "status_details"])
    

    df1 = json_nor
    df1 = df1.loc[:, df1.columns.isin(['jia_yi_fang_a', 'petrol_id'])]
    df1 = df1.rename(columns={"jia_yi_fang_a": 'jia_yi_id'})
    df1 = pd.DataFrame(df1)
    # display('df1', df1)
    try: 
        # url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'
        url = f'{pv_api}/mongodb_data_api/api/v2/search/jia_yi_name_list_id'
        to_dict = df1.to_dict('records')
        body_raw = {"data_api": to_dict}
        df13 = requests.get(url=url, json=body_raw).json()
        df13 = pd.DataFrame(df13)
        # display('df13', df13)
    except:
        d = {'jia_yi_id': [0], 'jia_yi_idname': [""], 'jia_yi_mm_name': [""]}
        df13 = pd.DataFrame(d)
        # display('df13', df13)
    merge_store = pd.merge(df1, df13, on='jia_yi_id', how='left')
    # display('merge_store', merge_store)


    df2 = json_nor
    df2 = df2.loc[:, df2.columns.isin(['jia_yi_fang_b', 'petrol_id'])]
    df2 = df2.rename(columns={"jia_yi_fang_b": 'jia_yi_id'})
    df2 = pd.DataFrame(df2)
    # display('df2', df2)
    # url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'
    url = Arter_api + 'jia_yi_name_list_id'
    to_dict = df2.to_dict('records')
    body_raw = {"data_api": to_dict}
    df23 = requests.get(url=url, json=body_raw).json()
    df23 = pd.DataFrame(df23)
    # display('df23', df23)
    merge_car = pd.merge(df2, df23, on='jia_yi_id', how='left')
    # display('merge_car', merge_car)


    df21 = json_nor
    df21 = df21.loc[:, df21.columns.isin(['driver', 'petrol_id'])]
    df21 = df21.rename(columns={"driver": 'jia_yi_id'})
    df21 = pd.DataFrame(df21)
    # display('df21', df21)
    try:
        # url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'
        url = f'{pv_api}/mongodb_data_api/api/v2/search/jia_yi_name_list_id'
        # url = Arter_api + 'jia_yi_name_list_id'
        to_dict = df21.to_dict('records')
        body_raw = {"data_api": to_dict}
        df231 = requests.get(url=url, json=body_raw).json()
        df231 = pd.DataFrame(df231)
        merge_driver = pd.merge(df21, df231, on='jia_yi_id', how='left')
        merge_driver = merge_driver.rename(columns=dict(jia_yi_id='driverJia_yi_id', 
                                    jia_yi_idname='driverJia_yi_idname_x',
                                    jia_yi_mm_name='driverJia_yi_mm_name_x',
                                    ))
    except:
        d = {'jia_yi_id': [0], 'petrol_id': merge_car['petrol_id'], 'jia_yi_idname': [""], 'jia_yi_mm_name': [""], "ri_qi": [""]}
        df231 = pd.DataFrame(d)
        merge_driver = df231.rename(columns=dict(jia_yi_id='driverJia_yi_id', 
                                    jia_yi_idname='driverJia_yi_idname_x',
                                    jia_yi_mm_name='driverJia_yi_mm_name_x',
                                    ))
    # display('df231', df231)
    
    # display('merge_driver', merge_driver)


    df3 = json_nor
    df3 = df3.loc[:, df3.columns.isin(['product_id', 'petrol_id'])]
    df3 = pd.DataFrame(df3)
    # display('df3', df3)
    # url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/product_list_id'
    url = Arter_api + 'product_list_id'
    to_dict = df3.to_dict('records')
    body_raw = {"data_api": to_dict}
    df33 = requests.get(url=url, json=body_raw).json()
    df33 = pd.DataFrame(df33)
    # display('df33', df33)
    merge_petrol = pd.merge(df3, df33, on='product_id', how='left')
    # display('merge_petrol', merge_petrol)


    df4 = json_nor
    df4 = df4.loc[:, df4.columns.isin(['bi_zhi', 'petrol_id'])]
    # display('df4', df4)
    df42 = pd.read_sql_table('bi_zhi', engine)
    df42 = df42.loc[:, ~df42.columns.isin(['auto_id'])]
    # # display('df42', df42)
    merge_bi_zhi = pd.merge(df4, df42, on='bi_zhi', how='left')
    # display('merge_bi_zhi', merge_bi_zhi)


    df5 = json_nor
    df5 = df5.loc[:, df5.columns.isin(['lei_a', 'ke_bian', 'petrol_id'])]
    df5 = df5.rename(columns={"ke_bian": 'jia_yi_id'})
    df5 = pd.DataFrame(df5)
    # display('df5', df5)
    df52 = pd.read_sql_table('lei_type', engine)
    df52 = df52.loc[:, ~df52.columns.isin(['auto_id', 'data_sub'])]
    df52 = pd.DataFrame(df52)
    # display('df52', df52)
    # url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'
    url = Arter_api + 'jia_yi_name_list_id'
    to_dict = df5.to_dict('records')
    body_raw = {"data_api": to_dict}
    df53 = requests.get(url=url, json=body_raw).json()
    df53 = [{"jia_yi_id": df53}]
    new_value = []
    for data in df53:
        if data['jia_yi_id'] == False:
            new_value.append(data)
        else:
            new_value.extend(data['jia_yi_id'])
    df53 = pd.DataFrame(new_value)
    # display('df53', df53)
    merge_lei_a_ke_bian = df5.merge(df52, on='lei_a', how='left').merge(df53, on='jia_yi_id', how='left')
    # display('merge_lei_a_ke_bian', merge_lei_a_ke_bian)


    data = json_nor
    data = data.loc[:, ~data.columns.isin(['jia_yi_fang_a', 'jia_yi_fang_b', 'product_id', 'bi_zhi', 'lei_a', 'lei_b', 'ke_bian'])]
    data = pd.DataFrame(data)
    # display('data', data)
    merge = [data, merge_store, merge_car, merge_petrol, merge_bi_zhi, merge_lei_a_ke_bian, merge_driver]
    merge = reduce(lambda left,right: pd.merge(left,right,on='petrol_id'), merge)
    merge = merge.rename(columns=dict(jia_yi_id_x='storeJia_yi_id', 
                                    jia_yi_idname_x='storeJia_yi_idname_x',
                                    jia_yi_mm_name_x='storeJia_yi_mm_name_x',
                                    jia_yi_id_y='carJia_yi_id', 
                                    jia_yi_idname_y='carJia_yi_idname_x',
                                    jia_yi_mm_name_y='carJia_yi_mm_name_x',
                                    product_id='petrolProduct_id',
                                    product_idname='petrolProduct_idname',
                                    product_mm_name='petrolProduct_mm_name', 
                                    product_d_name='petrolProduct_d_name',
                                    th_name='petrolTh_name',
                                    idname='bi_zhiIdname',
                                    jia_yi_id='ke_bianJia_yi_id',
                                    jia_yi_idname='ke_bianJia_yi_idname',
                                    jia_yi_mm_name='ke_bianJia_yi_mm_name'
                                    ))
    merge["datetime"] = merge['datetime'].dt.strftime("%Y-%m-%d")
    if day == merge["datetime"].all():
        print("day", day, merge["datetime"])
        merge["status_update"] = True
    elif yesterday == merge["datetime"].all():
        print("yesterday", yesterday, merge["datetime"])
        if day2 < day3:
            print("less up", day2, day3)
            merge["status_update"] = True
        else:
            print("more no", day2, day3)
            merge["status_update"] = False    
    else:
        print("else")
        merge["status_update"] = False
    # Cal_id = pd.DataFrame(merge)
    # merge = merge.to_dict('records')
    # display('merge', merge)
    
    # session = Session()
    # today = datetime.date.today()
    # future_day = today.day - 1
    # future_month = (today.month - 3) % 12
    # future_year = today.year + ((today.month - 3) // 12)
    # six_months_ago = datetime.date(future_year, future_month, future_day)
    # print(future_day, future_month, future_year, six_months_ago)

    import datetime as DT
    today = DT.date.today()
    week_ago = today - DT.timedelta(days=90)
    print(week_ago)
    
    df20 = pd.read_sql(sql = session.query(PetrolModel)\
                            .with_entities(
                                PetrolModel.petrol_id,
                                PetrolModel.datetime,
                                PetrolModel.datetime_bill,
                                PetrolModel.data_details[0]['che_liang_id'],
                                PetrolModel.data_details[0]['distant'],
                                PetrolModel.data_details[0]['qty'],
                                PetrolModel.data_details[0]['imageKm'],
                                )
                                .filter(PetrolModel.datetime > week_ago).statement,
                    con = session.bind)
    df20 = df20.rename(columns={
        "anon_1": 'che_liang_id',
        "anon_2": 'distant',
        "anon_3": 'qty',
        "anon_4": 'imageKm',
        })

    # print("df20", df20)

    df30 = pd.read_sql(sql = session.query(PetrolModel2)\
                            .with_entities(
                                PetrolModel2.petrol_id,
                                PetrolModel2.datetime,
                                PetrolModel2.data_details[0]['che_liang_id'],
                                PetrolModel2.data_details[0]['distant'],
                                PetrolModel2.data_details[0]['qty'],
                                )
                                .filter( PetrolModel2.datetime > week_ago).statement,
                    con = session.bind)
    df30 = df30.rename(columns={
        "anon_1": 'che_liang_id',
        "anon_2": 'distant',
        "anon_3": 'qty',
        })

    # display("df30", df30)

    df40 = pd.concat([df20, df30], ignore_index=True)
    df40 = df40.sort_values(['datetime'], ascending=[True])
    # display(df40)

    Ca_id = merge.to_dict('records')
    df50 = df40[df40['che_liang_id'] == Ca_id[0]['che_liang_id']]
    
    try:
        df51 = df50.iloc[-2]['imageKm']
        print(df51, type(df51))
    except IndexError:
        # Handle the case where there is no second-to-last row
        df51 = "None"  # or provide a default value
        print("No second-to-last row found.")
        print(df51, type(df51))

    df50['datetime_bill'] = pd.to_datetime(df50['datetime_bill']).dt.date
    df50['previous_distant'] = df50['distant'].shift()
    df50['distance_used'] = df50.apply(lambda x: x['previous_distant'] if x['previous_distant'] == 0 else (x['distant'] if x['distant'] == 0 else x['distant'] - x['previous_distant']), axis=1)
    df50['mean_distance_used'] = df50['distance_used'].mean()
    df50['previous_qty'] = df50['qty'].shift()
    df50['count'] = df50['distance_used']
    df50['km_lit'] = (df50['distance_used'] / df50['previous_qty'])         
    df50['average'] = df50[df50['km_lit'] != 0]["km_lit"].mean()
    df50['average_percent'] = df50.apply(lambda x: x['km_lit'] if x['km_lit'] == 0 else x.mean_distance_used and ((x.distance_used - x.mean_distance_used) / x.mean_distance_used) * 100, axis=1)

    outlier_detector = IsolationForest(random_state=42)
    distance_used_fillna = df50[['km_lit']].fillna(0)
    outlier_detector.fit(distance_used_fillna)
    prediction = outlier_detector.predict(distance_used_fillna)
    prediction_strings = ['Outlier' if x < 0 else 'Standard' for x in prediction]
    df50['outlier_flag'] = prediction_strings
    
    df50['km_lit_2'] = df50.apply(lambda x: None if x['outlier_flag'] == 'Outlier' else x['km_lit'], axis=1)
    df50['average_2'] = df50[df50['km_lit_2'] != 0]["km_lit_2"].mean()
    df50['mean_distance_used_2'] = (df50['average_2'] * df50['previous_qty']) + 0.01
    df50['average_percent_2'] = df50.apply(lambda x: (x.distance_used - x.mean_distance_used_2) / x.mean_distance_used_2 * 100, axis=1)
    df50['mean'] = ((df50.average_2 - df50.average) / df50.average) * 100

    df60 = df50.pivot_table(columns=['distance_used'], aggfunc='size')
    df60 = pd.DataFrame(df60)
    df60 = df60.rename(columns={0: 'countoo'}).reset_index()
    df50['count'] = df50[['count']].replace(to_replace =df60['distance_used'].to_list(), value =df60['countoo'].to_list())
    df50 = df50.fillna(0).round(decimals = 1)
    # df50 = df50.to_dict('records')
     
    df70 = merge.merge(df50, on='petrol_id', how='inner')
    df70 = df70.rename(columns={
        "qty_x": 'qty',
        "distant_x": 'distant',
        "che_liang_id_x": 'che_liang_id',
        "datetime_x": 'datetime',
        "distant_x": 'distant',
        "imageKm_x": 'imageKm'
    })
    df70['imageKmPrevious'] = df51
    
    df70 = df70.to_dict('records')  
    return df70


@router.get("/getStore")
async def getStore():
    df = pd.read_sql_table('car_type', engine)
    df = df.to_dict('records')
    df = pd.json_normalize(df)
    df = df.rename(columns={"data_sub.type_name": 'data_sub_type_name'})
    df = df.loc[df['data_sub_type_name'] == "partner_id"]
    df = df.loc[:, df.columns.isin(['jia_yi_id'])]
    df = df.to_json(orient='records')
    df = json.loads(df)
    
    # url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'
    url = Arter_api + 'jia_yi_name_list_id'
    # body_raw = {"data_api": [{"jia_yi_id": 22904}]}
    body_raw = {"data_api": df}
    df2 = requests.get(url=url, json=body_raw)
    df2 = df2.json()
    df2 = pd.DataFrame(df2)
    df2 = df2.to_dict('records')
    return df2

@router.get("/getCar")
async def getCar():
    df = pd.read_sql_table('car_type', engine)
    df = df.to_dict('records')
    df = pd.json_normalize(df)
    df = df.rename(columns={"data_sub.type_name": 'data_sub_type_name'})
    df = df.loc[df['data_sub_type_name'] == "car_id"]
    df = df.loc[:, df.columns.isin(['jia_yi_id'])]
    df = df.to_json(orient='records')
    df = json.loads(df)
    
    # url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'
    url = Arter_api + 'jia_yi_name_list_id'
    # body_raw = {"data_api": [{"jia_yi_id": 22904}]}
    body_raw = {"data_api": df}
    df2 = requests.get(url=url, json=body_raw)
    df2 = df2.json()
    df2 = pd.DataFrame(df2)
    df2 = df2.to_dict('records')
    return df2

@router.get("/getDriver")
async def getDriver():
    df = pd.read_sql_table('car_type', engine)
    df = df.to_dict('records')
    df = pd.json_normalize(df)
    df = df.rename(columns={"data_sub.type_name": 'data_sub_type_name'})
    df = df.loc[df['data_sub_type_name'] == "driver_id"]
    df = df.loc[:, df.columns.isin(['jia_yi_id'])]
    df = df.to_json(orient='records')
    df = json.loads(df)
    
    # url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'
    url = Arter_api + 'jia_yi_name_list_id'
    # body_raw = {"data_api": [{"jia_yi_id": 22904}]}
    body_raw = {"data_api": df}
    df2 = requests.get(url=url, json=body_raw)
    df2 = df2.json()
    df2 = pd.DataFrame(df2)
    df2 = df2.to_dict('records')
    return df2

@router.get("/getPetrol")
async def getPetrol():
    df = pd.read_sql_table('car_type', engine)
    df = df.to_dict('records')
    df = pd.json_normalize(df)
    df = df.rename(columns={"jia_yi_id": "product_id", "data_sub.type_name": 'data_sub_type_name'})
    df = df.loc[df['data_sub_type_name'] == "petrol_id"]
    df = df.loc[:, df.columns.isin(['product_id'])]
    df = df.to_json(orient='records')
    df = json.loads(df)
    
    # url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/product_list_id'
    url = Arter_api + 'product_list_id'
    # body_raw = {"data_api": [{"jia_yi_id": 22904}]}
    body_raw = {"data_api": df}
    df2 = requests.get(url=url, json=body_raw)
    df2 = df2.json()
    df2 = pd.DataFrame(df2)
    df2 = df2.to_dict('records')
    return df2
    

@router.post("/check_km")
async def check_km(getIn: check_km, session: Session = Depends(get_db)):
    # session = Session()
    df10 = pd.read_sql(sql = session.query(PetrolModel)\
                        .with_entities(
                            PetrolModel.petrol_id,
                            PetrolModel.datetime,
                            PetrolModel.data_details[0]['che_liang_id'],
                            PetrolModel.data_details[0]['distant']
                            )
                        .filter(PetrolModel.data_details[0]['che_liang_id'].astext.cast(Integer) == getIn.che_liang_id)
                        .order_by(PetrolModel.datetime.desc())
                        # .distinct(PetrolModel.data_details[0]['che_liang_id'])
                        .limit(2)
                        .statement,
                con = session.bind)
    df10 = df10.rename(columns={
        "anon_1": 'che_liang_id', "anon_2": 'distant',
    })
    try:
        if int(df10.to_dict('records')[0]['distant']) > int(df10.to_dict('records')[1]['distant']):
            ans = "goInsert"
        elif int(df10.to_dict('records')[0]['distant']) == 0 and int(df10.to_dict('records')[1]['distant']) == 0:    
            ans = "goInsert"
        else: 
            ans = str(df10.to_dict('records')[1]['distant']) + " | " + str(df10.to_dict('records')[0]['distant'])
    except: 
        ans = "goInsert"     
    return ans    


@router.post("/formSubmit")
async def formSubmit(post: formSubmit, session: Session = Depends(get_db)):
    # session = Session()
    try:
        df20 = pd.read_sql(sql = session.query(PetrolModel)\
                            .with_entities(
                                PetrolModel.petrol_id,
                                PetrolModel.datetime,
                                PetrolModel.datetime_bill,
                                PetrolModel.data_details[0]['che_liang_id'],
                                PetrolModel.data_details[0]['distant']
                                )
                                .filter(PetrolModel.data_details[0]['che_liang_id'].astext.cast(Integer) == post.che_liang_id)
                                .order_by(PetrolModel.datetime.desc())
                                .limit(1)
                                .statement,
                    con = session.bind)
        df20 = df20.rename(columns={
            "anon_1": 'che_liang_id',
            "anon_2": 'distant'
            })
        # display("df20", df20)
        df20['datetime'] = pd.to_datetime(df20['datetime']).dt.date
        df20['datetime_bill_match'] = df20['datetime_bill'].apply(lambda x: 'Match' if x == post.datetime_bill else 'Mismatch')
        df20['today'] = pd.to_datetime("today").strftime("%Y-%m-%d")
        df20['dateCheck'] = df20.apply(lambda x: True if str(x['datetime']) == str(x['today']) else False, axis=1)
        # print(df20)

        if df20['dateCheck'].bool() == True and df20['datetime_bill_match'].item() == 'Match':
            print("Match")
            df70 = 'Match_datetime_bill'
        else:
            print("not Match")
            df70 = 'not_Match_datetime_bill'
            
            # FIRST INSERT
            a, b, c = await get_bookszxzx()
            db_user = PetrolModel(
                datetime=c,
                data_details=[
                    dict(sub_id=b, r_id=a, fen_dian_id=post.fen_dian_id, user_id=post.user_id, warehouse_id=post.warehouse_id, u_id=post.u_id, jia_yi_fang_a=post.store, 
                        jia_yi_fang_b=post.car, driver=post.driver, product_id=post.petrol, qty=post.qty, 
                        price=post.price, distant=post.distant, che_liang_id=post.che_liang_id,
                        bi_zhi=post.currency, lei_a=post.lei_a, lei_b=post.lei_b, ke_bian=post.ke_bian, bill=post.bill
                        )],
                status_details = dict(status="waiting"),
                update_time=c,
                datetime_bill=post.datetime_bill
            )
            session.add(db_user)
            session.commit()
            session.refresh(db_user)
            # session.close()
            
            # RETURN VALUE
            get_petrol_id = db_user.petrol_id
            get_id = db_user.data_details[0]["che_liang_id"]
            
            # GET MEAN
            import datetime as DT
            today = DT.date.today()
            week_ago = today - DT.timedelta(days=90)
            print(week_ago)
            df20 = pd.read_sql(sql = session.query(PetrolModel)\
                                    .with_entities(
                                        PetrolModel.petrol_id,
                                        PetrolModel.datetime,
                                        PetrolModel.datetime_bill,
                                        PetrolModel.data_details[0]['che_liang_id'],
                                        PetrolModel.data_details[0]['distant'],
                                        PetrolModel.data_details[0]['qty'],
                                        PetrolModel.data_details[0]['driver'],
                                        )
                                        .filter(PetrolModel.datetime > week_ago).statement,
                            con = session.bind)
            df20 = df20.rename(columns={
                "anon_1": 'che_liang_id',
                "anon_2": 'distant',
                "anon_3": 'qty',
                "anon_4": 'driver',
                })
            # display("df20", df20)
            df30 = pd.read_sql(sql = session.query(PetrolModel2)\
                                    .with_entities(
                                        PetrolModel2.petrol_id,
                                        PetrolModel2.datetime,
                                        PetrolModel2.data_details[0]['che_liang_id'],
                                        PetrolModel2.data_details[0]['distant'],
                                        PetrolModel2.data_details[0]['qty'],
                                        )
                                        .filter(PetrolModel2.datetime > week_ago).statement,
                            con = session.bind)
            df30 = df30.rename(columns={
                "anon_1": 'che_liang_id',
                "anon_2": 'distant',
                "anon_3": 'qty',
                })
            # # display("df30", df30)
            df40 = pd.concat([df20, df30], ignore_index=True)
            df40 = df40.sort_values(['datetime'], ascending=[True])
            # # display(df40)
            df50 = df40[df40['che_liang_id'] == get_id]

            df50['previous_datetime_bill'] = df50['datetime_bill'].shift()
            df50['diff_datetime_bill'] = (df50['datetime_bill'] - df50['previous_datetime_bill']).dt.days
            df50['mean_datetime_bill'] = df50['diff_datetime_bill'].mean()
            df50['percent_date_bill'] = df50.apply(lambda x: x.mean_datetime_bill and (x.diff_datetime_bill - x.mean_datetime_bill) / x.mean_datetime_bill * 100, axis=1)

            df50['previous_distant'] = df50['distant'].shift()  
            df50['distance_used'] = df50.apply(lambda x: x['previous_distant'] if x['previous_distant'] == 0 else (x['distant'] if x['distant'] == 0 else x['distant'] - x['previous_distant']), axis=1)
            df50['mean_distance_used'] = df50['distance_used'].mean()
            df50['previous_qty'] = df50['qty'].shift()
            df50['count'] = df50['distance_used']
            df50['km_lit'] = (df50['distance_used'] / df50['previous_qty'])         
            df50['average'] = df50[df50['km_lit'] != 0]["km_lit"].mean()
            df50['average_percent'] = df50.apply(lambda x: x['km_lit'] if x['km_lit'] == 0 else x.mean_distance_used and ((x.distance_used - x.mean_distance_used) / x.mean_distance_used) * 100, axis=1)

            outlier_detector = IsolationForest(random_state=42)
            distance_used_fillna = df50[['km_lit']].fillna(0)
            outlier_detector.fit(distance_used_fillna)
            prediction = outlier_detector.predict(distance_used_fillna)
            prediction_strings = ['Outlier' if x < 0 else 'Standard' for x in prediction]
            df50['outlier_flag'] = prediction_strings
            
            df50['km_lit_2'] = df50.apply(lambda x: None if x['outlier_flag'] == 'Outlier' else x['km_lit'], axis=1)
            df50['average_2'] = df50[df50['km_lit_2'] != 0]["km_lit_2"].mean()
            df50['mean_distance_used_2'] = (df50['average_2'] * df50['previous_qty']) + 0.01
            df50['average_percent_2'] = df50.apply(lambda x: (x.distance_used - x.mean_distance_used_2) / x.mean_distance_used_2 * 100, axis=1)
            df50['mean'] = ((df50.average_2 - df50.average) / df50.average) * 100

            df60 = df50.pivot_table(columns=['distance_used'], aggfunc='size')
            df60 = pd.DataFrame(df60)
            df60 = df60.rename(columns={0: 'countoo'}).reset_index()
            df50['count'] = df50[['count']].replace(to_replace =df60['distance_used'].to_list(), value =df60['countoo'].to_list())
            qty_list = df50['qty'].astype(float).round(decimals = 1)
            uniqleQtyToList = qty_list.unique().tolist()
            df50['uniqleQty'] = str(uniqleQtyToList)
            df50['FirstuniqleQty'] = str(uniqleQtyToList[0])
            df50['LastuniqleQty'] = str(uniqleQtyToList[-1])
            driver_list = df50['driver'].dropna().unique().tolist()
            df50['uniqleDriver'] = str(driver_list)
            df50['FirstuniqleDriver'] = driver_list[0]
            df50['LastuniqleDriver'] = driver_list[-1]
            df50['driverCount'] = df50['driver'].map(df50['driver'].value_counts())

            df50 = df50.fillna(0).round(decimals = 1)
            
            # SELECT CURREN PETROLID
            df70 = df50[df50['petrol_id'] == get_petrol_id]
            df70 = df70.to_dict('records')
            df70 = {"petrol_id": df70[0]['petrol_id'], "datetime": str(df70[0]['datetime']), "che_liang_id": df70[0]["che_liang_id"], "distant": df70[0]["distant"],
                    "distance_used": df70[0]["distance_used"], "mean_distance_used": df70[0]["mean_distance_used"], "average_percent": df70[0]["average_percent"], "mean": df70[0]["mean"],
                    "mean_distance_used_2": df70[0]["mean_distance_used_2"], "average_percent_2": df70[0]["average_percent_2"], "count": df70[0]["count"],
                    "qty": df70[0]["qty"], "uniqleQty": df70[0]["uniqleQty"], "FirstuniqleQty": df70[0]["FirstuniqleQty"], "LastuniqleQty": df70[0]["LastuniqleQty"],
                    "driver": df70[0]["driver"], "uniqleDriver": df70[0]["uniqleDriver"], "FirstuniqleDriver": df70[0]["FirstuniqleDriver"], "LastuniqleDriver": df70[0]["LastuniqleDriver"], "driverCount": df70[0]["driverCount"],
                    "diff_datetime_bill": df70[0]["diff_datetime_bill"], "mean_datetime_bill": df70[0]["mean_datetime_bill"], "percent_date_bill": df70[0]["percent_date_bill"]
                    }

            # INSERT INTO CALCULATE TABLE
            db_user2 = Calculate(
                petrol_id=get_petrol_id,
                calOne=df70
            )
            session.add(db_user2)
            session.commit()
            session.refresh(db_user2)
            # session.close()
    except:
        # FIRST INSERT
        a, b, c = await get_bookszxzx()
        db_user = PetrolModel(
            datetime=c,
            data_details=[
                dict(sub_id=b, r_id=a, fen_dian_id=post.fen_dian_id, user_id=post.user_id, warehouse_id=post.warehouse_id, u_id=post.u_id, jia_yi_fang_a=post.store, 
                    jia_yi_fang_b=post.car, driver=post.driver, product_id=post.petrol, qty=post.qty, 
                    price=post.price, distant=post.distant, che_liang_id=post.che_liang_id,
                    bi_zhi=post.currency, lei_a=post.lei_a, lei_b=post.lei_b, ke_bian=post.ke_bian, bill=post.bill
                    )],
            status_details = dict(status="waiting"),
            update_time=c,
            datetime_bill=post.datetime_bill
        )
        session.add(db_user)
        session.commit()
        session.refresh(db_user)
        # session.close()
        
        # RETURN VALUE
        get_petrol_id = db_user.petrol_id
        get_id = db_user.data_details[0]["che_liang_id"]
        
        # GET MEAN
        import datetime as DT
        today = DT.date.today()
        week_ago = today - DT.timedelta(days=90)
        print(week_ago)
        df20 = pd.read_sql(sql = session.query(PetrolModel)\
                                .with_entities(
                                    PetrolModel.petrol_id,
                                    PetrolModel.datetime,
                                    PetrolModel.datetime_bill,
                                    PetrolModel.data_details[0]['che_liang_id'],
                                    PetrolModel.data_details[0]['distant'],
                                    PetrolModel.data_details[0]['qty'],
                                    PetrolModel.data_details[0]['driver'],
                                    )
                                    .filter(PetrolModel.datetime > week_ago).statement,
                        con = session.bind)
        df20 = df20.rename(columns={
            "anon_1": 'che_liang_id',
            "anon_2": 'distant',
            "anon_3": 'qty',
            "anon_4": 'driver',
            })
        # display("df20", df20)
        df30 = pd.read_sql(sql = session.query(PetrolModel2)\
                                .with_entities(
                                    PetrolModel2.petrol_id,
                                    PetrolModel2.datetime,
                                    PetrolModel2.data_details[0]['che_liang_id'],
                                    PetrolModel2.data_details[0]['distant'],
                                    PetrolModel2.data_details[0]['qty'],
                                    )
                                    .filter(PetrolModel2.datetime > week_ago).statement,
                        con = session.bind)
        df30 = df30.rename(columns={
            "anon_1": 'che_liang_id',
            "anon_2": 'distant',
            "anon_3": 'qty',
            })
        # # display("df30", df30)
        df40 = pd.concat([df20, df30], ignore_index=True)
        df40 = df40.sort_values(['datetime'], ascending=[True])
        # # display(df40)
        df50 = df40[df40['che_liang_id'] == get_id]

        df50['previous_datetime_bill'] = df50['datetime_bill'].shift()
        df50['diff_datetime_bill'] = (df50['datetime_bill'] - df50['previous_datetime_bill']).dt.days
        df50['mean_datetime_bill'] = df50['diff_datetime_bill'].mean()
        df50['percent_date_bill'] = df50.apply(lambda x: x.mean_datetime_bill and (x.diff_datetime_bill - x.mean_datetime_bill) / x.mean_datetime_bill * 100, axis=1)

        df50['previous_distant'] = df50['distant'].shift()  
        df50['distance_used'] = df50.apply(lambda x: x['previous_distant'] if x['previous_distant'] == 0 else (x['distant'] if x['distant'] == 0 else x['distant'] - x['previous_distant']), axis=1)
        df50['mean_distance_used'] = df50['distance_used'].mean()
        df50['previous_qty'] = df50['qty'].shift()
        df50['count'] = df50['distance_used']
        df50['km_lit'] = (df50['distance_used'] / df50['previous_qty'])         
        df50['average'] = df50[df50['km_lit'] != 0]["km_lit"].mean()
        df50['average_percent'] = df50.apply(lambda x: x['km_lit'] if x['km_lit'] == 0 else x.mean_distance_used and ((x.distance_used - x.mean_distance_used) / x.mean_distance_used) * 100, axis=1)

        outlier_detector = IsolationForest(random_state=42)
        distance_used_fillna = df50[['km_lit']].fillna(0)
        outlier_detector.fit(distance_used_fillna)
        prediction = outlier_detector.predict(distance_used_fillna)
        prediction_strings = ['Outlier' if x < 0 else 'Standard' for x in prediction]
        df50['outlier_flag'] = prediction_strings
        
        df50['km_lit_2'] = df50.apply(lambda x: None if x['outlier_flag'] == 'Outlier' else x['km_lit'], axis=1)
        df50['average_2'] = df50[df50['km_lit_2'] != 0]["km_lit_2"].mean()
        df50['mean_distance_used_2'] = (df50['average_2'] * df50['previous_qty']) + 0.01
        df50['average_percent_2'] = df50.apply(lambda x: (x.distance_used - x.mean_distance_used_2) / x.mean_distance_used_2 * 100, axis=1)
        df50['mean'] = ((df50.average_2 - df50.average) / df50.average) * 100

        df60 = df50.pivot_table(columns=['distance_used'], aggfunc='size')
        df60 = pd.DataFrame(df60)
        df60 = df60.rename(columns={0: 'countoo'}).reset_index()
        df50['count'] = df50[['count']].replace(to_replace =df60['distance_used'].to_list(), value =df60['countoo'].to_list())
        qty_list = df50['qty'].astype(float).round(decimals = 1)
        uniqleQtyToList = qty_list.unique().tolist()
        df50['uniqleQty'] = str(uniqleQtyToList)
        df50['FirstuniqleQty'] = str(uniqleQtyToList[0])
        df50['LastuniqleQty'] = str(uniqleQtyToList[-1])
        driver_list = df50['driver'].dropna().unique().tolist()
        df50['uniqleDriver'] = str(driver_list)
        df50['FirstuniqleDriver'] = driver_list[0]
        df50['LastuniqleDriver'] = driver_list[-1]
        df50['driverCount'] = df50['driver'].map(df50['driver'].value_counts())

        df50 = df50.fillna(0).round(decimals = 1)
        
        # SELECT CURREN PETROLID
        df70 = df50[df50['petrol_id'] == get_petrol_id]
        df70 = df70.to_dict('records')
        df70 = {"petrol_id": df70[0]['petrol_id'], "datetime": str(df70[0]['datetime']), "che_liang_id": df70[0]["che_liang_id"], "distant": df70[0]["distant"],
                "distance_used": df70[0]["distance_used"], "mean_distance_used": df70[0]["mean_distance_used"], "average_percent": df70[0]["average_percent"], "mean": df70[0]["mean"],
                "mean_distance_used_2": df70[0]["mean_distance_used_2"], "average_percent_2": df70[0]["average_percent_2"], "count": df70[0]["count"],
                "qty": df70[0]["qty"], "uniqleQty": df70[0]["uniqleQty"], "FirstuniqleQty": df70[0]["FirstuniqleQty"], "LastuniqleQty": df70[0]["LastuniqleQty"],
                "driver": df70[0]["driver"], "uniqleDriver": df70[0]["uniqleDriver"], "FirstuniqleDriver": df70[0]["FirstuniqleDriver"], "LastuniqleDriver": df70[0]["LastuniqleDriver"], "driverCount": df70[0]["driverCount"],
                "diff_datetime_bill": df70[0]["diff_datetime_bill"], "mean_datetime_bill": df70[0]["mean_datetime_bill"], "percent_date_bill": df70[0]["percent_date_bill"]
                }

        # INSERT INTO CALCULATE TABLE
        db_user2 = Calculate(
            petrol_id=get_petrol_id,
            calOne=df70
        )
        session.add(db_user2)
        session.commit()
        session.refresh(db_user2)
        # session.close()
        df70 = "first in"
    return df70


@router.post("/addStore")
async def addStore(post: addStore, session: Session = Depends(get_db)):
    # session = Session()
    db_user = TypeCar(
        jia_yi_id=post.jia_yi_id,
        data_sub=dict(search="jia_yi_name_list_id", type_name="partner_id")
    )
    session.add(db_user)
    session.commit()
    session.refresh(db_user)
    # session.close()
    return db_user

@router.post("/addCar")
async def addCar(post: addCar, session: Session = Depends(get_db)):
    # session = Session()
    db_user = TypeCar(
        jia_yi_id=post.jia_yi_id,
        data_sub=dict(search="jia_yi_name_list_id", type_name="car_id")
    )
    session.add(db_user)
    session.commit()
    session.refresh(db_user)
    # session.close()
    return db_user

@router.post("/addDriver")
async def addDriver(post: addDriver, session: Session = Depends(get_db)):
    # session = Session()
    db_user = TypeCar(
        jia_yi_id=post.jia_yi_id,
        data_sub=dict(search="jia_yi_search_text", type_name="driver_id")
    )
    session.add(db_user)
    session.commit()
    session.refresh(db_user)
    # session.close()
    return db_user


@router.get("/checkDriver/{checkDriver}")
async def checkDriver(checkDriver: str):
    # print(checkDriver)
    # print(type(checkDriver))

    try:
        checkDriver = int(checkDriver)
        print(checkDriver)
        print(type(checkDriver))

        # url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'
        url = Arter_api + 'jia_yi_name_list_id'
        # body_raw = {"data_api": [{"jia_yi_id": 36557}]}
        body_raw = {"data_api": [{"jia_yi_id": checkDriver}]}
        # body_raw = {"data_api": df}
        df2 = requests.get(url=url, json=body_raw)
        df2 = df2.json()
        df2 = pd.DataFrame(df2)
        df2 = df2.to_dict('records')
        # print(df2)
    except:
        print('The provided value is not an integer')
        print(checkDriver)
        print(type(checkDriver))

        # url = f'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/jia_yi_search_text?text={checkDriver}'
        url = Arter_api + f'jia_yi_search_text?text={checkDriver}'
        df2 = requests.get(url=url)
        df2 = df2.json()
        df2 = pd.DataFrame(df2)
        df2 = df2.to_dict('records')
        # print(df2)

    return df2

@router.get("/check_price")
async def check_price(che_liang_id: str, session: Session = Depends(get_db)):
    # session = Session()
    df10 = pd.read_sql(sql = session.query(PetrolModel)\
                        .with_entities(
                            PetrolModel.petrol_id,
                            PetrolModel.datetime,
                            PetrolModel.data_details[0]['che_liang_id'],
                            PetrolModel.data_details[0]['price']
                            )
                        .filter(PetrolModel.data_details[0]['che_liang_id'].astext.cast(Integer) == che_liang_id)
                        .order_by(PetrolModel.datetime.desc())
                        .limit(2)
                        .statement,
                con = session.bind)
    df10 = df10.rename(columns={
        "anon_1": 'che_liang_id', "anon_2": 'price',
    })
    print(len(df10))
    # df10.loc[0, 'price'] = 27
    # df10.loc[1, 'price'] = 30
    if len(df10) >= 2:
        if abs(df10.iloc[0]['price'] - df10.iloc[1]['price']) > (0.5 * df10.iloc[0]['price']):
            print("Abnormal")
            ans = "NoInsert"  
        else:
            print("normal")
            ans = "goInsert"  
    else:
        # If there's only one row, you can consider it as "goInsert"
        print("normal")
        ans = "goInsert"

    # ans = df10.to_dict('records')   
    return ans 


@router.get("/checkCarThatNotInTerAPI")
async def check_car_that_not_in_ter_api(session: Session = Depends(get_db)):

    df = pd.read_sql_table('car_type', engine)
    df = df.to_dict('records')
    df = pd.json_normalize(df)
    df = df.rename(columns={"data_sub.type_name": 'data_sub_type_name'})
    df = df.loc[df['data_sub_type_name'] == "car_id"]
    df = df.loc[:, df.columns.isin(['jia_yi_id'])]

    # Assuming you have the API response in a variable called 'api_data'
    api_url = f'{pv_api}/shwethe_car_active/api/v1/form/vehicles'
    response = requests.get(api_url)
    api_data = response.json()

    # Create a DataFrame from the API data
    api_df = pd.DataFrame(api_data)
    che_liang_ids = api_df["che_liang_id"].tolist()
    # print(che_liang_ids)

    missing_jia_yi_ids = df[~df["jia_yi_id"].isin(che_liang_ids)]
    # print(missing_jia_yi_ids)

    url = Arter_api + 'jia_yi_name_list_id'
    to_dict = missing_jia_yi_ids.to_dict('records')
    body_raw = {"data_api": to_dict}
    new = requests.get(url=url, json=body_raw).json()
    
    return new



@router.get("/bug")
async def bug(session: Session = Depends(get_db)):

    import datetime as DT
    today = DT.date.today()
    week_ago = today - DT.timedelta(days=3)
    print(week_ago)

    df20 = pd.read_sql(sql = session.query(PetrolModel) 
                           .filter(PetrolModel.datetime > week_ago)
                           .filter(PetrolModel.status_details['status'].astext.cast(String) == "success")
                           .statement, con = session.bind)
    df20 = df20.to_dict('records')
    json_nor = pd.json_normalize(df20, "data_details", ["auto_id", "petrol_id", "datetime"])
    json_nor = json_nor.to_dict('records')

    filtered_data = []
    for d in json_nor:
        if d["jia_yi_fang_a"] == 0:
           print(d)
           print(d['jia_yi_fang_a'])
           user = session.query(PetrolModel).filter(PetrolModel.petrol_id == d['petrol_id']).first()
           for var in user.data_details:
                print(var['r_id'])
                if var['r_id'] == d['r_id']:
                    var.update(dict(jia_yi_fang_a=d['warehouse_id']))
           flag_modified(user, 'data_details')
           session.commit()
        else:
            print("jia_yi_fang_a not equal to 0")
    
    # aaa = []
    # for ioo in json_nor:
    #     d = {
    #         'a_id': ioo['auto_id'],
    #         'b_id': ioo['sub_id'],
    #         'datetime': str(ioo['datetime']),
    #         'jin_huo_dang': ioo['petrol_id'],
    #         'uid': ioo['user_id'],
    #         'product_id': ioo['product_id'],
    #         'product_qty': ioo['qty'],
    #         'product_price_x': ioo['price'],
    #         'product_price_y': ioo['price'],
    #         'jia_yi_fang_a': ioo['jia_yi_fang_a'],
    #         'jia_yi_fang_b': ioo['jia_yi_fang_b'],
    #         'lei_a': ioo['lei_a'],
    #         'lei_b': ioo['lei_b'],
    #         'line': 1005,
    #         'ke_bian': ioo['ke_bian'],
    #         'che_liang': ioo['che_liang_id'],
    #         'kind': 10003,
    #         'bi_zhi': ioo['bi_zhi']
    #         }
    #     aaa.append(d)
    # df1 = pd.DataFrame(aaa) 

    return filtered_data


@router.get("/driver_compare_each_car")
async def driver_compare_each_car(che_liang_id: int, session: Session = Depends(get_db)):

    import datetime as DT
    today = DT.date.today()
    week_ago = today - DT.timedelta(days=180)
    print(week_ago)

    che_liang_id = che_liang_id

    df20 = pd.read_sql(sql = session.query(PetrolModel)\
                                .with_entities(
                                    PetrolModel.petrol_id,
                                    PetrolModel.datetime,
                                    PetrolModel.datetime_bill,
                                    PetrolModel.data_details[0]['che_liang_id'],
                                    PetrolModel.data_details[0]['distant'],
                                    PetrolModel.data_details[0]['qty'],
                                    PetrolModel.data_details[0]['driver'],
                                    )
                                    .filter(
                                        PetrolModel.datetime > week_ago,
                                        func.cast(
                                            PetrolModel.data_details[0]['che_liang_id'].astext,
                                            Integer
                                        ) == che_liang_id
                                    ).statement,
                        con = session.bind)
    df20 = df20.rename(columns={
        "anon_1": 'che_liang_id',
        "anon_2": 'distant',
        "anon_3": 'qty',
        "anon_4": 'driver',
        })


    df50 = df20
    # print(df50)

    df50 = df50.sort_values(['datetime'], ascending=[True])
    
    df50['datetime'] = pd.to_datetime(df50['datetime'])
    df50['datetime'] = df50['datetime'].dt.date

    df50['previous_distant'] = df50['distant'].shift().fillna(0)
    df50['distance_used'] = df50.apply(lambda x: x['previous_distant'] if x['previous_distant'] == 0 else (x['distant'] if x['distant'] == 0 else x['distant'] - x['previous_distant']), axis=1)
    df50['mean_distance_used'] = df50['distance_used'].mean()
    df50['previous_qty'] = df50['qty'].shift()
    df50['km_lit'] = (df50['distance_used'] / df50['previous_qty'])
    df50['average'] = df50[df50['km_lit'] != 0]["km_lit"].mean()
    df50['average_percent'] = df50.apply(lambda x: x['km_lit'] if x['km_lit'] == 0 else x.mean_distance_used and ((x.distance_used - x.mean_distance_used) / x.mean_distance_used) * 100, axis=1)

    df50['mean_qty_dri'] = df50.groupby('driver')['qty'].transform('mean')
    df50['km_lit_dri'] = df50.groupby('driver')['km_lit'].transform('mean')
    df50['volatility_dri'] = df50.groupby('driver')['average_percent'].transform(lambda x: x.abs().sum() / len(x))
    df50['mean_distance_used_dri'] = df50.groupby('driver')['distance_used'].transform('mean')
    df50['period_dri'] = df50.groupby('driver')['datetime'].transform(lambda x: f'{x.min().strftime("%Y-%m-%d")} - {x.max().strftime("%Y-%m-%d")}')
    df50['day_pass_dri'] = (df50.groupby('driver')['datetime']
                    .transform(lambda x: (x.max() - x.min()).days))
    df50['count_dri'] = df50.groupby('driver')['driver'].transform('count')


    columns_to_exclude = ['distant', 'qty', 'previous_distant', 'distance_used', 'mean_distance_used', 'previous_qty', 'km_lit']
    df50 = df50.drop(columns=columns_to_exclude)


    df50['datetime'] = pd.to_datetime(df50['datetime'])
    df50 = df50.loc[df50.groupby('driver')['datetime'].idxmax()]
    df50 = df50.round(2)


    df50 = df50.to_dict('records')   

    return df50