import numpy as np
from fastapi import FastAPI, APIRouter, Body, Response, BackgroundTasks, Header
from pydantic import BaseModel
from sqlalchemy.orm.attributes import flag_modified
from helper import generate_id, generate_datetime, generate_datetime_id, generate_datetime_selie, get_bookszxzx
from typing import List, Optional
import json
import requests
import pandas as pd
import time


from sqlalchemy import create_engine, tuple_
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import NullPool
from fastapi.responses import JSONResponse
from datetime import date, datetime
from src.Connect.postgresql_connect import DATABASE_URL
from src.Connect.https_connect import Arter_api
from src.shwethe_petrol.models.model import PetrolModel, TypeCar, TypeLei
from src.shwethe_petrol.schema.schema import Put, formSubmit, addStore, addCar
from functools import reduce


router = APIRouter()


engine = create_engine(DATABASE_URL, poolclass=NullPool)
Session = sessionmaker(bind=engine)



# @router.put("/confirm_status/{petrol_id}")
# async def confirmStatus(petrol_id: str, put: confirm_status):
#     # UPDATE STATUS TO SUCCESS
#     session = Session()
#     user = session.query(PetrolModel).filter(
#         PetrolModel.petrol_id == petrol_id).first()
#     ccc = user.status_details
#     ccc.update({"status": put.status})
#     flag_modified(user, 'status_details')
#     session.commit()
    
#     # GET PETROL DATA FROM petrol_id
#     auto_id = petrol_id
#     sql = """SELECT  * from petrol where petrol_id = %s"""
#     database = pd.read_sql_query(sql, engine, params=[auto_id])
#     to_record = database.to_dict('records')
#     json_nor = pd.json_normalize(to_record, "data_details", ["petrol_id", "datetime"])
#     json_nor = json_nor.to_dict('records')
    
#     # INSERT TO SQL SERVER 2000
#     DATABASE_URL2 = "mssql+pymssql://sa:123041316@192.168.1.202/aa名稱"
#     engine2 = create_engine(DATABASE_URL2, poolclass=NullPool)
#     Session2 = sessionmaker(bind=engine2)
#     Base = declarative_base()
#     class BBPetrol(Base):
#         __tablename__ = 'Table1'
#         auto_id = Column(Integer, primary_key=True)
#         petrol_id = Column(TEXT)
#         datetime = Column(DateTime)
#         sub_id = Column(Integer)
#         fen_dian_id = Column(Integer)
#         warehouse_id = Column(Integer)
#         user_id = Column(Integer)
#         jia_yi_fang_a = Column(Integer)
#         jia_yi_fang_b = Column(Integer)
#         che_liang_id = Column(Integer)
#         lei_a = Column(Integer)
#         lei_b = Column(Integer)
#         ke_bian = Column(Integer)
#         product_id = Column(Integer)
#         bi_zhi = Column(Integer)
#         price = Column(FLOAT)
#         qty = Column(FLOAT)
#         distant = Column(FLOAT)
#         bill = Column(TEXT)
#     session = Session2()
#     db_user = BBPetrol(
#         petrol_id=json_nor[0]['petrol_id'],
#         datetime=json_nor[0]['datetime'],
#         sub_id=json_nor[0]['sub_id'],
#         fen_dian_id=json_nor[0]['fen_dian_id'],
#         warehouse_id=json_nor[0]['warehouse_id'],
#         user_id=json_nor[0]['user_id'],
#         jia_yi_fang_a=json_nor[0]['jia_yi_fang_a'],
#         jia_yi_fang_b=json_nor[0]['jia_yi_fang_b'],
#         che_liang_id=json_nor[0]['che_liang_id'],
#         lei_a=json_nor[0]['lei_a'],
#         lei_b=json_nor[0]['lei_b'],
#         ke_bian=json_nor[0]['ke_bian'],
#         product_id=json_nor[0]['product_id'],
#         bi_zhi=json_nor[0]['bi_zhi'],
#         price=json_nor[0]['price'],
#         qty=json_nor[0]['qty'],
#         distant=json_nor[0]['distant'],
#         bill=json_nor[0]['bill']
#     )
#     session.add(db_user)
#     session.commit()
#     session.refresh(db_user)
#     session.close()
    
#     return json_nor

@router.get("/calDistantUpdate/{che_liang_id}")
async def calDistantUpdate(che_liang_id: int = 36555):
    df = pd.read_sql_table('petrol', engine)
    df = df.to_dict('records')
    df = pd.json_normalize(df, "data_details", ["petrol_id", "datetime", "auto_id"])
    df = df.loc[df['che_liang_id'] == che_liang_id]
    df = df.iloc[-2:-1]
    df = df.to_dict('records')
    return df

@router.get("/calDistantInsert/{che_liang_id}")
async def calDistantInsert(che_liang_id: int = 36555):
    df = pd.read_sql_table('petrol', engine)
    df = df.to_dict('records')
    df = pd.json_normalize(df, "data_details", ["petrol_id", "datetime", "auto_id"])
    df = df.loc[df['che_liang_id'] == che_liang_id]
    df = df.iloc[-1:]
    df = df.to_dict('records')
    
    new_value = []
    if_yes = False
    if df == []:
       print("yes")
       new_value.append(if_yes)
       new_value = str(new_value)[1:-1] 
    else:
       print("no")  
       new_value.extend(df)  
    print(new_value)   
    return new_value

@router.get("/getPayment")
async def getPayment():
    # df = pd.read_sql_table('lei_type', engine)
    # # df = df.loc[:, ~df.columns.isin(['data_sub'])]
    # df = df.drop([('data_sub')], axis=1)
    # df = df.to_dict('records')
    # return df
    session = Session()
    books = session.query(TypeLei.lei_a, TypeLei.lei_b, TypeLei.lei_name).order_by(TypeLei.lei_a.desc()).all()
    session.close()
    return books

@router.get("/getCurrency")
async def getCurrency():
    df = pd.read_sql_table('bi_zhi', engine)
    df = df.to_dict('records')
    return df

@router.put("/put/{petrol_id}/{r_id}", response_model=Put)
async def put(petrol_id: str, r_id: str, put: Put):
    session = Session()

    user = session.query(PetrolModel).filter(
        PetrolModel.petrol_id == petrol_id).first()
    for var in user.data_details:
        # print(var['r_id'])
        if var['r_id'] == r_id:
            var.update(dict(jia_yi_fang_a=put.store, jia_yi_fang_b=put.car, product_id=put.petrol, 
                            qty=put.qty, price=put.price, distant=put.distant, che_liang_id=put.che_liang_id,
                            bi_zhi=put.currency, lei_a=put.lei_a, lei_b=put.lei_b, ke_bian=put.ke_bian))
    flag_modified(user, 'data_details')
    session.commit()
    
    return JSONResponse(status_code=200, content={
        "status_code": 200,
        "message": "success"
    })


@router.get("/IndexData")
async def IndexData(): 
    df = pd.read_sql_table('petrol', engine)
    df = df.loc[:, df.columns.isin(['petrol_id', 'datetime'])]
    df = df.sort_values(by = ['petrol_id'], ascending = [False])
    # df = df.head(3)
    df = df.to_dict('records')
    return df

 
@router.get("/contentData/{petrol_id}")
async def contentData(petrol_id: str):
    
    today = pd.DataFrame({'today':[pd.to_datetime("today").normalize()]})
    today = today.to_string().replace("today\n0","")
    # display(today)
    
    
    auto_id = petrol_id
    sql = """SELECT  * from petrol where petrol_id = %s"""
    database = pd.read_sql_query(sql, engine, params=[auto_id])
    to_record = database.to_dict('records')
    json_nor = pd.json_normalize(to_record, "data_details", ["petrol_id", "datetime"])


    df1 = json_nor
    df1 = df1.loc[:, df1.columns.isin(['jia_yi_fang_a', 'petrol_id'])]
    df1 = df1.rename(columns={"jia_yi_fang_a": 'jia_yi_id'})
    df1 = pd.DataFrame(df1)
    # display('df1', df1)
    # url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'
    url = Arter_api + 'jia_yi_name_list_id'
    to_dict = df1.to_dict('records')
    body_raw = {"data_api": to_dict}
    df13 = requests.get(url=url, json=body_raw).json()
    df13 = pd.DataFrame(df13)
    # display('df13', df13)
    merge_store = pd.merge(df1, df13, on='jia_yi_id', how='left')
    # display('merge_store', merge_store)


    df2 = json_nor
    df2 = df2.loc[:, df2.columns.isin(['jia_yi_fang_b', 'petrol_id'])]
    df2 = df2.rename(columns={"jia_yi_fang_b": 'jia_yi_id'})
    df2 = pd.DataFrame(df2)
    # display('df2', df2)
    # url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'
    url = Arter_api + 'jia_yi_name_list_id'
    to_dict = df2.to_dict('records')
    body_raw = {"data_api": to_dict}
    df23 = requests.get(url=url, json=body_raw).json()
    df23 = pd.DataFrame(df23)
    # display('df23', df23)
    merge_car = pd.merge(df2, df23, on='jia_yi_id', how='left')
    # display('merge_car', merge_car)


    df3 = json_nor
    df3 = df3.loc[:, df3.columns.isin(['product_id', 'petrol_id'])]
    df3 = pd.DataFrame(df3)
    # display('df3', df3)
    # url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/product_list_id'
    url = Arter_api + 'product_list_id'
    to_dict = df3.to_dict('records')
    body_raw = {"data_api": to_dict}
    df33 = requests.get(url=url, json=body_raw).json()
    df33 = pd.DataFrame(df33)
    # display('df33', df33)
    merge_petrol = pd.merge(df3, df33, on='product_id', how='left')
    # display('merge_petrol', merge_petrol)


    df4 = json_nor
    df4 = df4.loc[:, df4.columns.isin(['bi_zhi', 'petrol_id'])]
    # display('df4', df4)
    df42 = pd.read_sql_table('bi_zhi', engine)
    df42 = df42.loc[:, ~df42.columns.isin(['auto_id'])]
    # display('df42', df42)
    merge_bi_zhi = pd.merge(df4, df42, on='bi_zhi', how='left')
    # display('merge_bi_zhi', merge_bi_zhi)


    df5 = json_nor
    df5 = df5.loc[:, df5.columns.isin(['lei_a', 'ke_bian', 'petrol_id'])]
    df5 = df5.rename(columns={"ke_bian": 'jia_yi_id'})
    df5 = pd.DataFrame(df5)
    # display('df5', df5)
    df52 = pd.read_sql_table('lei_type', engine)
    df52 = df52.loc[:, ~df52.columns.isin(['auto_id', 'data_sub'])]
    df52 = pd.DataFrame(df52)
    # display('df52', df52)
    # url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'
    url = Arter_api + 'jia_yi_name_list_id'
    to_dict = df5.to_dict('records')
    body_raw = {"data_api": to_dict}
    df53 = requests.get(url=url, json=body_raw).json()
    df53 = [{"jia_yi_id": df53}]
    new_value = []
    for data in df53:
        if data['jia_yi_id'] == False:
            new_value.append(data)
        else:
            new_value.extend(data['jia_yi_id'])
    df53 = pd.DataFrame(new_value)
    # display('df53', df53)
    merge_lei_a_ke_bian = df5.merge(df52, on='lei_a', how='left').merge(df53, on='jia_yi_id', how='left')
    # display('merge_lei_a_ke_bian', merge_lei_a_ke_bian)


    data = json_nor
    data = data.loc[:, ~data.columns.isin(['jia_yi_fang_a', 'jia_yi_fang_b', 'product_id', 'bi_zhi', 'lei_a', 'lei_b', 'ke_bian'])]
    data = pd.DataFrame(data)
    # display('data', data)
    merge = [data, merge_store, merge_car, merge_petrol, merge_bi_zhi, merge_lei_a_ke_bian]
    merge = reduce(lambda left,right: pd.merge(left,right,on='petrol_id'), merge)
    merge = merge.rename(columns=dict(jia_yi_id_x='storeJia_yi_id', 
                                    jia_yi_idname_x='storeJia_yi_idname_x',
                                    jia_yi_mm_name_x='storeJia_yi_mm_name_x',
                                    jia_yi_id_y='carJia_yi_id', 
                                    jia_yi_idname_y='carJia_yi_idname_x',
                                    jia_yi_mm_name_y='carJia_yi_mm_name_x',
                                    product_id='petrolProduct_id',
                                    product_idname='petrolProduct_idname',
                                    product_mm_name='petrolProduct_mm_name', 
                                    product_d_name='petrolProduct_d_name',
                                    th_name='petrolTh_name',
                                    idname='bi_zhiIdname',
                                    jia_yi_id='ke_bianJia_yi_id',
                                    jia_yi_idname='ke_bianJia_yi_idname',
                                    jia_yi_mm_name='ke_bianJia_yi_mm_name'
                                    ))
    merge['datetime'] = pd.to_datetime(merge['datetime']).dt.normalize()
    merge['status_update'] = np.where(merge['datetime'] == today, True, False)
    merge = merge.to_dict('records')
    # display('merge', merge)
    return merge
    

@router.get("/getStore")
async def getStore():
    df = pd.read_sql_table('car_type', engine)
    df = df.to_dict('records')
    df = pd.json_normalize(df)
    df = df.rename(columns={"data_sub.type_name": 'data_sub_type_name'})
    df = df.loc[df['data_sub_type_name'] == "partner_id"]
    df = df.loc[:, df.columns.isin(['jia_yi_id'])]
    df = df.to_json(orient='records')
    df = json.loads(df)
    
    # url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'
    url = Arter_api + 'jia_yi_name_list_id'
    # body_raw = {"data_api": [{"jia_yi_id": 22904}]}
    body_raw = {"data_api": df}
    df2 = requests.get(url=url, json=body_raw)
    df2 = df2.json()
    df2 = pd.DataFrame(df2)
    df2 = df2.to_dict('records')
    return df2

@router.get("/getCar")
async def getCar():
    df = pd.read_sql_table('car_type', engine)
    df = df.to_dict('records')
    df = pd.json_normalize(df)
    df = df.rename(columns={"data_sub.type_name": 'data_sub_type_name'})
    df = df.loc[df['data_sub_type_name'] == "car_id"]
    df = df.loc[:, df.columns.isin(['jia_yi_id'])]
    df = df.to_json(orient='records')
    df = json.loads(df)
    
    # url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'
    url = Arter_api + 'jia_yi_name_list_id'
    # body_raw = {"data_api": [{"jia_yi_id": 22904}]}
    body_raw = {"data_api": df}
    df2 = requests.get(url=url, json=body_raw)
    df2 = df2.json()
    df2 = pd.DataFrame(df2)
    df2 = df2.to_dict('records')
    return df2

@router.get("/getPetrol")
async def getCar():
    df = pd.read_sql_table('car_type', engine)
    df = df.to_dict('records')
    df = pd.json_normalize(df)
    df = df.rename(columns={"jia_yi_id": "product_id", "data_sub.type_name": 'data_sub_type_name'})
    df = df.loc[df['data_sub_type_name'] == "petrol_id"]
    df = df.loc[:, df.columns.isin(['product_id'])]
    df = df.to_json(orient='records')
    df = json.loads(df)
    
    # url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/product_list_id'
    url = Arter_api + 'product_list_id'
    # body_raw = {"data_api": [{"jia_yi_id": 22904}]}
    body_raw = {"data_api": df}
    df2 = requests.get(url=url, json=body_raw)
    df2 = df2.json()
    df2 = pd.DataFrame(df2)
    df2 = df2.to_dict('records')
    return df2
    

@router.post("/formSubmit")
async def formSubmit(post: formSubmit):
    session = Session()
    a, b, c = await get_bookszxzx()
    db_user = PetrolModel(
        datetime=c,
        # data_details=post.data_details,
        data_details=[
            dict(sub_id=b, r_id=a, u_id=post.u_id, jia_yi_fang_a=post.store, 
                 jia_yi_fang_b=post.car, product_id=post.petrol, qty=post.qty, 
                 price=post.price, distant=post.distant, che_liang_id=post.che_liang_id,
                 bi_zhi=post.currency, lei_a=post.lei_a, lei_b=post.lei_b, ke_bian=post.ke_bian
                 )]
    )
    session.add(db_user)
    session.commit()
    session.refresh(db_user)
    session.close()
    return db_user

@router.post("/addStore")
async def addStore(post: addStore):
    session = Session()
    db_user = TypeCar(
        jia_yi_id=post.jia_yi_id,
        data_sub=dict(search="jia_yi_name_list_id", type_name="partner_id")
    )
    session.add(db_user)
    session.commit()
    session.refresh(db_user)
    session.close()
    return db_user

@router.post("/addCar")
async def addCar(post: addCar):
    session = Session()
    db_user = TypeCar(
        jia_yi_id=post.jia_yi_id,
        data_sub=dict(search="jia_yi_name_list_id", type_name="car_id")
    )
    session.add(db_user)
    session.commit()
    session.refresh(db_user)
    session.close()
    return db_user

