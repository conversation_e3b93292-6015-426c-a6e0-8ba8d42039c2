

def dfaaa(session, petrol_id, put):
    # UPDATE STATUS TO SUCCESS
    user = session.query(PetrolModel).filter(
        PetrolModel.petrol_id == petrol_id).first()
    ccc = user.status_details
    ccc.update({"status": put.status})
    flag_modified(user, 'status_details')
    session.commit()
    
    # RETURN VALUE
    get_petrol_id = user.petrol_id
    get_id = user.data_details[0]["che_liang_id"]
    get_datetime = user.datetime
    
    return get_petrol_id, get_id, get_datetime
def dfbbb(session, get_petrol_id, get_id, get_datetime):
    # # GET MEAN
    today = datetime.date.today()
    future_day = today.day
    future_month = (today.month - 12) % 12
    future_year = today.year + ((today.month - 12) // 12)
    six_months_ago = datetime.date(future_year, future_month, future_day) 
    df20 = pd.read_sql(sql = session.query(PetrolModel)\
                            .with_entities(
                                PetrolModel.petrol_id,
                                PetrolModel.datetime,
                                PetrolModel.data_details[0]['che_liang_id'],
                                PetrolModel.data_details[0]['distant'],
                                PetrolModel.data_details[0]['qty'],
                                )
                                .filter(PetrolModel.datetime > six_months_ago).statement,
                    con = session.bind)
    df20 = df20.rename(columns={
        "anon_1": 'che_liang_id',
        "anon_2": 'distant',
        "anon_3": 'qty',
        })
    # display("df20", df20)
    df30 = pd.read_sql(sql = session.query(PetrolModel2)\
                            .with_entities(
                                PetrolModel2.petrol_id,
                                PetrolModel2.datetime,
                                PetrolModel2.data_details[0]['che_liang_id'],
                                PetrolModel2.data_details[0]['distant'],
                                PetrolModel2.data_details[0]['qty'],
                                )
                                .filter( PetrolModel2.datetime > six_months_ago).statement,
                    con = session.bind)
    df30 = df30.rename(columns={
        "anon_1": 'che_liang_id',
        "anon_2": 'distant',
        "anon_3": 'qty',
        })
    # # display("df30", df30)
    df40 = pd.concat([df20, df30], ignore_index=True)
    df40 = df40.sort_values(['datetime'], ascending=[True])
    # # display(df40)
    df50 = df40[df40['che_liang_id'] == get_id]
    df50['previous_distant'] = df50['distant'].shift()
    def fx(x):
        if x['previous_distant'] == 0:
            return x['previous_distant']
        elif x['distant'] == 0:
            return x['distant']
        else:
            return x['distant'] - x['previous_distant']
    df50['distance_used'] = df50.apply(lambda x : fx(x),axis=1)
    df50['previous_qty'] = df50['qty'].shift()
    df50['duplicated'] = df50['distance_used'].duplicated(keep=False)
    df50['count'] = df50['distance_used']
    df50['km_lit'] = (df50['distance_used'] / df50['previous_qty'])         
    df50['average'] = df50[df50['km_lit'] != 0]["km_lit"].mean()
    def fx2(x):
        if x['km_lit'] == 0:
            return x['km_lit']
        else:
            return (x.km_lit - x.average) / x.average * 100
    df50['average_percent'] = df50.apply(lambda x : fx2(x),axis=1)
    def remove_outliers(df50, q=0.05):
        upper = df50.quantile(1-q)
        lower = df50.quantile(q)
        mask = (df50 < upper) & (df50 > lower)
        return mask
    df50['outliers'] = remove_outliers(df50['km_lit'], 0.1)
    def fx3(x):
        if x['outliers'] == False:
            return None
        else:
            return x['distance_used'] / x['previous_qty']
    df50['km_lit_2'] = df50.apply(lambda x : fx3(x),axis=1)
    df50['average_2'] = df50[df50['km_lit_2'] != 0]["km_lit_2"].mean()
    def fx4(x):
        if x['km_lit_2'] == None:
            return x['km_lit_2']
        else:
            return (x.km_lit_2 - x.average_2) / x.average_2 * 100
    df50['average_percent_2'] = df50.apply(lambda x : fx4(x),axis=1)
    df50['mean'] = ((df50.average_2 - df50.average) / df50.average * 100)
    df50['average_mean'] = df50["average_percent"].astype(str) + ' , ' + df50["mean"].astype(str)
    df60 = df50.pivot_table(columns=['distance_used'], aggfunc='size')
    df60 = pd.DataFrame(df60)
    df60 = df60.rename(columns={0: 'countoo'}).reset_index()
    df50['count'] = df50[['count']].replace(to_replace =df60['distance_used'].to_list(), 
                        value =df60['countoo'].to_list())
    df50['show'] = df50["average_mean"].astype(str) + ' | ' + df50["count"].astype(str)
    df50['oneKmLit'] = 1 / df50['average']
    df50['hundredKmLit'] = 100 / df50['average']
    df50['minute'] = 60 * df50['average']
    df50['kmLitStd'] = df50['km_lit'].std()
    df50['kmLitMin'] = df50['km_lit'].min()
    df50['kmLit25Per'] = df50['km_lit'].quantile(0.25)
    df50['kmLit50Per'] = df50['km_lit'].quantile(0.5)
    df50['kmLit75Per'] = df50['km_lit'].quantile(0.75)
    df50['kmLitMax'] = df50['km_lit'].max()
    df50['countDuplicate'] = df50['count'].max()
    df50['che_liang_id_Count'] = df50['che_liang_id'].count()
    df50 = df50.fillna(0).round(decimals = 1)
    df50 = df50.to_dict('records')
    df70 = dict(petrol_id=get_petrol_id, datetime=get_datetime, che_liang_id=df50[0]["che_liang_id"], average=df50[0]["average"], 
                oneKmLit=df50[0]["oneKmLit"], minute=df50[0]["minute"], hundredKmLit=df50[0]["hundredKmLit"], average_2=df50[0]["average_2"], mean=df50[0]["mean"],
                kmLitStd=df50[0]['kmLitStd'], kmLitMin=df50[0]['kmLitMin'], kmLit25Per=df50[0]['kmLit25Per'], kmLit50Per=df50[0]['kmLit50Per'], 
                kmLit75Per=df50[0]['kmLit75Per'], kmLitMax=df50[0]['kmLitMax'], countDuplicate=df50[0]['countDuplicate'], 
                che_liang_id_Count=df50[0]['che_liang_id_Count'])
    return df70
def dfccc(session, df70):
    # # INSERT INTO CALCULATE TABLE
    db_user = Calculate(
        head=dict(petrol_id=df70['petrol_id'], datetime=str(df70['datetime']), che_liang_id=df70['che_liang_id'], average=df70['average'], minute=df70['minute'],  
                  oneKmLit=df70['oneKmLit'], hundredKmLit=df70['hundredKmLit'], average_2=df70['average_2'], mean=df70['mean'], kmLitStd=df70['kmLitStd'],
                  kmLitMin=df70['kmLitMin'], kmLit25Per=df70['kmLit25Per'], kmLit50Per=df70['kmLit50Per'], kmLit75Per=df70['kmLit75Per'], kmLitMax=df70['kmLitMax'],
                  countDuplicate=df70['countDuplicate'], che_liang_id_Count=df70['che_liang_id_Count'])
    )
    session.add(db_user)
    session.commit()
    session.refresh(db_user)
    session.close()


@router.put("/confirm_status/{petrol_id}")
async def confirmStatus(petrol_id: str, put: confirm_status):
    session = Session()
    
    get_petrol_id, get_id, get_datetime =  dfaaa(session, petrol_id, put)
    df70 =  dfbbb(session, get_petrol_id, get_id, get_datetime)
    dfccc(session, df70)
    
    return df70

