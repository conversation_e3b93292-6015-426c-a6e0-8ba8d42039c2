{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import uuid\n", "import random\n", "from datetime import date, datetime\n", "\n", "cc = uuid.uuid4().hex[:10]\n", "start = datetime.today()\n", "display(cc)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import sqlalchemy\n", "from sqlalchemy import create_engine, tuple_\n", "import requests\n", "\n", "\n", "DATABASE_URL = \"*************************************************/n_data\"\n", "engine = create_engine(DATABASE_URL)\n", "\n", "\n", "df = pd.read_sql_table('petrol', engine)\n", "df = df.sort_values(by = ['auto_id'], ascending = [False])\n", "df = df.head(1)\n", "df = df.to_dict('records')\n", "df = pd.json_normalize(df, \"data_details\", [\"auto_id\"])\n", "df = pd.DataFrame(df)\n", "# df = df.to_dict('records')\n", "display(df)\n", "\n", "df2 = pd.read_sql_table('petrol', engine)\n", "df2 = df2.sort_values(by = ['auto_id'], ascending = [False])\n", "df2 = df2.head(1)\n", "df2 = df2.to_dict('records')\n", "df2 = pd.json_normalize(df2, \"data_details\", [\"petrol_id\"])\n", "df2 = pd.DataFrame(df2)\n", "# df = df.to_dict('records')\n", "display(df2)\n", "\n", "\n", "df3 = df.merge(df2.drop_duplicates(), left_on='car',\n", "                right_on='car', how='left')\n", "# df3 = df3.to_dict('records')\n", "display(df3)\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import sqlalchemy\n", "from sqlalchemy import create_engine, tuple_\n", "import requests\n", "from functools import reduce\n", "\n", "\n", "DATABASE_URL = \"*************************************************/n_data\"\n", "engine = create_engine(DATABASE_URL)\n", "\n", "\n", "today = pd.DataFrame({'today':[pd.to_datetime(\"today\").normalize()]})\n", "today = today.to_string().replace(\"today\\n0\",\"\")\n", "display(today)\n", "\n", "database = pd.read_sql_table('petrol', engine)\n", "sort_index = database.sort_values(by = ['petrol_id'], ascending = [False])\n", "# head = sort_index.head()\n", "to_record = sort_index.to_dict('records')\n", "json_nor = pd.json_normalize(to_record, \"data_details\", [\"petrol_id\", \"datetime\"])\n", "\n", "\n", "df1 = json_nor\n", "df1 = df1.loc[:, df1.columns.isin(['store', 'petrol_id'])]\n", "df1 = df1.rename(columns={\"store\": 'jia_yi_id'})\n", "df1 = pd.DataFrame(df1)\n", "display('df1', df1)\n", "df12 = json_nor\n", "df12 = df12.loc[:, df12.columns.isin(['store', 'petrol_id'])]\n", "df12 = df12.rename(columns={\"store\": 'jia_yi_id'})\n", "df12 = pd.DataFrame(df12)\n", "df12 = df12.to_dict('records')\n", "display('df12', df12)\n", "url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'\n", "body_raw = {\"data_api\": df12}\n", "df13 = requests.get(url=url, json=body_raw).json()\n", "df13 = pd.DataFrame(df13)\n", "display('df13', df13)\n", "merge_store = pd.merge(df1, df13, on='jia_yi_id', how='left')\n", "display('merge_store', merge_store)\n", "\n", "\n", "df2 = json_nor\n", "df2 = df2.loc[:, df2.columns.isin(['car', 'petrol_id'])]\n", "df2 = df2.rename(columns={\"car\": 'jia_yi_id'})\n", "df2 = pd.DataFrame(df2)\n", "display('df2', df2)\n", "df22 = json_nor\n", "df22 = df22.loc[:, df22.columns.isin(['car', 'petrol_id'])]\n", "df22 = df22.rename(columns={\"car\": 'jia_yi_id'})\n", "df22 = pd.DataFrame(df22)\n", "df22 = df22.to_dict('records')\n", "display('df22', df22)\n", "url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'\n", "body_raw = {\"data_api\": df22}\n", "df23 = requests.get(url=url, json=body_raw).json()\n", "df23 = pd.DataFrame(df23)\n", "display('df23', df23)\n", "merge_car = pd.merge(df2, df23, on='jia_yi_id', how='left')\n", "display('merge_car', merge_car)\n", "\n", "\n", "df3 = json_nor\n", "df3 = df3.loc[:, df3.columns.isin(['petrol', 'petrol_id'])]\n", "df3 = df3.rename(columns={\"petrol\": 'product_id'})\n", "df3 = pd.DataFrame(df3)\n", "display('df3', df3)\n", "df32 = json_nor\n", "df32 = df32.loc[:, df32.columns.isin(['petrol', 'petrol_id'])]\n", "df32 = df32.rename(columns={\"petrol\": 'product_id'})\n", "df32 = pd.DataFrame(df32)\n", "df32 = df32.to_dict('records')\n", "display('df32', df32)\n", "url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/product_list_id'\n", "body_raw = {\"data_api\": df32}\n", "df33 = requests.get(url=url, json=body_raw).json()\n", "df33 = pd.DataFrame(df33)\n", "display('df33', df33)\n", "merge_petrol = pd.merge(df3, df33, on='product_id', how='left')\n", "display('merge_petrol', merge_petrol)\n", "\n", "\n", "df4 = pd.read_sql_table('bi_zhi', engine)\n", "display('df4', df4)\n", "\n", "\n", "data = json_nor\n", "data = data.loc[:, ~data.columns.isin(['store', 'car', 'petrol'])]\n", "data = pd.DataFrame(data)\n", "display('data', data)\n", "merge = [data, merge_store, merge_car, merge_petrol]\n", "merge = reduce(lambda left,right: pd.merge(left,right,on='petrol_id'), merge)\n", "merge = merge.rename(columns=dict(jia_yi_id_x='storeJia_yi_id', \n", "                                  jia_yi_idname_x='storeJia_yi_idname_x',\n", "                                  jia_yi_mm_name_x='storeJia_yi_mm_name_x',\n", "                                  jia_yi_id_y='carJia_yi_id', \n", "                                  jia_yi_idname_y='carJia_yi_idname_x',\n", "                                  jia_yi_mm_name_y='car<PERSON>ia_yi_mm_name_x',\n", "                                  product_id='petrolProduct_id',\n", "                                  product_idname='petrolProduct_idname',\n", "                                  product_mm_name='petrolProduct_mm_name', \n", "                                  product_d_name='petrolProduct_d_name',\n", "                                  th_name='petrolTh_name',\n", "                                  ))\n", "merge['datetime'] = pd.to_datetime(merge['datetime']).dt.normalize()\n", "merge['status'] = np.where(merge['datetime'] == today, True, False)\n", "display('merge', merge)\n", "\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import sqlalchemy\n", "from sqlalchemy import create_engine, tuple_\n", "import requests\n", "import numpy as np\n", "\n", "\n", "DATABASE_URL = \"*************************************************/n_data\"\n", "engine = create_engine(DATABASE_URL)\n", "\n", "\n", "df = pd.read_sql_table('petrol', engine)\n", "df = df.sort_values(by = ['petrol_id'], ascending = [False])\n", "df = df.head(3)\n", "df = df[[\"datetime\", \"petrol_id\"]]\n", "df['datetime'] = pd.to_datetime(df['datetime']).dt.normalize()\n", "display(df)\n", "\n", "today = pd.DataFrame({'today':[pd.to_datetime(\"today\").normalize()]})\n", "today = today.to_string().replace(\"today\\n0\",\"\")\n", "display(today)\n", "\n", "\n", "df2 = df['datetime'] == today\n", "df2 = df[df2]\n", "df2 = pd.DataFrame(df2)\n", "df2.loc[df2.any(1), 'status'] = True\n", "display(df2)\n", "\n", "# merge = pd.merge(df, df2, on='datetime', how='outer')\n", "# display('merge', merge)\n", "\n", "\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["today = pd.DataFrame({'today':[pd.to_datetime(\"today\").normalize()]})\n", "today = today.to_string().replace(\"today\\n0\",\"\")\n", "display(today)\n", "\n", "df = pd.read_sql_table('petrol', engine)\n", "df = df.sort_values(by = ['petrol_id'], ascending = [False])\n", "df = df.head(3)\n", "df = df[[\"datetime\", \"petrol_id\"]]\n", "df['datetime'] = pd.to_datetime(df['datetime']).dt.normalize()\n", "df['status'] = np.where(df['datetime'] == today, True, False)\n", "# df.loc[df.any(1), 'new_col'] = \"True\"\n", "display(df)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import sqlalchemy\n", "from sqlalchemy import create_engine, tuple_\n", "from sqlalchemy.pool import NullPool\n", "import requests\n", "from functools import reduce, partial\n", "import numpy as np\n", "\n", "DATABASE_URL = \"*************************************************/n_data\"\n", "engine = create_engine(DATABASE_URL, poolclass=NullPool)\n", "\n", "\n", "today = pd.DataFrame({'today':[pd.to_datetime(\"today\").normalize()]})\n", "today = today.to_string().replace(\"today\\n0\",\"\")\n", "display(\"today\", today)\n", "\n", "\n", "database = pd.read_sql_table('petrol', engine)\n", "sort_index = database.sort_values(by = ['petrol_id'], ascending = [False])\n", "head = sort_index.head(5)\n", "to_record = head.to_dict('records')\n", "json_nor = pd.json_normalize(to_record, \"data_details\", [\"petrol_id\", \"datetime\"])\n", "\n", "\n", "df1 = json_nor\n", "df1 = df1.loc[:, df1.columns.isin(['jia_yi_fang_a', 'petrol_id'])]\n", "df1 = df1.rename(columns={\"jia_yi_fang_a\": 'jia_yi_id'})\n", "df1 = pd.DataFrame(df1)\n", "display('df1', df1)\n", "url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'\n", "to_dict = df1.to_dict('records')\n", "body_raw = {\"data_api\": to_dict}\n", "df13 = requests.get(url=url, json=body_raw).json()\n", "df13 = pd.DataFrame(df13)\n", "display('df13', df13)\n", "merge_store = pd.merge(df1, df13, on='jia_yi_id', how='left')\n", "display('merge_store', merge_store)\n", "\n", "\n", "df2 = json_nor\n", "df2 = df2.loc[:, df2.columns.isin(['jia_yi_fang_b', 'petrol_id'])]\n", "df2 = df2.rename(columns={\"jia_yi_fang_b\": 'jia_yi_id'})\n", "df2 = pd.DataFrame(df2)\n", "display('df2', df2)\n", "url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'\n", "to_dict = df2.to_dict('records')\n", "body_raw = {\"data_api\": to_dict}\n", "df23 = requests.get(url=url, json=body_raw).json()\n", "df23 = pd.DataFrame(df23)\n", "display('df23', df23)\n", "merge_car = pd.merge(df2, df23, on='jia_yi_id', how='left')\n", "display('merge_car', merge_car)\n", "\n", "\n", "df3 = json_nor\n", "df3 = df3.loc[:, df3.columns.isin(['product_id', 'petrol_id'])]\n", "df3 = pd.DataFrame(df3)\n", "display('df3', df3)\n", "url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/product_list_id'\n", "to_dict = df3.to_dict('records')\n", "body_raw = {\"data_api\": to_dict}\n", "df33 = requests.get(url=url, json=body_raw).json()\n", "df33 = pd.DataFrame(df33)\n", "display('df33', df33)\n", "merge_petrol = pd.merge(df3, df33, on='product_id', how='left')\n", "display('merge_petrol', merge_petrol)\n", "\n", "\n", "df4 = json_nor\n", "df4 = df4.loc[:, df4.columns.isin(['bi_zhi', 'petrol_id'])]\n", "display('df4', df4)\n", "df42 = pd.read_sql_table('bi_zhi', engine)\n", "df42 = df42.loc[:, ~df42.columns.isin(['auto_id'])]\n", "display('df42', df42)\n", "merge_bi_zhi = pd.merge(df4, df42, on='bi_zhi', how='left')\n", "display('merge_bi_zhi', merge_bi_zhi)\n", "\n", "\n", "df5 = json_nor\n", "df5 = df5.loc[:, df5.columns.isin(['lei_a', 'ke_bian', 'petrol_id'])]\n", "df5 = df5.rename(columns={\"ke_bian\": 'jia_yi_id'})\n", "# df5.jia_yi_id = np.where(df5.jia_yi_id==0, 5, 6)\n", "df5 = pd.DataFrame(df5)\n", "display('df5', df5)\n", "df52 = pd.read_sql_table('lei_type', engine)\n", "df52 = df52.loc[:, ~df52.columns.isin(['auto_id', 'data_sub'])]\n", "df52 = pd.DataFrame(df52)\n", "display('df52', df52)\n", "url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'\n", "to_dict = df5.to_dict('records')\n", "body_raw = {\"data_api\": to_dict}\n", "df53 = requests.get(url=url, json=body_raw).json()\n", "df53 = [{\"jia_yi_id\": df53}]\n", "new_value = []\n", "for data in df53:\n", "    if data['jia_yi_id'] == False:\n", "        print('index aaa: ')\n", "        new_value.append(data)\n", "        print('new_value', new_value)\n", "    else:\n", "        print('index bbb: ')\n", "        new_value.extend(data['jia_yi_id'])\n", "        print('new_value', new_value)\n", "df53 = pd.DataFrame(new_value)\n", "display('df53', df53)\n", "merge_lei_a_ke_bian = df5.merge(df52, on='lei_a', how='left').merge(df53, on='jia_yi_id', how='left')\n", "display('merge_lei_a_ke_bian', merge_lei_a_ke_bian)\n", "\n", "\n", "data = json_nor\n", "data = data.loc[:, ~data.columns.isin(['jia_yi_fang_a', 'jia_yi_fang_b', 'product_id', 'bi_zhi', 'lei_a', 'lei_b', 'ke_bian'])]\n", "data = pd.DataFrame(data)\n", "display('data', data)\n", "merge = [data, merge_store, merge_car, merge_petrol, merge_bi_zhi, merge_lei_a_ke_bian]\n", "merge = reduce(lambda left,right: pd.merge(left,right,on='petrol_id'), merge)\n", "merge = merge.rename(columns=dict(jia_yi_id_x='storeJia_yi_id', \n", "                                jia_yi_idname_x='storeJia_yi_idname_x',\n", "                                jia_yi_mm_name_x='storeJia_yi_mm_name_x',\n", "                                jia_yi_id_y='carJia_yi_id', \n", "                                jia_yi_idname_y='carJia_yi_idname_x',\n", "                                jia_yi_mm_name_y='car<PERSON>ia_yi_mm_name_x',\n", "                                product_id='petrolProduct_id',\n", "                                product_idname='petrolProduct_idname',\n", "                                product_mm_name='petrolProduct_mm_name', \n", "                                product_d_name='petrolProduct_d_name',\n", "                                th_name='petrolTh_name',\n", "                                idname='bi_zhiIdname',\n", "                                jia_yi_id='ke_bianJia_yi_id',\n", "                                jia_yi_idname='ke_bianJia_yi_idname',\n", "                                jia_yi_mm_name='ke_bian<PERSON>ia_yi_mm_name'\n", "                                ))\n", "merge['datetime'] = pd.to_datetime(merge['datetime']).dt.normalize()\n", "merge['status_update'] = np.where(merge['datetime'] == today, True, False)\n", "# merge = merge.to_dict('records')\n", "display('merge', merge)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import sqlalchemy\n", "from sqlalchemy import create_engine, tuple_\n", "import requests\n", "from functools import reduce, partial\n", "import numpy as np\n", "\n", "DATABASE_URL = \"*************************************************/n_data\"\n", "engine = create_engine(DATABASE_URL)\n", "\n", "\n", "# t2 = pd.DataFrame({'today':[pd.to_datetime(\"today\").normalize()]})\n", "# t2 = t2.to_string().replace(\"today\\n0\",\"\")\n", "# display(\"today\", t2)\n", "\n", "today = pd.DataFrame({'today':[pd.to_datetime(\"today\")]})\n", "display(\"today\", today)\n", "\n", "\n", "df = pd.read_sql_table('petrol', engine, columns=[\"petrol_id\", \"datetime\"])\n", "df = df.sort_values(by = ['petrol_id'], ascending = [False])\n", "df = df.head(3)\n", "df['datetime'] = pd.to_datetime(df['datetime'])\n", "display(\"df\", df)\n", "\n", "\n", "merge = df.merge(today, left_on='datetime', right_on='today', how='left')\n", "merge = merge.where(pd.notnull(merge), today.to_string().replace(\"today\\n0\",\"\"))\n", "merge = merge.to_dict('records')  \n", "display('merge', merge)\n", "\n", "\n", "# for i in merge:\n", "#     print(i[\"datetime\"], i[\"today\"])\n", "#     if i[\"datetime\"] == i[\"today\"]:\n", "#         print(\"yes\")\n", "#     else: \n", "#         print(\"no\")\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from datetime import datetime, date\n", "\n", "now = datetime.now()\n", "today = date.today()\n", "# print(now, today)\n", "\n", "# compare now with today\n", "two_month_earlier = date(now.year, now.month-2, now.day)\n", "print(two_month_earlier , today)\n", "if two_month_earlier >= today:\n", "    print(\"y\")\n", "else:\n", "    print(\"F\")   \n", "\n", "two_month_earlier = datetime(now.year, now.month, now.day, now.hour, now.minute)\n", "print(two_month_earlier, now)\n", "if two_month_earlier > now:\n", "   print(\"y\")\n", "else:\n", "   print(\"F\") "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from datetime import datetime, date\n", "\n", "benchMark = datetime.strptime('20110701', \"%Y%m%d\") \n", "actualDate = datetime.strptime('20110930', \"%Y%m%d\")\n", "print(benchMark, actualDate)\n", "\n", "if actualDate.date() < benchMark.date():\n", "    print(actualDate.date(), benchMark.date())\n", "    print (\"True\")\n", "else:\n", "    print(\"F\")    "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import requests\n", "response = requests.get(\"https://61c6b93c903185001754720e.mockapi.io/api/v1/user\")\n", "print(response.json())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pandas import Timestamp\n", "import pandas as pd\n", "import numpy as np\n", "\n", "\n", "data = [\n", "    {\n", "      'id': 1,\n", "      'datetime': Timestamp('2022-1-20 16:17:52.979532')\n", "    },\n", "    {\n", "      'id': 2,\n", "      'datetime': Timestamp('2021-12-24 09:43:54.059257')\n", "    },\n", "    {\n", "      'id': 3,\n", "      'datetime': Timestamp('2021-12-23 11:43:54.059257')\n", "    }\n", "]\n", "\n", "\n", "today = pd.DataFrame({'today':[pd.to_datetime(\"today\").normalize()]})\n", "today = today.to_string().replace(\"today\\n0\",\"\")\n", "display(\"today\", today)\n", "\n", "\n", "data = pd.DataFrame(data)\n", "data['datetime'] = pd.to_datetime(data['datetime']).dt.normalize()\n", "data['status'] = np.where(data['datetime'] == today, True, False)\n", "display('data', data)\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pandas import Timestamp\n", "import pandas as pd\n", "import numpy as np\n", "from datetime import datetime\n", "\n", "\n", "data = [\n", "    {\n", "      'id': 1,\n", "      'datetime': Timestamp('2022-1-23 16:17:52.979532')\n", "    },\n", "    {\n", "      'id': 2,\n", "      'datetime': Timestamp('2022-1-24 15:43:54.059257')\n", "    },\n", "    {\n", "      'id': 3,\n", "      'datetime': Timestamp('2022-1-25 11:43:54.059257')\n", "    }\n", "]\n", "\n", "\n", "today = pd.DataFrame({'today':[pd.to_datetime(\"today\").normalize()]})\n", "today = today.to_string().replace(\"today\\n0\",\"\")\n", "display(\"today\", today)\n", "\n", "\n", "# data = pd.DataFrame(data)\n", "# data['datetime'] = pd.to_datetime(data['datetime']).dt.normalize()\n", "# data['status'] = np.where(data['datetime'] == today, True, False)\n", "# display('data', data)\n", "\n", "\n", "df1 = pd.DataFrame(data)\n", "df1 = df1.iloc[-1:]\n", "df1['datetime'] = df1['datetime'].dt.strftime(\"%Y-%m-%d\")\n", "df1['status'] = True\n", "display('df1', df1)\n", "# for i in df1['datetime']:\n", "#     print(i)\n", "#     if i == \"2022-01-25\":\n", "#       print(\"y\")\n", "#     else: \n", "#       print(\"n\") \n", "\n", "\n", "\n", "df2 = pd.DataFrame(data)\n", "df2 = df2.iloc[-2:2]\n", "df2['datetime'] = df2['datetime'].dt.strftime(\"%Y-%m-%d %H-:%M:%S\")\n", "# df2['status'] = ['red' if x == 'Z' else 'green' for x in df['Set']]\n", "# 2022-1-24 15:43:54.059257\n", "display('df2', df2)\n", "for i in df2['datetime']:\n", "    print(i)\n", "    if i <= \"2022-05-02 14-00-00\":\n", "      print(\"y\")\n", "    else: \n", "      print(\"n\")\n", "\n", "\n", "\n", "    \n", "        \n", "\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import sqlalchemy\n", "from sqlalchemy import create_engine, tuple_\n", "from sqlalchemy.pool import NullPool\n", "import requests\n", "from functools import reduce, partial\n", "import numpy as np\n", "from datetime import datetime, timedelta, date, time\n", "\n", "DATABASE_URL = \"*************************************************/n_data\"\n", "engine = create_engine(DATABASE_URL, poolclass=NullPool)\n", "\n", "\n", "database = pd.read_sql_table('petrol', engine)\n", "\n", "\n", "yesterday = pd.DataFrame({'today':[pd.to_datetime(\"today\").normalize() - <PERSON><PERSON><PERSON>(1)]})\n", "yesterday = yesterday.to_string().replace(\"       today\\n0 \",\"\")\n", "display(\"yesterday\", yesterday)\n", "\n", "\n", "today = pd.DataFrame({'today':[pd.to_datetime(\"today\")]})\n", "today[\"today\"] = today[\"today\"].dt.strftime(\"%Y-%m-%d %H:%M:%S\")\n", "today = today.to_string().replace(\"                 today\\n0  \",\"\")\n", "display(\"today\", today)\n", "\n", "\n", "todat2 = pd.DataFrame({'today':[pd.to_datetime(\"today\")]})\n", "todat2[\"today\"] = todat2[\"today\"].dt.strftime(\"%Y-%m-%d 14:00:00\")\n", "todat2 = todat2.to_string().replace(\"                 today\\n0  \",\"\")\n", "display(\"todat2\", todat2)\n", "\n", "\n", "df = pd.DataFrame(database)\n", "df = df.loc[:, df.columns.isin(['petrol_id', 'datetime'])]\n", "df = df.iloc[-1:]\n", "df = df.assign(status=True)\n", "display(\"df\", df)\n", "\n", "\n", "df2 = pd.DataFrame(database)\n", "df2 = df2.loc[:, df2.columns.isin(['petrol_id', 'datetime'])]\n", "df2 = df2.iloc[-2:-1]\n", "df2[\"datetime\"] = df2['datetime'].dt.strftime(\"%Y-%m-%d\")\n", "\n", "if yesterday == df2[\"datetime\"].all():\n", "    print(\"y\")\n", "    if today < todat2:\n", "        df2 = df2.assign(status=True)\n", "        print(\"y2\")\n", "    else: \n", "        print(\"n2\")    \n", "else: \n", "    print(\"n\")\n", "\n", "display(\"df2\", df2)\n", "\n", "\n", "concat = [df2, df]\n", "concat = pd.concat(concat)\n", "display(\"concat\", concat)\n", "\n", "\n", "table = pd.DataFrame(database)\n", "display(\"table\", table)\n", "\n", "\n", "merge = table.merge(concat, on='petrol_id', how='outer')\n", "display(\"merge\", merge)\n", "\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import sqlalchemy\n", "from sqlalchemy import create_engine, tuple_\n", "from sqlalchemy.pool import NullPool\n", "import requests\n", "from functools import reduce, partial\n", "import numpy as np\n", "from datetime import datetime, timedelta, date, time\n", "\n", "DATABASE_URL = \"*************************************************/n_data\"\n", "engine = create_engine(DATABASE_URL, poolclass=NullPool)\n", "\n", "\n", "\n", "# database = pd.read_sql_table('petrol', engine)\n", "# head = database.tail(1)\n", "# to_record = head.to_dict('records')\n", "# json_nor = pd.json_normalize(to_record, \"data_details\", [\"petrol_id\", \"datetime\"])\n", "\n", "\n", "auto_id = \"petrol00000270\"\n", "sql = \"\"\"SELECT  * from petrol where petrol_id = %s\"\"\"\n", "database = pd.read_sql_query(sql, engine, params=[auto_id])\n", "to_record = database.to_dict('records')\n", "json_nor = pd.json_normalize(to_record, \"data_details\", [\"petrol_id\", \"datetime\"])\n", "\n", "\n", "day = pd.DataFrame({'day':[pd.to_datetime(\"today\")]})\n", "day[\"day\"] = day[\"day\"].dt.strftime(\"%Y-%m-%d\")\n", "day = day.to_string().replace(\"          day\\n0  \",\"\")\n", "display(\"day\", day)\n", "\n", "\n", "day2 = pd.DataFrame({'day2':[pd.to_datetime(\"today\")]})\n", "day2[\"day2\"] = day2[\"day2\"].dt.strftime(\"%Y-%m-%d %H:%M:%S\")\n", "day2 = day2.to_string().replace(\"                  day2\\n0  \",\"\")\n", "display(\"day2\", day2)\n", "\n", "\n", "day3 = pd.DataFrame({'day3':[pd.to_datetime(\"today\")]})\n", "day3[\"day3\"] = day3[\"day3\"].dt.strftime(\"%Y-%m-%d 13:00:00\")\n", "day3 = day3.to_string().replace(\"                  day3\\n0  \",\"\")\n", "display(\"day3\", day3)\n", "\n", "\n", "yesterday = pd.DataFrame({'yesterday':[pd.to_datetime(\"today\").normalize() - <PERSON><PERSON><PERSON>(1)]})\n", "yesterday = yesterday.to_string().replace(\"   yesterday\\n0 \",\"\")\n", "display(\"yesterday\", yesterday)\n", "\n", "\n", "staUp = pd.DataFrame(json_nor)\n", "staUp = staUp.loc[:, staUp.columns.isin(['petrol_id', 'datetime'])]\n", "staUp[\"datetime\"] = staUp['datetime'].dt.strftime(\"%Y-%m-%d\")\n", "\n", "if day == staUp[\"datetime\"].all():\n", "    print(\"day\")\n", "    staUp[\"status_update\"] = True\n", "elif yesterday == staUp[\"datetime\"].all():\n", "    print(\"yesterday\")\n", "    if day2 < day3:\n", "        print(\"less up\")\n", "        staUp[\"status_update\"] = True\n", "    else:\n", "        print(\"more no\")\n", "        staUp[\"status_update\"] = False    \n", "else:\n", "    print(\"else\")\n", "    staUp[\"status_update\"] = False\n", "\n", "display(\"staUp\", staUp)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import sqlalchemy\n", "from sqlalchemy import create_engine, tuple_\n", "from sqlalchemy.pool import NullPool\n", "import requests\n", "from functools import reduce, partial\n", "import numpy as np\n", "from datetime import datetime, timedelta, date, time\n", "\n", "DATABASE_URL = \"*************************************************/n_data\"\n", "engine = create_engine(DATABASE_URL, poolclass=NullPool)\n", "\n", "\n", "day = pd.DataFrame({'day':[pd.to_datetime(\"today\")]})\n", "day[\"day\"] = day[\"day\"].dt.strftime(\"%Y-%m-%d\")\n", "day = day.to_string().replace(\"          day\\n0  \",\"\")\n", "display(\"day\", day)\n", "\n", "\n", "day2 = pd.DataFrame({'day2':[pd.to_datetime(\"today\")]})\n", "day2[\"day2\"] = day2[\"day2\"].dt.strftime(\"%Y-%m-%d %H:%M:%S\")\n", "day2 = day2.to_string().replace(\"                  day2\\n0  \",\"\")\n", "display(\"day2\", day2)\n", "\n", "\n", "day3 = pd.DataFrame({'day3':[pd.to_datetime(\"today\")]})\n", "day3[\"day3\"] = day3[\"day3\"].dt.strftime(\"%Y-%m-%d 13:00:00\")\n", "day3 = day3.to_string().replace(\"                  day3\\n0  \",\"\")\n", "display(\"day3\", day3)\n", "\n", "\n", "yesterday = pd.DataFrame({'yesterday':[pd.to_datetime(\"today\").normalize() - <PERSON><PERSON><PERSON>(1)]})\n", "yesterday = yesterday.to_string().replace(\"   yesterday\\n0 \",\"\")\n", "display(\"yesterday\", yesterday)\n", "\n", "\n", "# database = pd.read_sql_table('petrol', engine)\n", "# sort_index = database.sort_values(by = ['petrol_id'], ascending = [False])\n", "# head = sort_index.head(1)\n", "# to_record = head.to_dict('records')\n", "# json_nor = pd.json_normalize(to_record, \"data_details\", [\"petrol_id\", \"datetime\"])\n", "\n", "\n", "auto_id = \"petrol00000268\"\n", "sql = \"\"\"SELECT  * from petrol where petrol_id = %s\"\"\"\n", "database = pd.read_sql_query(sql, engine, params=[auto_id])\n", "to_record = database.to_dict('records')\n", "json_nor = pd.json_normalize(to_record, \"data_details\", [\"petrol_id\", \"datetime\"])\n", "\n", "\n", "df1 = json_nor\n", "df1 = df1.loc[:, df1.columns.isin(['jia_yi_fang_a', 'petrol_id'])]\n", "df1 = df1.rename(columns={\"jia_yi_fang_a\": 'jia_yi_id'})\n", "df1 = pd.DataFrame(df1)\n", "display('df1', df1)\n", "url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'\n", "to_dict = df1.to_dict('records')\n", "body_raw = {\"data_api\": to_dict}\n", "df13 = requests.get(url=url, json=body_raw).json()\n", "df13 = pd.DataFrame(df13)\n", "display('df13', df13)\n", "merge_store = pd.merge(df1, df13, on='jia_yi_id', how='left')\n", "display('merge_store', merge_store)\n", "\n", "\n", "df2 = json_nor\n", "df2 = df2.loc[:, df2.columns.isin(['jia_yi_fang_b', 'petrol_id'])]\n", "df2 = df2.rename(columns={\"jia_yi_fang_b\": 'jia_yi_id'})\n", "df2 = pd.DataFrame(df2)\n", "display('df2', df2)\n", "url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'\n", "to_dict = df2.to_dict('records')\n", "body_raw = {\"data_api\": to_dict}\n", "df23 = requests.get(url=url, json=body_raw).json()\n", "df23 = pd.DataFrame(df23)\n", "display('df23', df23)\n", "merge_car = pd.merge(df2, df23, on='jia_yi_id', how='left')\n", "display('merge_car', merge_car)\n", "\n", "\n", "df3 = json_nor\n", "df3 = df3.loc[:, df3.columns.isin(['product_id', 'petrol_id'])]\n", "df3 = pd.DataFrame(df3)\n", "display('df3', df3)\n", "url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/product_list_id'\n", "to_dict = df3.to_dict('records')\n", "body_raw = {\"data_api\": to_dict}\n", "df33 = requests.get(url=url, json=body_raw).json()\n", "df33 = pd.DataFrame(df33)\n", "display('df33', df33)\n", "merge_petrol = pd.merge(df3, df33, on='product_id', how='left')\n", "display('merge_petrol', merge_petrol)\n", "\n", "\n", "df4 = json_nor\n", "df4 = df4.loc[:, df4.columns.isin(['bi_zhi', 'petrol_id'])]\n", "display('df4', df4)\n", "df42 = pd.read_sql_table('bi_zhi', engine)\n", "df42 = df42.loc[:, ~df42.columns.isin(['auto_id'])]\n", "display('df42', df42)\n", "merge_bi_zhi = pd.merge(df4, df42, on='bi_zhi', how='left')\n", "display('merge_bi_zhi', merge_bi_zhi)\n", "\n", "\n", "df5 = json_nor\n", "df5 = df5.loc[:, df5.columns.isin(['lei_a', 'ke_bian', 'petrol_id'])]\n", "df5 = df5.rename(columns={\"ke_bian\": 'jia_yi_id'})\n", "# df5.jia_yi_id = np.where(df5.jia_yi_id==0, 5, 6)\n", "df5 = pd.DataFrame(df5)\n", "display('df5', df5)\n", "df52 = pd.read_sql_table('lei_type', engine)\n", "df52 = df52.loc[:, ~df52.columns.isin(['auto_id', 'data_sub'])]\n", "df52 = pd.DataFrame(df52)\n", "display('df52', df52)\n", "url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'\n", "to_dict = df5.to_dict('records')\n", "body_raw = {\"data_api\": to_dict}\n", "df53 = requests.get(url=url, json=body_raw).json()\n", "df53 = [{\"jia_yi_id\": df53}]\n", "new_value = []\n", "for data in df53:\n", "    if data['jia_yi_id'] == False:\n", "        print('index aaa: ')\n", "        new_value.append(data)\n", "        print('new_value', new_value)\n", "    else:\n", "        print('index bbb: ')\n", "        new_value.extend(data['jia_yi_id'])\n", "        print('new_value', new_value)\n", "df53 = pd.DataFrame(new_value)\n", "display('df53', df53)\n", "merge_lei_a_ke_bian = df5.merge(df52, on='lei_a', how='left').merge(df53, on='jia_yi_id', how='left')\n", "display('merge_lei_a_ke_bian', merge_lei_a_ke_bian)\n", "\n", "\n", "data = json_nor\n", "data = data.loc[:, ~data.columns.isin(['jia_yi_fang_a', 'jia_yi_fang_b', 'product_id', 'bi_zhi', 'lei_a', 'lei_b', 'ke_bian'])]\n", "data = pd.DataFrame(data)\n", "display('data', data)\n", "merge = [data, merge_store, merge_car, merge_petrol, merge_bi_zhi, merge_lei_a_ke_bian]\n", "merge = reduce(lambda left,right: pd.merge(left,right,on='petrol_id'), merge)\n", "merge = merge.rename(columns=dict(jia_yi_id_x='storeJia_yi_id', \n", "                                jia_yi_idname_x='storeJia_yi_idname_x',\n", "                                jia_yi_mm_name_x='storeJia_yi_mm_name_x',\n", "                                jia_yi_id_y='carJia_yi_id', \n", "                                jia_yi_idname_y='carJia_yi_idname_x',\n", "                                jia_yi_mm_name_y='car<PERSON>ia_yi_mm_name_x',\n", "                                product_id='petrolProduct_id',\n", "                                product_idname='petrolProduct_idname',\n", "                                product_mm_name='petrolProduct_mm_name', \n", "                                product_d_name='petrolProduct_d_name',\n", "                                th_name='petrolTh_name',\n", "                                idname='bi_zhiIdname',\n", "                                jia_yi_id='ke_bianJia_yi_id',\n", "                                jia_yi_idname='ke_bianJia_yi_idname',\n", "                                jia_yi_mm_name='ke_bian<PERSON>ia_yi_mm_name'\n", "                                ))\n", "# merge['datetime'] = pd.to_datetime(merge['datetime']).dt.normalize()\n", "# merge['status_update'] = np.where(merge['datetime'] == today, True, False)\n", "merge[\"datetime\"] = merge['datetime'].dt.strftime(\"%Y-%m-%d\")\n", "if day == merge[\"datetime\"].all():\n", "    print(\"day\")\n", "    merge[\"status_update\"] = True\n", "elif yesterday == merge[\"datetime\"].all():\n", "    print(\"yesterday\")\n", "    if day2 < day3:\n", "        print(\"less up\")\n", "        merge[\"status_update\"] = True\n", "    else:\n", "        print(\"more no\")\n", "        merge[\"status_update\"] = False    \n", "else:\n", "    print(\"else\")\n", "    merge[\"status_update\"] = False\n", "merge = merge.to_dict('records')\n", "display('merge', merge)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import sqlalchemy\n", "from sqlalchemy import create_engine, tuple_\n", "from sqlalchemy.pool import NullPool\n", "import requests\n", "from functools import reduce, partial\n", "import numpy as np\n", "\n", "DATABASE_URL = \"*************************************************/n_data\"\n", "engine = create_engine(DATABASE_URL, poolclass=NullPool)\n", "\n", "\n", "today = pd.DataFrame({'today':[pd.to_datetime(\"today\").normalize()]})\n", "today = today.to_string().replace(\"today\\n0\",\"\")\n", "display(\"today\", today)\n", "\n", "\n", "# database = pd.read_sql_table('petrol', engine)\n", "# sort_index = database.sort_values(by = ['petrol_id'], ascending = [False])\n", "# head = sort_index.head(1)\n", "# to_record = head.to_dict('records')\n", "# json_nor = pd.json_normalize(to_record, \"data_details\", [\"petrol_id\", \"datetime\"])\n", "\n", "\n", "auto_id = \"petrol00000379\"\n", "sql = \"\"\"SELECT  * from petrol where petrol_id = %s\"\"\"\n", "database = pd.read_sql_query(sql, engine, params=[auto_id])\n", "to_record = database.to_dict('records')\n", "json_nor = pd.json_normalize(to_record, \"data_details\", [\"petrol_id\", \"datetime\"])\n", "\n", "\n", "df1 = json_nor\n", "df1 = df1.loc[:, df1.columns.isin(['jia_yi_fang_a', 'petrol_id'])]\n", "df1 = df1.rename(columns={\"jia_yi_fang_a\": 'jia_yi_id'})\n", "df1 = pd.DataFrame(df1)\n", "display('df1', df1)\n", "url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'\n", "to_dict = df1.to_dict('records')\n", "body_raw = {\"data_api\": to_dict}\n", "df13 = requests.get(url=url, json=body_raw).json()\n", "df13 = pd.DataFrame(df13)\n", "display('df13', df13)\n", "merge_store = pd.merge(df1, df13, on='jia_yi_id', how='left')\n", "display('merge_store', merge_store)\n", "\n", "\n", "df2 = json_nor\n", "df2 = df2.loc[:, df2.columns.isin(['jia_yi_fang_b', 'petrol_id'])]\n", "df2 = df2.rename(columns={\"jia_yi_fang_b\": 'jia_yi_id'})\n", "df2 = pd.DataFrame(df2)\n", "display('df2', df2)\n", "url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'\n", "to_dict = df2.to_dict('records')\n", "body_raw = {\"data_api\": to_dict}\n", "df23 = requests.get(url=url, json=body_raw).json()\n", "df23 = pd.DataFrame(df23)\n", "display('df23', df23)\n", "merge_car = pd.merge(df2, df23, on='jia_yi_id', how='left')\n", "display('merge_car', merge_car)\n", "\n", "\n", "df3 = json_nor\n", "df3 = df3.loc[:, df3.columns.isin(['product_id', 'petrol_id'])]\n", "df3 = pd.DataFrame(df3)\n", "display('df3', df3)\n", "url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/product_list_id'\n", "to_dict = df3.to_dict('records')\n", "body_raw = {\"data_api\": to_dict}\n", "df33 = requests.get(url=url, json=body_raw).json()\n", "df33 = pd.DataFrame(df33)\n", "display('df33', df33)\n", "merge_petrol = pd.merge(df3, df33, on='product_id', how='left')\n", "display('merge_petrol', merge_petrol)\n", "\n", "\n", "df4 = json_nor\n", "df4 = df4.loc[:, df4.columns.isin(['bi_zhi', 'petrol_id'])]\n", "display('df4', df4)\n", "df42 = pd.read_sql_table('bi_zhi', engine)\n", "df42 = df42.loc[:, ~df42.columns.isin(['auto_id'])]\n", "display('df42', df42)\n", "merge_bi_zhi = pd.merge(df4, df42, on='bi_zhi', how='left')\n", "display('merge_bi_zhi', merge_bi_zhi)\n", "\n", "\n", "df5 = json_nor\n", "df5 = df5.loc[:, df5.columns.isin(['lei_a', 'ke_bian', 'petrol_id'])]\n", "df5 = df5.rename(columns={\"ke_bian\": 'jia_yi_id'})\n", "# df5.jia_yi_id = np.where(df5.jia_yi_id==0, 5, 6)\n", "df5 = pd.DataFrame(df5)\n", "display('df5', df5)\n", "df52 = pd.read_sql_table('lei_type', engine)\n", "df52 = df52.loc[:, ~df52.columns.isin(['auto_id', 'data_sub'])]\n", "df52 = pd.DataFrame(df52)\n", "display('df52', df52)\n", "url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'\n", "to_dict = df5.to_dict('records')\n", "body_raw = {\"data_api\": to_dict}\n", "df53 = requests.get(url=url, json=body_raw).json()\n", "df53 = [{\"jia_yi_id\": df53}]\n", "new_value = []\n", "for data in df53:\n", "    if data['jia_yi_id'] == False:\n", "        print('index aaa: ')\n", "        new_value.append(data)\n", "        print('new_value', new_value)\n", "    else:\n", "        print('index bbb: ')\n", "        new_value.extend(data['jia_yi_id'])\n", "        print('new_value', new_value)\n", "df53 = pd.DataFrame(new_value)\n", "display('df53', df53)\n", "merge_lei_a_ke_bian = df5.merge(df52, on='lei_a', how='left').merge(df53, on='jia_yi_id', how='left')\n", "display('merge_lei_a_ke_bian', merge_lei_a_ke_bian)\n", "\n", "\n", "data = json_nor\n", "data = data.loc[:, ~data.columns.isin(['jia_yi_fang_a', 'jia_yi_fang_b', 'product_id', 'bi_zhi', 'lei_a', 'lei_b', 'ke_bian'])]\n", "data = pd.DataFrame(data)\n", "display('data', data)\n", "merge = [data, merge_store, merge_car, merge_petrol, merge_bi_zhi, merge_lei_a_ke_bian]\n", "merge = reduce(lambda left,right: pd.merge(left,right,on='petrol_id'), merge)\n", "merge = merge.rename(columns=dict(jia_yi_id_x='storeJia_yi_id', \n", "                                jia_yi_idname_x='storeJia_yi_idname_x',\n", "                                jia_yi_mm_name_x='storeJia_yi_mm_name_x',\n", "                                jia_yi_id_y='carJia_yi_id', \n", "                                jia_yi_idname_y='carJia_yi_idname_x',\n", "                                jia_yi_mm_name_y='car<PERSON>ia_yi_mm_name_x',\n", "                                product_id='petrolProduct_id',\n", "                                product_idname='petrolProduct_idname',\n", "                                product_mm_name='petrolProduct_mm_name', \n", "                                product_d_name='petrolProduct_d_name',\n", "                                th_name='petrolTh_name',\n", "                                idname='bi_zhiIdname',\n", "                                jia_yi_id='ke_bianJia_yi_id',\n", "                                jia_yi_idname='ke_bianJia_yi_idname',\n", "                                jia_yi_mm_name='ke_bian<PERSON>ia_yi_mm_name'\n", "                                ))\n", "merge['datetime'] = pd.to_datetime(merge['datetime']).dt.normalize()\n", "merge['status_update'] = np.where(merge['datetime'] == today, True, False)\n", "\n", "merge = merge.loc[:, ['petrol_id', 'datetime', 'sub_id', 'r_id', 'u_id', 'che_liang_id', \n", "                      'storeJia_yi_id', 'storeJia_yi_idname_x', 'storeJia_yi_mm_name_x',\n", "                      'carJia_yi_id', 'carJia_yi_idname_x', 'carJia_yi_mm_name_x',\n", "                      'petrolProduct_id', 'petrolProduct_idname', 'petrolProduct_mm_name',\n", "                      'petrolProduct_d_name', 'petrolTh_name', 'price', 'qty', 'distant', 'bi_zhi',\n", "                      'bi_zhiIdname', 'lei_a', 'lei_b', 'lei_name', 'ke_bianJia_yi_id', 'status_update']\n", "                  ]\n", "\n", "# merge = merge.to_dict('records')\n", "# merge = merge['che_liang_id']\n", "display('merge', merge)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import sqlalchemy\n", "from sqlalchemy import create_engine, tuple_\n", "from sqlalchemy.pool import NullPool\n", "import numpy as np\n", "import json\n", "from sqlalchemy.ext.declarative import declarative_base\n", "from sqlalchemy import Column, Integer, String, Date, DateTime, ForeignKey, TEXT\n", "from sqlalchemy.sql.expression import table, text\n", "from sqlalchemy.dialects.postgresql.json import JSONB\n", "from sqlalchemy.orm import sessionmaker\n", "from sqlalchemy import and_, or_, not_\n", "from sqlalchemy import literal\n", "import datetime\n", "import itertools\n", "\n", "DATABASE_URL = \"*************************************************/test_nern\"\n", "engine = create_engine(DATABASE_URL, poolclass=NullPool)\n", "Session = sessionmaker(bind=engine)\n", "\n", "Base = declarative_base()\n", "\n", "class PetrolModel(Base):\n", "    __tablename__ = 'petrol'\n", "    auto_id = Column(Integer, primary_key=True)\n", "    petrol_id = Column(TEXT(convert_unicode=True), server_default=text(\"'petrol'||lpad(NEXTVAL('petrol_sequence'::regclass)::text,8, '0')\"))\n", "    datetime = Column(DateTime)\n", "    data_details = Column(JSONB, nullable=False, server_default='[]')\n", "\n", "\n", "session = Session()\n", "\n", "\n", "today = datetime.date.today()\n", "future_day = today.day\n", "future_month = (today.month) % 12\n", "future_year = today.year + ((today.month) // 12)\n", "six_months_ago = datetime.date(future_year, future_month, future_day)\n", "print(six_months_ago)\n", "print(type(six_months_ago))\n", "df20 = pd.read_sql(sql = session.query(PetrolModel).order_by(PetrolModel.datetime.desc()).filter(PetrolModel.datetime > six_months_ago,).statement, con = session.bind)\n", "df20 = df20.to_dict('records')\n", "df20 = pd.json_normalize(df20, \"data_details\", [\"auto_id\", \"petrol_id\", \"datetime\"])\n", "display(df20)\n", "\n", "\n", "df30 = df20\n", "df30 = df30.loc[:, df30.columns.isin(['che_liang_id', 'petrol_id', 'product_id'])]\n", "df30 = df30.rename(columns={\"che_liang_id\": 'jia_yi_id'})\n", "display('df30', df30)\n", "\n", "\n", "url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'\n", "to_dict = df30.to_dict('records')\n", "body_raw = {\"data_api\": to_dict}\n", "df40 = requests.get(url=url, json=body_raw).json()\n", "df40 = pd.DataFrame(df40)\n", "display('df40', df40)\n", "\n", "\n", "url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/product_list_id'\n", "to_dict = df30.to_dict('records')\n", "body_raw = {\"data_api\": to_dict}\n", "df50 = requests.get(url=url, json=body_raw).json()\n", "df50 = pd.DataFrame(df50)\n", "display('df50', df50)\n", "\n", "\n", "merge = df20.merge(df40, left_on='che_liang_id', right_on='jia_yi_id', how='left').merge(df50, on='product_id', how='left')\n", "merge = merge.to_dict('records')\n", "display('merge', merge)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import sqlalchemy\n", "from sqlalchemy import create_engine, tuple_\n", "from sqlalchemy.pool import NullPool\n", "import numpy as np\n", "import json\n", "from sqlalchemy.ext.declarative import declarative_base\n", "from sqlalchemy import Column, Integer, String, Date, DateTime, ForeignKey, TEXT\n", "from sqlalchemy.sql.expression import table, text\n", "from sqlalchemy.dialects.postgresql.json import JSONB\n", "from sqlalchemy.orm import sessionmaker\n", "from sqlalchemy import and_, or_, not_\n", "from sqlalchemy import literal\n", "import datetime\n", "import itertools\n", "import requests\n", "\n", "DATABASE_URL = \"*************************************************/test_nern\"\n", "engine = create_engine(DATABASE_URL, poolclass=NullPool)\n", "Session = sessionmaker(bind=engine)\n", "\n", "Base = declarative_base()\n", "\n", "class PetrolModel(Base):\n", "    __tablename__ = 'petrol'\n", "    auto_id = Column(Integer, primary_key=True)\n", "    petrol_id = Column(TEXT(convert_unicode=True), server_default=text(\"'petrol'||lpad(NEXTVAL('petrol_sequence'::regclass)::text,8, '0')\"))\n", "    datetime = Column(DateTime)\n", "    data_details = Column(JSONB, nullable=False, server_default='[]')\n", "    status_details = Column(JSONB, nullable=False, server_default='{}')\n", "\n", "session = Session()\n", "\n", "\n", "today = datetime.date.today()\n", "future_day = today.day\n", "future_month = (today.month) % 12\n", "future_year = today.year + ((today.month) // 12)\n", "six_months_ago = datetime.date(future_year, future_month, future_day)\n", "print(six_months_ago)\n", "print(type(six_months_ago))\n", "df20 = pd.read_sql(sql = session.query(PetrolModel).order_by(PetrolModel.datetime.desc()).filter(PetrolModel.datetime > six_months_ago,).statement, con = session.bind)\n", "df20 = df20.to_dict('records')\n", "df20 = pd.json_normalize(df20, \"data_details\", [\"auto_id\", \"petrol_id\", \"datetime\", \"status_details\"])\n", "df20 = df20.to_dict('records')\n", "df20 = pd.json_normalize(df20)\n", "df20 = df20.rename(columns={\"status_details.status\": 'status'})\n", "display(df20)\n", "\n", "\n", "df30 = df20\n", "df30 = df30.loc[:, df30.columns.isin(['che_liang_id', 'petrol_id', 'product_id'])]\n", "df30 = df30.rename(columns={\"che_liang_id\": 'jia_yi_id'})\n", "display('df30', df30)\n", "\n", "\n", "url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'\n", "to_dict = df30.to_dict('records')\n", "body_raw = {\"data_api\": to_dict}\n", "df40 = requests.get(url=url, json=body_raw).json()\n", "df40 = pd.DataFrame(df40)\n", "display('df40', df40)\n", "\n", "\n", "url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/product_list_id'\n", "to_dict = df30.to_dict('records')\n", "body_raw = {\"data_api\": to_dict}\n", "df50 = requests.get(url=url, json=body_raw).json()\n", "df50 = pd.DataFrame(df50)\n", "display('df50', df50)\n", "\n", "\n", "merge = df20.merge(df40, left_on='che_liang_id', right_on='jia_yi_id', how='left').merge(df50, on='product_id', how='left')\n", "# merge = merge.to_dict('records')\n", "display('merge', merge)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import sqlalchemy\n", "from sqlalchemy import create_engine, tuple_\n", "from sqlalchemy.pool import NullPool\n", "import numpy as np\n", "import json\n", "from sqlalchemy.ext.declarative import declarative_base\n", "from sqlalchemy import Column, Integer, String, Date, DateTime, ForeignKey, TEXT\n", "from sqlalchemy.sql.expression import table, text\n", "from sqlalchemy.dialects.postgresql.json import JSONB\n", "from sqlalchemy.orm import sessionmaker\n", "from sqlalchemy import and_, or_, not_\n", "from sqlalchemy import literal\n", "import datetime\n", "import itertools\n", "import requests\n", "\n", "DATABASE_URL = \"*************************************************/test_nern\"\n", "engine = create_engine(DATABASE_URL, poolclass=NullPool)\n", "Session = sessionmaker(bind=engine)\n", "\n", "Base = declarative_base()\n", "\n", "class PetrolModel(Base):\n", "    __tablename__ = 'petrol'\n", "    auto_id = Column(Integer, primary_key=True)\n", "    petrol_id = Column(TEXT(convert_unicode=True), server_default=text(\"'petrol'||lpad(NEXTVAL('petrol_sequence'::regclass)::text,8, '0')\"))\n", "    datetime = Column(DateTime)\n", "    data_details = Column(JSONB, nullable=False, server_default='[]')\n", "    status_details = Column(JSONB, nullable=False, server_default='{}')\n", "\n", "session = Session()\n", "\n", "\n", "today = datetime.date.today()\n", "future_day = today.day\n", "future_month = (today.month) % 12\n", "future_year = today.year + ((today.month) // 12)\n", "six_months_ago = datetime.date(future_year, future_month, future_day)\n", "print(six_months_ago)\n", "print(type(six_months_ago))\n", "\n", "df20 = pd.read_sql(sql = session.query(PetrolModel)\\\n", "                        .with_entities(\n", "                            PetrolModel.petrol_id,\n", "                            PetrolModel.datetime,\n", "                            PetrolModel.data_details[0]['bill'],\n", "                            )\n", "                            .order_by(PetrolModel.datetime.desc())\n", "                            .filter(\n", "                                and_(\n", "                                    PetrolModel.datetime > six_months_ago,\n", "                                    PetrolModel.data_details[0]['bill'].astext.cast(String) == \"waiting\",\n", "                                    )\n", "                                ).statement,\n", "                con = session.bind)\n", "\n", "df20 = df20.rename(columns={\n", "    \"anon_1\": 'bill',\n", "    })\n", "display(df20)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import sqlalchemy\n", "from sqlalchemy import create_engine, tuple_\n", "from sqlalchemy.pool import NullPool\n", "import requests\n", "from functools import reduce, partial\n", "import numpy as np\n", "\n", "DATABASE_URL = \"*************************************************/test_nern\"\n", "engine = create_engine(DATABASE_URL, poolclass=NullPool)\n", "\n", "\n", "today = pd.DataFrame({'today':[pd.to_datetime(\"today\").normalize()]})\n", "today = today.to_string().replace(\"today\\n0\",\"\")\n", "display(\"today\", today)\n", "\n", "\n", "# database = pd.read_sql_table('petrol', engine)\n", "# sort_index = database.sort_values(by = ['petrol_id'], ascending = [False])\n", "# head = sort_index.head(1)\n", "# to_record = head.to_dict('records')\n", "# json_nor = pd.json_normalize(to_record, \"data_details\", [\"petrol_id\", \"datetime\"])\n", "\n", "\n", "auto_id = \"petrol00000354\"\n", "sql = \"\"\"SELECT  * from petrol where petrol_id = %s\"\"\"\n", "database = pd.read_sql_query(sql, engine, params=[auto_id])\n", "to_record = database.to_dict('records')\n", "json_nor = pd.json_normalize(to_record, \"data_details\", [\"petrol_id\", \"datetime\"])\n", "\n", "\n", "df1 = json_nor\n", "df1 = df1.loc[:, df1.columns.isin(['jia_yi_fang_a', 'petrol_id'])]\n", "df1 = df1.rename(columns={\"jia_yi_fang_a\": 'jia_yi_id'})\n", "df1 = pd.DataFrame(df1)\n", "display('df1', df1)\n", "try: \n", "    url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'\n", "    to_dict = df1.to_dict('records')\n", "    body_raw = {\"data_api\": to_dict}\n", "    df13 = requests.get(url=url, json=body_raw).json()\n", "    df13 = pd.DataFrame(df13)\n", "    display('df13', df13)\n", "except:\n", "    d = {'jia_yi_id': [0], 'jia_yi_idname': [\"\"], 'jia_yi_mm_name': [\"\"]}\n", "    df13 = pd.DataFrame(d)\n", "    display('df13', df13)\n", "merge_store = pd.merge(df1, df13, on='jia_yi_id', how='left')\n", "display('merge_store', merge_store)\n", "\n", "\n", "df2 = json_nor\n", "df2 = df2.loc[:, df2.columns.isin(['jia_yi_fang_b', 'petrol_id'])]\n", "df2 = df2.rename(columns={\"jia_yi_fang_b\": 'jia_yi_id'})\n", "df2 = pd.DataFrame(df2)\n", "display('df2', df2)\n", "url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'\n", "to_dict = df2.to_dict('records')\n", "body_raw = {\"data_api\": to_dict}\n", "df23 = requests.get(url=url, json=body_raw).json()\n", "df23 = pd.DataFrame(df23)\n", "display('df23', df23)\n", "merge_car = pd.merge(df2, df23, on='jia_yi_id', how='left')\n", "display('merge_car', merge_car)\n", "\n", "\n", "df3 = json_nor\n", "df3 = df3.loc[:, df3.columns.isin(['product_id', 'petrol_id'])]\n", "df3 = pd.DataFrame(df3)\n", "display('df3', df3)\n", "url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/product_list_id'\n", "to_dict = df3.to_dict('records')\n", "body_raw = {\"data_api\": to_dict}\n", "df33 = requests.get(url=url, json=body_raw).json()\n", "df33 = pd.DataFrame(df33)\n", "display('df33', df33)\n", "merge_petrol = pd.merge(df3, df33, on='product_id', how='left')\n", "display('merge_petrol', merge_petrol)\n", "\n", "\n", "df4 = json_nor\n", "df4 = df4.loc[:, df4.columns.isin(['bi_zhi', 'petrol_id'])]\n", "display('df4', df4)\n", "df42 = pd.read_sql_table('bi_zhi', engine)\n", "df42 = df42.loc[:, ~df42.columns.isin(['auto_id'])]\n", "display('df42', df42)\n", "merge_bi_zhi = pd.merge(df4, df42, on='bi_zhi', how='left')\n", "display('merge_bi_zhi', merge_bi_zhi)\n", "\n", "\n", "df5 = json_nor\n", "df5 = df5.loc[:, df5.columns.isin(['lei_a', 'ke_bian', 'petrol_id'])]\n", "df5 = df5.rename(columns={\"ke_bian\": 'jia_yi_id'})\n", "# df5.jia_yi_id = np.where(df5.jia_yi_id==0, 5, 6)\n", "df5 = pd.DataFrame(df5)\n", "display('df5', df5)\n", "df52 = pd.read_sql_table('lei_type', engine)\n", "df52 = df52.loc[:, ~df52.columns.isin(['auto_id', 'data_sub'])]\n", "df52 = pd.DataFrame(df52)\n", "display('df52', df52)\n", "url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'\n", "to_dict = df5.to_dict('records')\n", "body_raw = {\"data_api\": to_dict}\n", "df53 = requests.get(url=url, json=body_raw).json()\n", "df53 = [{\"jia_yi_id\": df53}]\n", "new_value = []\n", "for data in df53:\n", "    if data['jia_yi_id'] == False:\n", "        print('index aaa: ')\n", "        new_value.append(data)\n", "        print('new_value', new_value)\n", "    else:\n", "        print('index bbb: ')\n", "        new_value.extend(data['jia_yi_id'])\n", "        print('new_value', new_value)\n", "df53 = pd.DataFrame(new_value)\n", "display('df53', df53)\n", "merge_lei_a_ke_bian = df5.merge(df52, on='lei_a', how='left').merge(df53, on='jia_yi_id', how='left')\n", "display('merge_lei_a_ke_bian', merge_lei_a_ke_bian)\n", "\n", "\n", "data = json_nor\n", "data = data.loc[:, ~data.columns.isin(['jia_yi_fang_a', 'jia_yi_fang_b', 'product_id', 'bi_zhi', 'lei_a', 'lei_b', 'ke_bian'])]\n", "data = pd.DataFrame(data)\n", "display('data', data)\n", "merge = [data, merge_store, merge_car, merge_petrol, merge_bi_zhi, merge_lei_a_ke_bian]\n", "merge = reduce(lambda left,right: pd.merge(left,right,on='petrol_id'), merge)\n", "merge = merge.rename(columns=dict(jia_yi_id_x='storeJia_yi_id', \n", "                                jia_yi_idname_x='storeJia_yi_idname_x',\n", "                                jia_yi_mm_name_x='storeJia_yi_mm_name_x',\n", "                                jia_yi_id_y='carJia_yi_id', \n", "                                jia_yi_idname_y='carJia_yi_idname_x',\n", "                                jia_yi_mm_name_y='car<PERSON>ia_yi_mm_name_x',\n", "                                product_id='petrolProduct_id',\n", "                                product_idname='petrolProduct_idname',\n", "                                product_mm_name='petrolProduct_mm_name', \n", "                                product_d_name='petrolProduct_d_name',\n", "                                th_name='petrolTh_name',\n", "                                idname='bi_zhiIdname',\n", "                                jia_yi_id='ke_bianJia_yi_id',\n", "                                jia_yi_idname='ke_bianJia_yi_idname',\n", "                                jia_yi_mm_name='ke_bian<PERSON>ia_yi_mm_name'\n", "                                ))\n", "merge['datetime'] = pd.to_datetime(merge['datetime']).dt.normalize()\n", "merge['status_update'] = np.where(merge['datetime'] == today, True, False)\n", "\n", "merge = merge.loc[:, ['petrol_id', 'datetime', 'sub_id', 'r_id', 'u_id', 'che_liang_id', \n", "                      'storeJia_yi_id', 'storeJia_yi_idname_x', 'storeJia_yi_mm_name_x',\n", "                      'carJia_yi_id', 'carJia_yi_idname_x', 'carJia_yi_mm_name_x',\n", "                      'petrolProduct_id', 'petrolProduct_idname', 'petrolProduct_mm_name',\n", "                      'petrolProduct_d_name', 'petrolTh_name', 'price', 'qty', 'distant', 'bi_zhi',\n", "                      'bi_zhiIdname', 'lei_a', 'lei_b', 'lei_name', 'ke_bianJia_yi_id', 'status_update']\n", "                  ]\n", "\n", "merge = merge.to_dict('records')\n", "display('merge', merge)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import sqlalchemy\n", "from sqlalchemy import create_engine, tuple_\n", "from sqlalchemy.pool import NullPool\n", "import requests\n", "from functools import reduce, partial\n", "import numpy as np\n", "from datetime import datetime, timedelta, date, time\n", "\n", "DATABASE_URL = \"*************************************************/test_nern\"\n", "engine = create_engine(DATABASE_URL, poolclass=NullPool)\n", "\n", "\n", "day = pd.DataFrame({'day':[pd.to_datetime(\"today\")]})\n", "day[\"day\"] = day[\"day\"].dt.strftime(\"%Y-%m-%d\")\n", "day = day.to_string().replace(\"          day\\n0  \",\"\")\n", "display(\"day\", day)\n", "\n", "\n", "day2 = pd.DataFrame({'day2':[pd.to_datetime(\"today\")]})\n", "day2[\"day2\"] = day2[\"day2\"].dt.strftime(\"%Y-%m-%d %H:%M:%S\")\n", "day2 = day2.to_string().replace(\"                  day2\\n0  \",\"\")\n", "display(\"day2\", day2)\n", "\n", "\n", "day3 = pd.DataFrame({'day3':[pd.to_datetime(\"today\")]})\n", "day3[\"day3\"] = day3[\"day3\"].dt.strftime(\"%Y-%m-%d 15:00:00\")\n", "day3 = day3.to_string().replace(\"                  day3\\n0  \",\"\")\n", "display(\"day3\", day3)\n", "\n", "\n", "yesterday = pd.DataFrame({'yesterday':[pd.to_datetime(\"today\").normalize() - <PERSON><PERSON><PERSON>(1)]})\n", "yesterday = yesterday.to_string().replace(\"   yesterday\\n0 \",\"\")\n", "display(\"yesterday\", yesterday)\n", "\n", "\n", "# database = pd.read_sql_table('petrol', engine)\n", "# sort_index = database.sort_values(by = ['petrol_id'], ascending = [False])\n", "# head = sort_index.head(1)\n", "# to_record = head.to_dict('records')\n", "# json_nor = pd.json_normalize(to_record, \"data_details\", [\"petrol_id\", \"datetime\"])\n", "\n", "\n", "auto_id = \"petrol00000630\"\n", "sql = \"\"\"SELECT  * from petrol where petrol_id = %s\"\"\"\n", "database = pd.read_sql_query(sql, engine, params=[auto_id])\n", "to_record = database.to_dict('records')\n", "json_nor = pd.json_normalize(to_record, \"data_details\", [\"petrol_id\", \"datetime\"])\n", "\n", "\n", "df1 = json_nor\n", "df1 = df1.loc[:, df1.columns.isin(['jia_yi_fang_a', 'petrol_id'])]\n", "df1 = df1.rename(columns={\"jia_yi_fang_a\": 'jia_yi_id'})\n", "df1 = pd.DataFrame(df1)\n", "display('df1', df1)\n", "url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'\n", "to_dict = df1.to_dict('records')\n", "body_raw = {\"data_api\": to_dict}\n", "df13 = requests.get(url=url, json=body_raw).json()\n", "df13 = pd.DataFrame(df13)\n", "display('df13', df13)\n", "merge_store = pd.merge(df1, df13, on='jia_yi_id', how='left')\n", "display('merge_store', merge_store)\n", "\n", "\n", "df2 = json_nor\n", "df2 = df2.loc[:, df2.columns.isin(['jia_yi_fang_b', 'petrol_id'])]\n", "df2 = df2.rename(columns={\"jia_yi_fang_b\": 'jia_yi_id'})\n", "df2 = pd.DataFrame(df2)\n", "display('df2', df2)\n", "url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'\n", "to_dict = df2.to_dict('records')\n", "body_raw = {\"data_api\": to_dict}\n", "df23 = requests.get(url=url, json=body_raw).json()\n", "df23 = pd.DataFrame(df23)\n", "display('df23', df23)\n", "merge_car = pd.merge(df2, df23, on='jia_yi_id', how='left')\n", "display('merge_car', merge_car)\n", "\n", "\n", "df3 = json_nor\n", "df3 = df3.loc[:, df3.columns.isin(['product_id', 'petrol_id'])]\n", "df3 = pd.DataFrame(df3)\n", "display('df3', df3)\n", "url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/product_list_id'\n", "to_dict = df3.to_dict('records')\n", "body_raw = {\"data_api\": to_dict}\n", "df33 = requests.get(url=url, json=body_raw).json()\n", "df33 = pd.DataFrame(df33)\n", "display('df33', df33)\n", "merge_petrol = pd.merge(df3, df33, on='product_id', how='left')\n", "display('merge_petrol', merge_petrol)\n", "\n", "\n", "df4 = json_nor\n", "df4 = df4.loc[:, df4.columns.isin(['bi_zhi', 'petrol_id'])]\n", "display('df4', df4)\n", "df42 = pd.read_sql_table('bi_zhi', engine)\n", "df42 = df42.loc[:, ~df42.columns.isin(['auto_id'])]\n", "display('df42', df42)\n", "merge_bi_zhi = pd.merge(df4, df42, on='bi_zhi', how='left')\n", "display('merge_bi_zhi', merge_bi_zhi)\n", "\n", "\n", "df5 = json_nor\n", "df5 = df5.loc[:, df5.columns.isin(['lei_a', 'ke_bian', 'petrol_id'])]\n", "df5 = df5.rename(columns={\"ke_bian\": 'jia_yi_id'})\n", "# df5.jia_yi_id = np.where(df5.jia_yi_id==0, 5, 6)\n", "df5 = pd.DataFrame(df5)\n", "display('df5', df5)\n", "df52 = pd.read_sql_table('lei_type', engine)\n", "df52 = df52.loc[:, ~df52.columns.isin(['auto_id', 'data_sub'])]\n", "df52 = pd.DataFrame(df52)\n", "display('df52', df52)\n", "url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'\n", "to_dict = df5.to_dict('records')\n", "body_raw = {\"data_api\": to_dict}\n", "df53 = requests.get(url=url, json=body_raw).json()\n", "df53 = [{\"jia_yi_id\": df53}]\n", "new_value = []\n", "for data in df53:\n", "    if data['jia_yi_id'] == False:\n", "        print('index aaa: ')\n", "        new_value.append(data)\n", "        print('new_value', new_value)\n", "    else:\n", "        print('index bbb: ')\n", "        new_value.extend(data['jia_yi_id'])\n", "        print('new_value', new_value)\n", "df53 = pd.DataFrame(new_value)\n", "display('df53', df53)\n", "merge_lei_a_ke_bian = df5.merge(df52, on='lei_a', how='left').merge(df53, on='jia_yi_id', how='left')\n", "display('merge_lei_a_ke_bian', merge_lei_a_ke_bian)\n", "\n", "\n", "data = json_nor\n", "data = data.loc[:, ~data.columns.isin(['jia_yi_fang_a', 'jia_yi_fang_b', 'product_id', 'bi_zhi', 'lei_a', 'lei_b', 'ke_bian'])]\n", "data = pd.DataFrame(data)\n", "display('data', data)\n", "merge = [data, merge_store, merge_car, merge_petrol, merge_bi_zhi, merge_lei_a_ke_bian]\n", "merge = reduce(lambda left,right: pd.merge(left,right,on='petrol_id'), merge)\n", "merge = merge.rename(columns=dict(jia_yi_id_x='storeJia_yi_id', \n", "                                jia_yi_idname_x='storeJia_yi_idname_x',\n", "                                jia_yi_mm_name_x='storeJia_yi_mm_name_x',\n", "                                jia_yi_id_y='carJia_yi_id', \n", "                                jia_yi_idname_y='carJia_yi_idname_x',\n", "                                jia_yi_mm_name_y='car<PERSON>ia_yi_mm_name_x',\n", "                                product_id='petrolProduct_id',\n", "                                product_idname='petrolProduct_idname',\n", "                                product_mm_name='petrolProduct_mm_name', \n", "                                product_d_name='petrolProduct_d_name',\n", "                                th_name='petrolTh_name',\n", "                                idname='bi_zhiIdname',\n", "                                jia_yi_id='ke_bianJia_yi_id',\n", "                                jia_yi_idname='ke_bianJia_yi_idname',\n", "                                jia_yi_mm_name='ke_bian<PERSON>ia_yi_mm_name'\n", "                                ))\n", "# merge['datetime'] = pd.to_datetime(merge['datetime']).dt.normalize()\n", "# merge['status_update'] = np.where(merge['datetime'] == today, True, False)\n", "merge[\"datetime\"] = merge['datetime'].dt.strftime(\"%Y-%m-%d\")\n", "if day == merge[\"datetime\"].all():\n", "    print(\"day\", day, merge[\"datetime\"])\n", "    merge[\"status_update\"] = True\n", "elif yesterday == merge[\"datetime\"].all():\n", "    print(\"yesterday\", yesterday, merge[\"datetime\"])\n", "    if day2 < day3:\n", "        print(\"less up\", day2, day3)\n", "        merge[\"status_update\"] = True\n", "    else:\n", "        print(\"more no\", day2, day3)\n", "        merge[\"status_update\"] = False    \n", "else:\n", "    print(\"else\")\n", "    merge[\"status_update\"] = False\n", "# merge = merge.to_dict('records')\n", "display('merge', merge)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import sqlalchemy\n", "from sqlalchemy import create_engine, tuple_\n", "from sqlalchemy.pool import NullPool\n", "import requests\n", "from functools import reduce, partial\n", "import numpy as np\n", "from datetime import datetime, timedelta, date, time\n", "\n", "DATABASE_URL = \"*************************************************/test_nern\"\n", "engine = create_engine(DATABASE_URL, poolclass=NullPool)\n", "\n", "\n", "day = pd.DataFrame({'day':[pd.to_datetime(\"today\")]})\n", "day[\"day\"] = day[\"day\"].dt.strftime(\"%Y-%m-%d\")\n", "day = day.to_string().replace(\"          day\\n0  \",\"\")\n", "display(\"day\", day)\n", "\n", "\n", "day2 = pd.DataFrame({'day2':[pd.to_datetime(\"today\")]})\n", "day2[\"day2\"] = day2[\"day2\"].dt.strftime(\"%Y-%m-%d %H:%M:%S\")\n", "day2 = day2.to_string().replace(\"                  day2\\n0  \",\"\")\n", "display(\"day2\", day2)\n", "\n", "\n", "day3 = pd.DataFrame({'day3':[pd.to_datetime(\"today\")]})\n", "day3[\"day3\"] = day3[\"day3\"].dt.strftime(\"%Y-%m-%d 16:00:00\")\n", "day3 = day3.to_string().replace(\"                  day3\\n0  \",\"\")\n", "display(\"day3\", day3)\n", "\n", "\n", "yesterday = pd.DataFrame({'yesterday':[pd.to_datetime(\"today\").normalize() - <PERSON><PERSON><PERSON>(1)]})\n", "yesterday = yesterday.to_string().replace(\"   yesterday\\n0 \",\"\")\n", "display(\"yesterday\", yesterday)\n", "\n", "\n", "# database = pd.read_sql_table('petrol', engine)\n", "# sort_index = database.sort_values(by = ['petrol_id'], ascending = [False])\n", "# head = sort_index.head(1)\n", "# to_record = head.to_dict('records')\n", "# json_nor = pd.json_normalize(to_record, \"data_details\", [\"petrol_id\", \"datetime\"])\n", "\n", "\n", "auto_id = \"petrol00000630\"\n", "sql = \"\"\"SELECT  * from petrol where petrol_id = %s\"\"\"\n", "database = pd.read_sql_query(sql, engine, params=[auto_id])\n", "to_record = database.to_dict('records')\n", "json_nor = pd.json_normalize(to_record, \"data_details\", [\"petrol_id\", \"datetime\"])\n", "\n", "\n", "df1 = json_nor\n", "df1 = df1.loc[:, df1.columns.isin(['jia_yi_fang_a', 'petrol_id'])]\n", "df1 = df1.rename(columns={\"jia_yi_fang_a\": 'jia_yi_id'})\n", "df1 = pd.DataFrame(df1)\n", "# display('df1', df1)\n", "try: \n", "    url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'\n", "    to_dict = df1.to_dict('records')\n", "    body_raw = {\"data_api\": to_dict}\n", "    df13 = requests.get(url=url, json=body_raw).json()\n", "    df13 = pd.DataFrame(df13)\n", "    # display('df13', df13)\n", "except:\n", "    d = {'jia_yi_id': [0], 'jia_yi_idname': [\"\"], 'jia_yi_mm_name': [\"\"]}\n", "    df13 = pd.DataFrame(d)\n", "    # display('df13', df13)\n", "merge_store = pd.merge(df1, df13, on='jia_yi_id', how='left')\n", "# display('merge_store', merge_store)\n", "\n", "\n", "df2 = json_nor\n", "df2 = df2.loc[:, df2.columns.isin(['jia_yi_fang_b', 'petrol_id'])]\n", "df2 = df2.rename(columns={\"jia_yi_fang_b\": 'jia_yi_id'})\n", "df2 = pd.DataFrame(df2)\n", "# display('df2', df2)\n", "url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'\n", "# url = Arter_api + 'jia_yi_name_list_id'\n", "to_dict = df2.to_dict('records')\n", "body_raw = {\"data_api\": to_dict}\n", "df23 = requests.get(url=url, json=body_raw).json()\n", "df23 = pd.DataFrame(df23)\n", "# display('df23', df23)\n", "merge_car = pd.merge(df2, df23, on='jia_yi_id', how='left')\n", "# display('merge_car', merge_car)\n", "\n", "\n", "df3 = json_nor\n", "df3 = df3.loc[:, df3.columns.isin(['product_id', 'petrol_id'])]\n", "df3 = pd.DataFrame(df3)\n", "# display('df3', df3)\n", "url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/product_list_id'\n", "# url = Arter_api + 'product_list_id'\n", "to_dict = df3.to_dict('records')\n", "body_raw = {\"data_api\": to_dict}\n", "df33 = requests.get(url=url, json=body_raw).json()\n", "df33 = pd.DataFrame(df33)\n", "# display('df33', df33)\n", "merge_petrol = pd.merge(df3, df33, on='product_id', how='left')\n", "# display('merge_petrol', merge_petrol)\n", "\n", "\n", "df4 = json_nor\n", "df4 = df4.loc[:, df4.columns.isin(['bi_zhi', 'petrol_id'])]\n", "# display('df4', df4)\n", "df42 = pd.read_sql_table('bi_zhi', engine)\n", "df42 = df42.loc[:, ~df42.columns.isin(['auto_id'])]\n", "# # display('df42', df42)\n", "merge_bi_zhi = pd.merge(df4, df42, on='bi_zhi', how='left')\n", "# display('merge_bi_zhi', merge_bi_zhi)\n", "\n", "\n", "df5 = json_nor\n", "df5 = df5.loc[:, df5.columns.isin(['lei_a', 'ke_bian', 'petrol_id'])]\n", "df5 = df5.rename(columns={\"ke_bian\": 'jia_yi_id'})\n", "df5 = pd.DataFrame(df5)\n", "# display('df5', df5)\n", "df52 = pd.read_sql_table('lei_type', engine)\n", "df52 = df52.loc[:, ~df52.columns.isin(['auto_id', 'data_sub'])]\n", "df52 = pd.DataFrame(df52)\n", "# display('df52', df52)\n", "url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'\n", "# url = Arter_api + 'jia_yi_name_list_id'\n", "to_dict = df5.to_dict('records')\n", "body_raw = {\"data_api\": to_dict}\n", "df53 = requests.get(url=url, json=body_raw).json()\n", "df53 = [{\"jia_yi_id\": df53}]\n", "new_value = []\n", "for data in df53:\n", "    if data['jia_yi_id'] == False:\n", "        new_value.append(data)\n", "    else:\n", "        new_value.extend(data['jia_yi_id'])\n", "df53 = pd.DataFrame(new_value)\n", "# display('df53', df53)\n", "merge_lei_a_ke_bian = df5.merge(df52, on='lei_a', how='left').merge(df53, on='jia_yi_id', how='left')\n", "# display('merge_lei_a_ke_bian', merge_lei_a_ke_bian)\n", "\n", "\n", "data = json_nor\n", "data = data.loc[:, ~data.columns.isin(['jia_yi_fang_a', 'jia_yi_fang_b', 'product_id', 'bi_zhi', 'lei_a', 'lei_b', 'ke_bian'])]\n", "data = pd.DataFrame(data)\n", "# display('data', data)\n", "merge = [data, merge_store, merge_car, merge_petrol, merge_bi_zhi, merge_lei_a_ke_bian]\n", "merge = reduce(lambda left,right: pd.merge(left,right,on='petrol_id'), merge)\n", "merge = merge.rename(columns=dict(jia_yi_id_x='storeJia_yi_id', \n", "                                jia_yi_idname_x='storeJia_yi_idname_x',\n", "                                jia_yi_mm_name_x='storeJia_yi_mm_name_x',\n", "                                jia_yi_id_y='carJia_yi_id', \n", "                                jia_yi_idname_y='carJia_yi_idname_x',\n", "                                jia_yi_mm_name_y='car<PERSON>ia_yi_mm_name_x',\n", "                                product_id='petrolProduct_id',\n", "                                product_idname='petrolProduct_idname',\n", "                                product_mm_name='petrolProduct_mm_name', \n", "                                product_d_name='petrolProduct_d_name',\n", "                                th_name='petrolTh_name',\n", "                                idname='bi_zhiIdname',\n", "                                jia_yi_id='ke_bianJia_yi_id',\n", "                                jia_yi_idname='ke_bianJia_yi_idname',\n", "                                jia_yi_mm_name='ke_bian<PERSON>ia_yi_mm_name'\n", "                                ))\n", "merge[\"datetime\"] = merge['datetime'].dt.strftime(\"%Y-%m-%d\")\n", "if day == merge[\"datetime\"].all():\n", "    print(\"day\", day, merge[\"datetime\"])\n", "    merge[\"status_update\"] = True\n", "elif yesterday == merge[\"datetime\"].all():\n", "    print(\"yesterday\", yesterday, merge[\"datetime\"])\n", "    if day2 < day3:\n", "        print(\"less up\", day2, day3)\n", "        merge[\"status_update\"] = True\n", "    else:\n", "        print(\"more no\", day2, day3)\n", "        merge[\"status_update\"] = False    \n", "else:\n", "    print(\"else\")\n", "    merge[\"status_update\"] = False\n", "# merge = merge.to_dict('records')\n", "display('merge', merge)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2022-10-24\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_8333/2101560504.py:27: SADeprecationWarning: The String.convert_unicode parameter is deprecated and will be removed in a future release.  All modern DBAPIs now support Python Unicode directly and this parameter is unnecessary.\n", "  petrol_id = Column(TEXT(convert_unicode=True), server_default=text(\"'petrol'||lpad(NEXTVAL('petrol_sequence'::regclass)::text,8, '0')\"))\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>auto_id</th>\n", "      <th>petrol_id</th>\n", "      <th>datetime</th>\n", "      <th>data_details</th>\n", "      <th>status_details</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2164</td>\n", "      <td>petrol00002280</td>\n", "      <td>2022-10-24 08:38:01.709900</td>\n", "      <td>[{'qty': 30.0, 'bill': '', 'r_id': 'f802c90a86...</td>\n", "      <td>{'status': 'success'}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2166</td>\n", "      <td>petrol00002282</td>\n", "      <td>2022-10-24 11:26:45.864222</td>\n", "      <td>[{'qty': 30.0, 'bill': 'success', 'r_id': '4ee...</td>\n", "      <td>{'status': 'success'}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2163</td>\n", "      <td>petrol00002279</td>\n", "      <td>2022-10-24 08:34:40.625700</td>\n", "      <td>[{'qty': 30.0, 'bill': '', 'r_id': '3ae0f46302...</td>\n", "      <td>{'status': 'success'}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2165</td>\n", "      <td>petrol00002281</td>\n", "      <td>2022-10-24 08:57:21.623404</td>\n", "      <td>[{'qty': 30.0, 'bill': '', 'r_id': 'b97b2c9799...</td>\n", "      <td>{'status': 'success'}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2167</td>\n", "      <td>petrol00002283</td>\n", "      <td>2022-10-24 12:56:45.663860</td>\n", "      <td>[{'qty': 30.0, 'bill': '', 'r_id': '642732f06d...</td>\n", "      <td>{'status': 'success'}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>2168</td>\n", "      <td>petrol00002284</td>\n", "      <td>2022-10-24 13:28:29.544882</td>\n", "      <td>[{'qty': 30.0, 'bill': '', 'r_id': 'cfbceb7815...</td>\n", "      <td>{'status': 'success'}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>2170</td>\n", "      <td>petrol00002286</td>\n", "      <td>2022-10-24 16:13:58.066088</td>\n", "      <td>[{'qty': 30.0, 'bill': 'success', 'r_id': 'b95...</td>\n", "      <td>{'status': 'success'}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>2172</td>\n", "      <td>petrol00002288</td>\n", "      <td>2022-10-24 16:17:23.769226</td>\n", "      <td>[{'qty': 2.64, 'bill': '', 'r_id': 'b98cd67664...</td>\n", "      <td>{'status': 'success'}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>2169</td>\n", "      <td>petrol00002285</td>\n", "      <td>2022-10-24 16:13:47.045921</td>\n", "      <td>[{'qty': 30.0, 'bill': 'success', 'r_id': '3d7...</td>\n", "      <td>{'status': 'success'}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>2173</td>\n", "      <td>petrol00002289</td>\n", "      <td>2022-10-24 16:29:59.130092</td>\n", "      <td>[{'qty': 30.0, 'bill': 'success', 'r_id': 'dda...</td>\n", "      <td>{'status': 'success'}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>2171</td>\n", "      <td>petrol00002287</td>\n", "      <td>2022-10-24 16:14:10.413439</td>\n", "      <td>[{'qty': 30.0, 'bill': 'success', 'r_id': '1b9...</td>\n", "      <td>{'status': 'success'}</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    auto_id       petrol_id                   datetime  \\\n", "0      2164  petrol00002280 2022-10-24 08:38:01.709900   \n", "1      2166  petrol00002282 2022-10-24 11:26:45.864222   \n", "2      2163  petrol00002279 2022-10-24 08:34:40.625700   \n", "3      2165  petrol00002281 2022-10-24 08:57:21.623404   \n", "4      2167  petrol00002283 2022-10-24 12:56:45.663860   \n", "5      2168  petrol00002284 2022-10-24 13:28:29.544882   \n", "6      2170  petrol00002286 2022-10-24 16:13:58.066088   \n", "7      2172  petrol00002288 2022-10-24 16:17:23.769226   \n", "8      2169  petrol00002285 2022-10-24 16:13:47.045921   \n", "9      2173  petrol00002289 2022-10-24 16:29:59.130092   \n", "10     2171  petrol00002287 2022-10-24 16:14:10.413439   \n", "\n", "                                         data_details         status_details  \n", "0   [{'qty': 30.0, 'bill': '', 'r_id': 'f802c90a86...  {'status': 'success'}  \n", "1   [{'qty': 30.0, 'bill': 'success', 'r_id': '4ee...  {'status': 'success'}  \n", "2   [{'qty': 30.0, 'bill': '', 'r_id': '3ae0f46302...  {'status': 'success'}  \n", "3   [{'qty': 30.0, 'bill': '', 'r_id': 'b97b2c9799...  {'status': 'success'}  \n", "4   [{'qty': 30.0, 'bill': '', 'r_id': '642732f06d...  {'status': 'success'}  \n", "5   [{'qty': 30.0, 'bill': '', 'r_id': 'cfbceb7815...  {'status': 'success'}  \n", "6   [{'qty': 30.0, 'bill': 'success', 'r_id': 'b95...  {'status': 'success'}  \n", "7   [{'qty': 2.64, 'bill': '', 'r_id': 'b98cd67664...  {'status': 'success'}  \n", "8   [{'qty': 30.0, 'bill': 'success', 'r_id': '3d7...  {'status': 'success'}  \n", "9   [{'qty': 30.0, 'bill': 'success', 'r_id': 'dda...  {'status': 'success'}  \n", "10  [{'qty': 30.0, 'bill': 'success', 'r_id': '1b9...  {'status': 'success'}  "]}, "metadata": {}, "output_type": "display_data"}], "source": ["import pandas as pd\n", "import sqlalchemy\n", "from sqlalchemy import create_engine, tuple_\n", "from sqlalchemy.pool import NullPool\n", "import numpy as np\n", "import json\n", "from sqlalchemy.ext.declarative import declarative_base\n", "from sqlalchemy import Column, Integer, String, Date, DateTime, ForeignKey, TEXT\n", "from sqlalchemy.sql.expression import table, text\n", "from sqlalchemy.dialects.postgresql.json import JSONB\n", "from sqlalchemy.orm import sessionmaker\n", "from sqlalchemy import and_, or_, not_\n", "from sqlalchemy import literal\n", "import datetime\n", "import itertools\n", "import requests\n", "\n", "DATABASE_URL = \"*************************************************/n_data\"\n", "engine = create_engine(DATABASE_URL, poolclass=NullPool)\n", "Session = sessionmaker(bind=engine)\n", "\n", "Base = declarative_base()\n", "\n", "class PetrolModel(Base):\n", "    __tablename__ = 'petrol'\n", "    auto_id = Column(Integer, primary_key=True)\n", "    petrol_id = Column(TEXT(convert_unicode=True), server_default=text(\"'petrol'||lpad(NEXTVAL('petrol_sequence'::regclass)::text,8, '0')\"))\n", "    datetime = Column(DateTime)\n", "    data_details = Column(JSONB, nullable=False, server_default='[]')\n", "    status_details = Column(JSONB, nullable=False, server_default='{}')\n", "\n", "session = Session()\n", "\n", "\n", "import datetime as DT\n", "today = DT.date.today()\n", "week_ago = today - DT.<PERSON><PERSON><PERSON>(days=1)\n", "print(week_ago)\n", "\n", "df20 = pd.read_sql(sql = session.query(PetrolModel)\\\n", "                        .filter(PetrolModel.datetime > week_ago)\n", "                        .filter(PetrolModel.status_details['status'].astext.cast(String) == \"success\")\n", "                        .statement, con = session.bind)\n", "display(df20)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"interpreter": {"hash": "916dbcbb3f70747c44a77c7bcd40155683ae19c65e1c03b4aa3499c5328201f1"}, "kernelspec": {"display_name": "Python 3.8.10 64-bit", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.4"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}