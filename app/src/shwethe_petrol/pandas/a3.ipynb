{"cells": [{"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2023-06-23\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>petrol_id</th>\n", "      <th>datetime</th>\n", "      <th>datetime_bill</th>\n", "      <th>che_liang_id</th>\n", "      <th>distant</th>\n", "      <th>qty</th>\n", "      <th>driver</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>petrol00004637</td>\n", "      <td>2023-06-23 13:22:41.866880</td>\n", "      <td>2023-06-23</td>\n", "      <td>37650</td>\n", "      <td>3325.1</td>\n", "      <td>30.0</td>\n", "      <td>26978</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>petrol00004658</td>\n", "      <td>2023-06-26 14:18:12.598418</td>\n", "      <td>2023-06-26</td>\n", "      <td>37650</td>\n", "      <td>3344.4</td>\n", "      <td>30.0</td>\n", "      <td>26978</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>petrol00004694</td>\n", "      <td>2023-06-29 10:16:50.146706</td>\n", "      <td>2023-06-29</td>\n", "      <td>37650</td>\n", "      <td>3362.6</td>\n", "      <td>30.0</td>\n", "      <td>26978</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>petrol00004712</td>\n", "      <td>2023-07-01 16:39:26.715881</td>\n", "      <td>2023-07-01</td>\n", "      <td>37650</td>\n", "      <td>3381.4</td>\n", "      <td>30.0</td>\n", "      <td>26978</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>petrol00004734</td>\n", "      <td>2023-07-04 15:42:54.156781</td>\n", "      <td>2023-07-04</td>\n", "      <td>37650</td>\n", "      <td>3400.2</td>\n", "      <td>30.0</td>\n", "      <td>26978</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>petrol00004755</td>\n", "      <td>2023-07-07 09:28:16.959619</td>\n", "      <td>2023-07-07</td>\n", "      <td>37650</td>\n", "      <td>3416.9</td>\n", "      <td>30.0</td>\n", "      <td>26978</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>petrol00004788</td>\n", "      <td>2023-07-10 12:05:17.814391</td>\n", "      <td>2023-07-10</td>\n", "      <td>37650</td>\n", "      <td>3433.1</td>\n", "      <td>30.0</td>\n", "      <td>26978</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>petrol00004808</td>\n", "      <td>2023-07-13 14:13:14.168053</td>\n", "      <td>2023-07-13</td>\n", "      <td>37650</td>\n", "      <td>3435.9</td>\n", "      <td>30.0</td>\n", "      <td>26978</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>petrol00005108</td>\n", "      <td>2023-08-20 15:58:46.083962</td>\n", "      <td>2023-08-20</td>\n", "      <td>37650</td>\n", "      <td>0.0</td>\n", "      <td>30.0</td>\n", "      <td>39582</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>petrol00005146</td>\n", "      <td>2023-08-24 16:52:01.930658</td>\n", "      <td>2023-08-24</td>\n", "      <td>37650</td>\n", "      <td>8.0</td>\n", "      <td>30.0</td>\n", "      <td>39582</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>petrol00005190</td>\n", "      <td>2023-08-31 10:09:42.869021</td>\n", "      <td>2023-08-31</td>\n", "      <td>37650</td>\n", "      <td>24.0</td>\n", "      <td>30.0</td>\n", "      <td>39582</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>petrol00005237</td>\n", "      <td>2023-09-06 10:49:55.365990</td>\n", "      <td>2023-09-06</td>\n", "      <td>37650</td>\n", "      <td>39.0</td>\n", "      <td>30.0</td>\n", "      <td>39582</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>petrol00005286</td>\n", "      <td>2023-09-12 12:02:03.977819</td>\n", "      <td>2023-09-12</td>\n", "      <td>37650</td>\n", "      <td>55.0</td>\n", "      <td>30.0</td>\n", "      <td>39582</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>petrol00005331</td>\n", "      <td>2023-09-19 08:40:13.654229</td>\n", "      <td>2023-09-19</td>\n", "      <td>37650</td>\n", "      <td>72.0</td>\n", "      <td>30.0</td>\n", "      <td>39582</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>petrol00005378</td>\n", "      <td>2023-09-26 13:27:09.350794</td>\n", "      <td>2023-09-26</td>\n", "      <td>37650</td>\n", "      <td>88.0</td>\n", "      <td>30.0</td>\n", "      <td>39582</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>petrol00005417</td>\n", "      <td>2023-09-30 16:10:19.787719</td>\n", "      <td>2023-09-30</td>\n", "      <td>37650</td>\n", "      <td>105.0</td>\n", "      <td>30.0</td>\n", "      <td>39582</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>petrol00005447</td>\n", "      <td>2023-10-05 15:39:16.382202</td>\n", "      <td>2023-10-05</td>\n", "      <td>37650</td>\n", "      <td>120.0</td>\n", "      <td>30.0</td>\n", "      <td>39582</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>petrol00005493</td>\n", "      <td>2023-10-11 13:39:31.793235</td>\n", "      <td>2023-10-11</td>\n", "      <td>37650</td>\n", "      <td>136.0</td>\n", "      <td>30.0</td>\n", "      <td>39582</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>petrol00005533</td>\n", "      <td>2023-10-16 13:38:28.790825</td>\n", "      <td>2023-10-16</td>\n", "      <td>37650</td>\n", "      <td>153.0</td>\n", "      <td>30.0</td>\n", "      <td>39582</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>petrol00005567</td>\n", "      <td>2023-10-21 11:22:33.942122</td>\n", "      <td>2023-10-21</td>\n", "      <td>37650</td>\n", "      <td>170.0</td>\n", "      <td>30.0</td>\n", "      <td>39582</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>petrol00005600</td>\n", "      <td>2023-10-25 15:08:47.772799</td>\n", "      <td>2023-10-25</td>\n", "      <td>37650</td>\n", "      <td>186.0</td>\n", "      <td>30.0</td>\n", "      <td>39582</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>petrol00005632</td>\n", "      <td>2023-10-30 12:12:53.210274</td>\n", "      <td>2023-10-30</td>\n", "      <td>37650</td>\n", "      <td>203.0</td>\n", "      <td>30.0</td>\n", "      <td>39582</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>petrol00005661</td>\n", "      <td>2023-11-05 09:02:50.988901</td>\n", "      <td>2023-11-05</td>\n", "      <td>37650</td>\n", "      <td>220.0</td>\n", "      <td>30.0</td>\n", "      <td>39582</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>petrol00005704</td>\n", "      <td>2023-11-11 15:48:04.422161</td>\n", "      <td>2023-11-11</td>\n", "      <td>37650</td>\n", "      <td>237.0</td>\n", "      <td>30.0</td>\n", "      <td>39582</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>petrol00005744</td>\n", "      <td>2023-11-16 16:19:38.438093</td>\n", "      <td>2023-11-16</td>\n", "      <td>37650</td>\n", "      <td>254.0</td>\n", "      <td>30.0</td>\n", "      <td>39582</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>petrol00005779</td>\n", "      <td>2023-11-21 16:25:25.641610</td>\n", "      <td>2023-11-21</td>\n", "      <td>37650</td>\n", "      <td>274.6</td>\n", "      <td>30.0</td>\n", "      <td>42588</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>petrol00005804</td>\n", "      <td>2023-11-25 08:13:44.708101</td>\n", "      <td>2023-11-25</td>\n", "      <td>37650</td>\n", "      <td>297.2</td>\n", "      <td>30.0</td>\n", "      <td>42588</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>petrol00005823</td>\n", "      <td>2023-11-28 09:18:33.660674</td>\n", "      <td>2023-11-28</td>\n", "      <td>37650</td>\n", "      <td>318.2</td>\n", "      <td>30.0</td>\n", "      <td>42588</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td>petrol00005845</td>\n", "      <td>2023-12-01 08:46:19.574344</td>\n", "      <td>2023-12-01</td>\n", "      <td>37650</td>\n", "      <td>339.6</td>\n", "      <td>30.0</td>\n", "      <td>42588</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29</th>\n", "      <td>petrol00005871</td>\n", "      <td>2023-12-03 15:57:58.630397</td>\n", "      <td>2023-12-03</td>\n", "      <td>37650</td>\n", "      <td>360.3</td>\n", "      <td>30.0</td>\n", "      <td>42588</td>\n", "    </tr>\n", "    <tr>\n", "      <th>30</th>\n", "      <td>petrol00005893</td>\n", "      <td>2023-12-07 14:22:12.877893</td>\n", "      <td>2023-12-07</td>\n", "      <td>37650</td>\n", "      <td>382.0</td>\n", "      <td>30.0</td>\n", "      <td>42588</td>\n", "    </tr>\n", "    <tr>\n", "      <th>31</th>\n", "      <td>petrol00005916</td>\n", "      <td>2023-12-10 14:03:21.498166</td>\n", "      <td>2023-12-10</td>\n", "      <td>37650</td>\n", "      <td>401.2</td>\n", "      <td>30.0</td>\n", "      <td>42588</td>\n", "    </tr>\n", "    <tr>\n", "      <th>32</th>\n", "      <td>petrol00005936</td>\n", "      <td>2023-12-13 15:16:11.406496</td>\n", "      <td>2023-12-13</td>\n", "      <td>37650</td>\n", "      <td>420.8</td>\n", "      <td>30.0</td>\n", "      <td>42588</td>\n", "    </tr>\n", "    <tr>\n", "      <th>33</th>\n", "      <td>petrol00005956</td>\n", "      <td>2023-12-16 14:54:16.538475</td>\n", "      <td>2023-12-16</td>\n", "      <td>37650</td>\n", "      <td>441.1</td>\n", "      <td>30.0</td>\n", "      <td>42588</td>\n", "    </tr>\n", "    <tr>\n", "      <th>34</th>\n", "      <td>petrol00005980</td>\n", "      <td>2023-12-19 14:15:19.885071</td>\n", "      <td>2023-12-19</td>\n", "      <td>37650</td>\n", "      <td>462.2</td>\n", "      <td>30.0</td>\n", "      <td>42588</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         petrol_id                   datetime datetime_bill  che_liang_id  \\\n", "0   petrol00004637 2023-06-23 13:22:41.866880    2023-06-23         37650   \n", "1   petrol00004658 2023-06-26 14:18:12.598418    2023-06-26         37650   \n", "2   petrol00004694 2023-06-29 10:16:50.146706    2023-06-29         37650   \n", "3   petrol00004712 2023-07-01 16:39:26.715881    2023-07-01         37650   \n", "4   petrol00004734 2023-07-04 15:42:54.156781    2023-07-04         37650   \n", "5   petrol00004755 2023-07-07 09:28:16.959619    2023-07-07         37650   \n", "6   petrol00004788 2023-07-10 12:05:17.814391    2023-07-10         37650   \n", "7   petrol00004808 2023-07-13 14:13:14.168053    2023-07-13         37650   \n", "8   petrol00005108 2023-08-20 15:58:46.083962    2023-08-20         37650   \n", "9   petrol00005146 2023-08-24 16:52:01.930658    2023-08-24         37650   \n", "10  petrol00005190 2023-08-31 10:09:42.869021    2023-08-31         37650   \n", "11  petrol00005237 2023-09-06 10:49:55.365990    2023-09-06         37650   \n", "12  petrol00005286 2023-09-12 12:02:03.977819    2023-09-12         37650   \n", "13  petrol00005331 2023-09-19 08:40:13.654229    2023-09-19         37650   \n", "14  petrol00005378 2023-09-26 13:27:09.350794    2023-09-26         37650   \n", "15  petrol00005417 2023-09-30 16:10:19.787719    2023-09-30         37650   \n", "16  petrol00005447 2023-10-05 15:39:16.382202    2023-10-05         37650   \n", "17  petrol00005493 2023-10-11 13:39:31.793235    2023-10-11         37650   \n", "18  petrol00005533 2023-10-16 13:38:28.790825    2023-10-16         37650   \n", "19  petrol00005567 2023-10-21 11:22:33.942122    2023-10-21         37650   \n", "20  petrol00005600 2023-10-25 15:08:47.772799    2023-10-25         37650   \n", "21  petrol00005632 2023-10-30 12:12:53.210274    2023-10-30         37650   \n", "22  petrol00005661 2023-11-05 09:02:50.988901    2023-11-05         37650   \n", "23  petrol00005704 2023-11-11 15:48:04.422161    2023-11-11         37650   \n", "24  petrol00005744 2023-11-16 16:19:38.438093    2023-11-16         37650   \n", "25  petrol00005779 2023-11-21 16:25:25.641610    2023-11-21         37650   \n", "26  petrol00005804 2023-11-25 08:13:44.708101    2023-11-25         37650   \n", "27  petrol00005823 2023-11-28 09:18:33.660674    2023-11-28         37650   \n", "28  petrol00005845 2023-12-01 08:46:19.574344    2023-12-01         37650   \n", "29  petrol00005871 2023-12-03 15:57:58.630397    2023-12-03         37650   \n", "30  petrol00005893 2023-12-07 14:22:12.877893    2023-12-07         37650   \n", "31  petrol00005916 2023-12-10 14:03:21.498166    2023-12-10         37650   \n", "32  petrol00005936 2023-12-13 15:16:11.406496    2023-12-13         37650   \n", "33  petrol00005956 2023-12-16 14:54:16.538475    2023-12-16         37650   \n", "34  petrol00005980 2023-12-19 14:15:19.885071    2023-12-19         37650   \n", "\n", "    distant   qty  driver  \n", "0    3325.1  30.0   26978  \n", "1    3344.4  30.0   26978  \n", "2    3362.6  30.0   26978  \n", "3    3381.4  30.0   26978  \n", "4    3400.2  30.0   26978  \n", "5    3416.9  30.0   26978  \n", "6    3433.1  30.0   26978  \n", "7    3435.9  30.0   26978  \n", "8       0.0  30.0   39582  \n", "9       8.0  30.0   39582  \n", "10     24.0  30.0   39582  \n", "11     39.0  30.0   39582  \n", "12     55.0  30.0   39582  \n", "13     72.0  30.0   39582  \n", "14     88.0  30.0   39582  \n", "15    105.0  30.0   39582  \n", "16    120.0  30.0   39582  \n", "17    136.0  30.0   39582  \n", "18    153.0  30.0   39582  \n", "19    170.0  30.0   39582  \n", "20    186.0  30.0   39582  \n", "21    203.0  30.0   39582  \n", "22    220.0  30.0   39582  \n", "23    237.0  30.0   39582  \n", "24    254.0  30.0   39582  \n", "25    274.6  30.0   42588  \n", "26    297.2  30.0   42588  \n", "27    318.2  30.0   42588  \n", "28    339.6  30.0   42588  \n", "29    360.3  30.0   42588  \n", "30    382.0  30.0   42588  \n", "31    401.2  30.0   42588  \n", "32    420.8  30.0   42588  \n", "33    441.1  30.0   42588  \n", "34    462.2  30.0   42588  "]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["import warnings\n", "warnings.simplefilter('ignore')\n", "\n", "import pandas as pd\n", "import sqlalchemy\n", "from sqlalchemy import create_engine, tuple_\n", "from sqlalchemy.pool import NullPool\n", "import numpy as np\n", "import json\n", "from sqlalchemy.ext.declarative import declarative_base\n", "from sqlalchemy import Column, Integer, String, Date, DateTime, ForeignKey, TEXT\n", "from sqlalchemy.sql.expression import table, text\n", "from sqlalchemy.dialects.postgresql.json import JSONB\n", "from sqlalchemy.orm import sessionmaker\n", "from sqlalchemy import and_, or_, not_\n", "from sqlalchemy import extract, cast\n", "from sqlalchemy import literal\n", "from datetime import date, datetime, timedelta\n", "import datetime\n", "import itertools\n", "from sqlalchemy.orm.attributes import flag_modified\n", "from functools import reduce\n", "from sqlalchemy import func  # Add this import statement\n", "from sqlalchemy.types import Integer\n", "from sklearn.ensemble import IsolationForest\n", "\n", "\n", "DATABASE_URL = \"**************************************************/n_data\"\n", "engine = create_engine(DATABASE_URL, poolclass=NullPool)\n", "# engine = create_engine(DATABASE_URL)\n", "Session = sessionmaker(bind=engine)\n", "\n", "Base = declarative_base()\n", "\n", "class PetrolModel(Base):\n", "    __tablename__ = 'petrol'\n", "    auto_id = Column(Integer, primary_key=True)\n", "    petrol_id = Column(TEXT(convert_unicode=True), server_default=text(\"'petrol'||lpad(NEXTVAL('petrol_sequence'::regclass)::text,8, '0')\"))\n", "    datetime = Column(DateTime)\n", "    data_details = Column(JSONB, nullable=False, server_default='[]')\n", "    status_details = Column(JSONB, nullable=False, server_default='{}')\n", "    update_time = Column(DateTime)\n", "    datetime_bill = Column(DateTime)\n", "    \n", "session = Session()\n", "\n", "\n", "\n", "import datetime as DT\n", "today = DT.date.today()\n", "week_ago = today - DT.<PERSON><PERSON><PERSON>(days=180)\n", "print(week_ago)\n", "\n", "che_liang_id = 37650\n", "\n", "df20 = pd.read_sql(sql = session.query(PetrolModel)\\\n", "                            .with_entities(\n", "                                PetrolModel.petrol_id,\n", "                                PetrolModel.datetime,\n", "                                PetrolModel.datetime_bill,\n", "                                PetrolModel.data_details[0]['che_liang_id'],\n", "                                PetrolModel.data_details[0]['distant'],\n", "                                PetrolModel.data_details[0]['qty'],\n", "                                PetrolModel.data_details[0]['driver'],\n", "                                )\n", "                                .filter(\n", "                                    PetrolModel.datetime > week_ago,\n", "                                    func.cast(\n", "                                        PetrolModel.data_details[0]['che_liang_id'].astext,\n", "                                        Integer\n", "                                    ) == che_liang_id\n", "                                ).statement,\n", "                    con = session.bind)\n", "df20 = df20.rename(columns={\n", "    \"anon_1\": 'che_liang_id',\n", "    \"anon_2\": 'distant',\n", "    \"anon_3\": 'qty',\n", "    \"anon_4\": 'driver',\n", "    })\n", "\n", "# display(\"df20\", df20)\n", "\n", "df50 = df20\n", "\n", "# df50['previous_distant'] = df50['distant'].shift().fillna(0)\n", "# df50['distance_used'] = df50.apply(lambda x: x['previous_distant'] if x['previous_distant'] == 0 else (x['distant'] if x['distant'] == 0 else x['distant'] - x['previous_distant']), axis=1)\n", "# # df50['mean_distance_used'] = df50['distance_used'].mean()\n", "# df50['previous_qty'] = df50['qty'].shift()\n", "# df50['km_lit'] = (df50['distance_used'] / df50['previous_qty'])         \n", "# # df50['average'] = df50[df50['km_lit'] != 0][\"km_lit\"].mean()\n", "# # df50['average_percent'] = df50.apply(lambda x: x['km_lit'] if x['km_lit'] == 0 else x.mean_distance_used and ((x.distance_used - x.mean_distance_used) / x.mean_distance_used) * 100, axis=1)\n", "\n", "# outlier_detector = IsolationForest(random_state=42)\n", "# distance_used_fillna = df50[['km_lit']].fillna(0)\n", "# outlier_detector.fit(distance_used_fillna)\n", "# prediction = outlier_detector.predict(distance_used_fillna)\n", "# prediction_strings = ['Outlier' if x < 0 else 'Standard' for x in prediction]\n", "# df50['outlier_flag'] = prediction_strings\n", "\n", "# df50['km_lit_2'] = df50.apply(lambda x: None if x['outlier_flag'] == 'Outlier' else x['km_lit'], axis=1)\n", "# df50['average_2'] = df50[df50['km_lit_2'] != 0][\"km_lit_2\"].mean()\n", "# df50['mean_distance_used_2'] = (df50['average_2'] * df50['previous_qty']) + 0.01\n", "# df50['average_percent_2'] = df50.apply(lambda x: (x.distance_used - x.mean_distance_used_2) / x.mean_distance_used_2 * 100, axis=1)\n", "# # df50['mean'] = ((df50.average_2 - df50.average) / df50.average) * 100\n", "# # df50['mean'] = df50['average_percent_2'].abs().sum() / len(df50)\n", "\n", "df50"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2023-06-24\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>petrol_id</th>\n", "      <th>datetime</th>\n", "      <th>datetime_bill</th>\n", "      <th>che_liang_id</th>\n", "      <th>distant</th>\n", "      <th>qty</th>\n", "      <th>driver</th>\n", "      <th>previous_distant</th>\n", "      <th>distance_used</th>\n", "      <th>mean_distance_used</th>\n", "      <th>average</th>\n", "      <th>average_percent</th>\n", "      <th>mean_qty_dri</th>\n", "      <th>km_lit_dri</th>\n", "      <th>volatility_dri</th>\n", "      <th>mean_distance_used_dri</th>\n", "      <th>period_dri</th>\n", "      <th>day_pass_dri</th>\n", "      <th>count_dri</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>petrol00004687</td>\n", "      <td>2023-06-28</td>\n", "      <td>2023-06-28</td>\n", "      <td>38201</td>\n", "      <td>13630.0</td>\n", "      <td>30.0</td>\n", "      <td>38541</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>170.73</td>\n", "      <td>5.06</td>\n", "      <td>-100.00</td>\n", "      <td>34.74</td>\n", "      <td>5.03</td>\n", "      <td>18.60</td>\n", "      <td>166.32</td>\n", "      <td>2023-06-28 - 2023-11-18</td>\n", "      <td>143</td>\n", "      <td>19</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>petrol00004718</td>\n", "      <td>2023-07-02</td>\n", "      <td>2023-07-02</td>\n", "      <td>38201</td>\n", "      <td>13768.0</td>\n", "      <td>30.0</td>\n", "      <td>38541</td>\n", "      <td>13630.0</td>\n", "      <td>138.0</td>\n", "      <td>170.73</td>\n", "      <td>5.06</td>\n", "      <td>-19.17</td>\n", "      <td>34.74</td>\n", "      <td>5.03</td>\n", "      <td>18.60</td>\n", "      <td>166.32</td>\n", "      <td>2023-06-28 - 2023-11-18</td>\n", "      <td>143</td>\n", "      <td>19</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>petrol00004747</td>\n", "      <td>2023-07-06</td>\n", "      <td>2023-07-06</td>\n", "      <td>38201</td>\n", "      <td>13913.0</td>\n", "      <td>30.0</td>\n", "      <td>38541</td>\n", "      <td>13768.0</td>\n", "      <td>145.0</td>\n", "      <td>170.73</td>\n", "      <td>5.06</td>\n", "      <td>-15.07</td>\n", "      <td>34.74</td>\n", "      <td>5.03</td>\n", "      <td>18.60</td>\n", "      <td>166.32</td>\n", "      <td>2023-06-28 - 2023-11-18</td>\n", "      <td>143</td>\n", "      <td>19</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>petrol00004796</td>\n", "      <td>2023-07-11</td>\n", "      <td>2023-07-10</td>\n", "      <td>38201</td>\n", "      <td>14055.0</td>\n", "      <td>30.0</td>\n", "      <td>38541</td>\n", "      <td>13913.0</td>\n", "      <td>142.0</td>\n", "      <td>170.73</td>\n", "      <td>5.06</td>\n", "      <td>-16.83</td>\n", "      <td>34.74</td>\n", "      <td>5.03</td>\n", "      <td>18.60</td>\n", "      <td>166.32</td>\n", "      <td>2023-06-28 - 2023-11-18</td>\n", "      <td>143</td>\n", "      <td>19</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>petrol00004820</td>\n", "      <td>2023-07-14</td>\n", "      <td>2023-07-14</td>\n", "      <td>38201</td>\n", "      <td>14199.0</td>\n", "      <td>30.0</td>\n", "      <td>38541</td>\n", "      <td>14055.0</td>\n", "      <td>144.0</td>\n", "      <td>170.73</td>\n", "      <td>5.06</td>\n", "      <td>-15.66</td>\n", "      <td>34.74</td>\n", "      <td>5.03</td>\n", "      <td>18.60</td>\n", "      <td>166.32</td>\n", "      <td>2023-06-28 - 2023-11-18</td>\n", "      <td>143</td>\n", "      <td>19</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>petrol00004861</td>\n", "      <td>2023-07-19</td>\n", "      <td>2023-07-19</td>\n", "      <td>38201</td>\n", "      <td>14387.0</td>\n", "      <td>30.0</td>\n", "      <td>38541</td>\n", "      <td>14199.0</td>\n", "      <td>188.0</td>\n", "      <td>170.73</td>\n", "      <td>5.06</td>\n", "      <td>10.11</td>\n", "      <td>34.74</td>\n", "      <td>5.03</td>\n", "      <td>18.60</td>\n", "      <td>166.32</td>\n", "      <td>2023-06-28 - 2023-11-18</td>\n", "      <td>143</td>\n", "      <td>19</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>petrol00004900</td>\n", "      <td>2023-07-23</td>\n", "      <td>2023-07-23</td>\n", "      <td>38201</td>\n", "      <td>14531.0</td>\n", "      <td>30.0</td>\n", "      <td>18519</td>\n", "      <td>14387.0</td>\n", "      <td>144.0</td>\n", "      <td>170.73</td>\n", "      <td>5.06</td>\n", "      <td>-15.66</td>\n", "      <td>37.14</td>\n", "      <td>5.15</td>\n", "      <td>26.42</td>\n", "      <td>182.71</td>\n", "      <td>2023-07-23 - 2023-12-03</td>\n", "      <td>133</td>\n", "      <td>7</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>petrol00004957</td>\n", "      <td>2023-07-30</td>\n", "      <td>2023-07-29</td>\n", "      <td>38201</td>\n", "      <td>14652.0</td>\n", "      <td>30.0</td>\n", "      <td>18519</td>\n", "      <td>14531.0</td>\n", "      <td>121.0</td>\n", "      <td>170.73</td>\n", "      <td>5.06</td>\n", "      <td>-29.13</td>\n", "      <td>37.14</td>\n", "      <td>5.15</td>\n", "      <td>26.42</td>\n", "      <td>182.71</td>\n", "      <td>2023-07-23 - 2023-12-03</td>\n", "      <td>133</td>\n", "      <td>7</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>petrol00004988</td>\n", "      <td>2023-08-04</td>\n", "      <td>2023-08-03</td>\n", "      <td>38201</td>\n", "      <td>14805.0</td>\n", "      <td>30.0</td>\n", "      <td>38541</td>\n", "      <td>14652.0</td>\n", "      <td>153.0</td>\n", "      <td>170.73</td>\n", "      <td>5.06</td>\n", "      <td>-10.39</td>\n", "      <td>34.74</td>\n", "      <td>5.03</td>\n", "      <td>18.60</td>\n", "      <td>166.32</td>\n", "      <td>2023-06-28 - 2023-11-18</td>\n", "      <td>143</td>\n", "      <td>19</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>petrol00005033</td>\n", "      <td>2023-08-10</td>\n", "      <td>2023-08-10</td>\n", "      <td>38201</td>\n", "      <td>14980.0</td>\n", "      <td>30.0</td>\n", "      <td>38541</td>\n", "      <td>14805.0</td>\n", "      <td>175.0</td>\n", "      <td>170.73</td>\n", "      <td>5.06</td>\n", "      <td>2.50</td>\n", "      <td>34.74</td>\n", "      <td>5.03</td>\n", "      <td>18.60</td>\n", "      <td>166.32</td>\n", "      <td>2023-06-28 - 2023-11-18</td>\n", "      <td>143</td>\n", "      <td>19</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>petrol00005055</td>\n", "      <td>2023-08-13</td>\n", "      <td>2023-08-12</td>\n", "      <td>38201</td>\n", "      <td>15144.0</td>\n", "      <td>30.0</td>\n", "      <td>38541</td>\n", "      <td>14980.0</td>\n", "      <td>164.0</td>\n", "      <td>170.73</td>\n", "      <td>5.06</td>\n", "      <td>-3.94</td>\n", "      <td>34.74</td>\n", "      <td>5.03</td>\n", "      <td>18.60</td>\n", "      <td>166.32</td>\n", "      <td>2023-06-28 - 2023-11-18</td>\n", "      <td>143</td>\n", "      <td>19</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>petrol00005082</td>\n", "      <td>2023-08-16</td>\n", "      <td>2023-08-15</td>\n", "      <td>38201</td>\n", "      <td>15282.0</td>\n", "      <td>30.0</td>\n", "      <td>38541</td>\n", "      <td>15144.0</td>\n", "      <td>138.0</td>\n", "      <td>170.73</td>\n", "      <td>5.06</td>\n", "      <td>-19.17</td>\n", "      <td>34.74</td>\n", "      <td>5.03</td>\n", "      <td>18.60</td>\n", "      <td>166.32</td>\n", "      <td>2023-06-28 - 2023-11-18</td>\n", "      <td>143</td>\n", "      <td>19</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>petrol00005199</td>\n", "      <td>2023-08-31</td>\n", "      <td>2023-08-30</td>\n", "      <td>38201</td>\n", "      <td>15434.0</td>\n", "      <td>30.0</td>\n", "      <td>18519</td>\n", "      <td>15282.0</td>\n", "      <td>152.0</td>\n", "      <td>170.73</td>\n", "      <td>5.06</td>\n", "      <td>-10.97</td>\n", "      <td>37.14</td>\n", "      <td>5.15</td>\n", "      <td>26.42</td>\n", "      <td>182.71</td>\n", "      <td>2023-07-23 - 2023-12-03</td>\n", "      <td>133</td>\n", "      <td>7</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>petrol00005268</td>\n", "      <td>2023-09-09</td>\n", "      <td>2023-09-06</td>\n", "      <td>38201</td>\n", "      <td>15584.0</td>\n", "      <td>40.0</td>\n", "      <td>18519</td>\n", "      <td>15434.0</td>\n", "      <td>150.0</td>\n", "      <td>170.73</td>\n", "      <td>5.06</td>\n", "      <td>-12.14</td>\n", "      <td>37.14</td>\n", "      <td>5.15</td>\n", "      <td>26.42</td>\n", "      <td>182.71</td>\n", "      <td>2023-07-23 - 2023-12-03</td>\n", "      <td>133</td>\n", "      <td>7</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>petrol00005291</td>\n", "      <td>2023-09-13</td>\n", "      <td>2023-09-12</td>\n", "      <td>38201</td>\n", "      <td>15794.0</td>\n", "      <td>40.0</td>\n", "      <td>38541</td>\n", "      <td>15584.0</td>\n", "      <td>210.0</td>\n", "      <td>170.73</td>\n", "      <td>5.06</td>\n", "      <td>23.00</td>\n", "      <td>34.74</td>\n", "      <td>5.03</td>\n", "      <td>18.60</td>\n", "      <td>166.32</td>\n", "      <td>2023-06-28 - 2023-11-18</td>\n", "      <td>143</td>\n", "      <td>19</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>petrol00005323</td>\n", "      <td>2023-09-17</td>\n", "      <td>2023-09-16</td>\n", "      <td>38201</td>\n", "      <td>15983.0</td>\n", "      <td>40.0</td>\n", "      <td>38541</td>\n", "      <td>15794.0</td>\n", "      <td>189.0</td>\n", "      <td>170.73</td>\n", "      <td>5.06</td>\n", "      <td>10.70</td>\n", "      <td>34.74</td>\n", "      <td>5.03</td>\n", "      <td>18.60</td>\n", "      <td>166.32</td>\n", "      <td>2023-06-28 - 2023-11-18</td>\n", "      <td>143</td>\n", "      <td>19</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>petrol00005356</td>\n", "      <td>2023-09-22</td>\n", "      <td>2023-09-21</td>\n", "      <td>38201</td>\n", "      <td>16205.0</td>\n", "      <td>40.0</td>\n", "      <td>18519</td>\n", "      <td>15983.0</td>\n", "      <td>222.0</td>\n", "      <td>170.73</td>\n", "      <td>5.06</td>\n", "      <td>30.03</td>\n", "      <td>37.14</td>\n", "      <td>5.15</td>\n", "      <td>26.42</td>\n", "      <td>182.71</td>\n", "      <td>2023-07-23 - 2023-12-03</td>\n", "      <td>133</td>\n", "      <td>7</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>petrol00005398</td>\n", "      <td>2023-09-28</td>\n", "      <td>2023-09-27</td>\n", "      <td>38201</td>\n", "      <td>16374.0</td>\n", "      <td>40.0</td>\n", "      <td>38541</td>\n", "      <td>16205.0</td>\n", "      <td>169.0</td>\n", "      <td>170.73</td>\n", "      <td>5.06</td>\n", "      <td>-1.01</td>\n", "      <td>34.74</td>\n", "      <td>5.03</td>\n", "      <td>18.60</td>\n", "      <td>166.32</td>\n", "      <td>2023-06-28 - 2023-11-18</td>\n", "      <td>143</td>\n", "      <td>19</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>petrol00005429</td>\n", "      <td>2023-10-03</td>\n", "      <td>2023-10-03</td>\n", "      <td>38201</td>\n", "      <td>16574.0</td>\n", "      <td>40.0</td>\n", "      <td>38541</td>\n", "      <td>16374.0</td>\n", "      <td>200.0</td>\n", "      <td>170.73</td>\n", "      <td>5.06</td>\n", "      <td>17.14</td>\n", "      <td>34.74</td>\n", "      <td>5.03</td>\n", "      <td>18.60</td>\n", "      <td>166.32</td>\n", "      <td>2023-06-28 - 2023-11-18</td>\n", "      <td>143</td>\n", "      <td>19</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>petrol00005491</td>\n", "      <td>2023-10-11</td>\n", "      <td>2023-10-10</td>\n", "      <td>38201</td>\n", "      <td>16769.0</td>\n", "      <td>40.0</td>\n", "      <td>38541</td>\n", "      <td>16574.0</td>\n", "      <td>195.0</td>\n", "      <td>170.73</td>\n", "      <td>5.06</td>\n", "      <td>14.21</td>\n", "      <td>34.74</td>\n", "      <td>5.03</td>\n", "      <td>18.60</td>\n", "      <td>166.32</td>\n", "      <td>2023-06-28 - 2023-11-18</td>\n", "      <td>143</td>\n", "      <td>19</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>petrol00005529</td>\n", "      <td>2023-10-16</td>\n", "      <td>2023-10-15</td>\n", "      <td>38201</td>\n", "      <td>16970.0</td>\n", "      <td>40.0</td>\n", "      <td>38541</td>\n", "      <td>16769.0</td>\n", "      <td>201.0</td>\n", "      <td>170.73</td>\n", "      <td>5.06</td>\n", "      <td>17.73</td>\n", "      <td>34.74</td>\n", "      <td>5.03</td>\n", "      <td>18.60</td>\n", "      <td>166.32</td>\n", "      <td>2023-06-28 - 2023-11-18</td>\n", "      <td>143</td>\n", "      <td>19</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>petrol00005574</td>\n", "      <td>2023-10-22</td>\n", "      <td>2023-10-22</td>\n", "      <td>38201</td>\n", "      <td>17142.0</td>\n", "      <td>40.0</td>\n", "      <td>38541</td>\n", "      <td>16970.0</td>\n", "      <td>172.0</td>\n", "      <td>170.73</td>\n", "      <td>5.06</td>\n", "      <td>0.74</td>\n", "      <td>34.74</td>\n", "      <td>5.03</td>\n", "      <td>18.60</td>\n", "      <td>166.32</td>\n", "      <td>2023-06-28 - 2023-11-18</td>\n", "      <td>143</td>\n", "      <td>19</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>petrol00005664</td>\n", "      <td>2023-11-05</td>\n", "      <td>2023-11-04</td>\n", "      <td>38201</td>\n", "      <td>17365.0</td>\n", "      <td>40.0</td>\n", "      <td>38541</td>\n", "      <td>17142.0</td>\n", "      <td>223.0</td>\n", "      <td>170.73</td>\n", "      <td>5.06</td>\n", "      <td>30.62</td>\n", "      <td>34.74</td>\n", "      <td>5.03</td>\n", "      <td>18.60</td>\n", "      <td>166.32</td>\n", "      <td>2023-06-28 - 2023-11-18</td>\n", "      <td>143</td>\n", "      <td>19</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>petrol00005758</td>\n", "      <td>2023-11-18</td>\n", "      <td>2023-11-10</td>\n", "      <td>38201</td>\n", "      <td>17579.0</td>\n", "      <td>40.0</td>\n", "      <td>38541</td>\n", "      <td>17365.0</td>\n", "      <td>214.0</td>\n", "      <td>170.73</td>\n", "      <td>5.06</td>\n", "      <td>25.34</td>\n", "      <td>34.74</td>\n", "      <td>5.03</td>\n", "      <td>18.60</td>\n", "      <td>166.32</td>\n", "      <td>2023-06-28 - 2023-11-18</td>\n", "      <td>143</td>\n", "      <td>19</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>petrol00005761</td>\n", "      <td>2023-11-18</td>\n", "      <td>2023-11-17</td>\n", "      <td>38201</td>\n", "      <td>17828.0</td>\n", "      <td>45.0</td>\n", "      <td>18519</td>\n", "      <td>17579.0</td>\n", "      <td>249.0</td>\n", "      <td>170.73</td>\n", "      <td>5.06</td>\n", "      <td>45.84</td>\n", "      <td>37.14</td>\n", "      <td>5.15</td>\n", "      <td>26.42</td>\n", "      <td>182.71</td>\n", "      <td>2023-07-23 - 2023-12-03</td>\n", "      <td>133</td>\n", "      <td>7</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>petrol00005867</td>\n", "      <td>2023-12-03</td>\n", "      <td>2023-12-02</td>\n", "      <td>38201</td>\n", "      <td>18069.0</td>\n", "      <td>45.0</td>\n", "      <td>18519</td>\n", "      <td>17828.0</td>\n", "      <td>241.0</td>\n", "      <td>170.73</td>\n", "      <td>5.06</td>\n", "      <td>41.16</td>\n", "      <td>37.14</td>\n", "      <td>5.15</td>\n", "      <td>26.42</td>\n", "      <td>182.71</td>\n", "      <td>2023-07-23 - 2023-12-03</td>\n", "      <td>133</td>\n", "      <td>7</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         petrol_id    datetime datetime_bill  che_liang_id  distant   qty  \\\n", "0   petrol00004687  2023-06-28    2023-06-28         38201  13630.0  30.0   \n", "1   petrol00004718  2023-07-02    2023-07-02         38201  13768.0  30.0   \n", "2   petrol00004747  2023-07-06    2023-07-06         38201  13913.0  30.0   \n", "3   petrol00004796  2023-07-11    2023-07-10         38201  14055.0  30.0   \n", "4   petrol00004820  2023-07-14    2023-07-14         38201  14199.0  30.0   \n", "5   petrol00004861  2023-07-19    2023-07-19         38201  14387.0  30.0   \n", "6   petrol00004900  2023-07-23    2023-07-23         38201  14531.0  30.0   \n", "7   petrol00004957  2023-07-30    2023-07-29         38201  14652.0  30.0   \n", "8   petrol00004988  2023-08-04    2023-08-03         38201  14805.0  30.0   \n", "9   petrol00005033  2023-08-10    2023-08-10         38201  14980.0  30.0   \n", "10  petrol00005055  2023-08-13    2023-08-12         38201  15144.0  30.0   \n", "11  petrol00005082  2023-08-16    2023-08-15         38201  15282.0  30.0   \n", "12  petrol00005199  2023-08-31    2023-08-30         38201  15434.0  30.0   \n", "13  petrol00005268  2023-09-09    2023-09-06         38201  15584.0  40.0   \n", "14  petrol00005291  2023-09-13    2023-09-12         38201  15794.0  40.0   \n", "15  petrol00005323  2023-09-17    2023-09-16         38201  15983.0  40.0   \n", "16  petrol00005356  2023-09-22    2023-09-21         38201  16205.0  40.0   \n", "17  petrol00005398  2023-09-28    2023-09-27         38201  16374.0  40.0   \n", "18  petrol00005429  2023-10-03    2023-10-03         38201  16574.0  40.0   \n", "19  petrol00005491  2023-10-11    2023-10-10         38201  16769.0  40.0   \n", "20  petrol00005529  2023-10-16    2023-10-15         38201  16970.0  40.0   \n", "21  petrol00005574  2023-10-22    2023-10-22         38201  17142.0  40.0   \n", "22  petrol00005664  2023-11-05    2023-11-04         38201  17365.0  40.0   \n", "24  petrol00005758  2023-11-18    2023-11-10         38201  17579.0  40.0   \n", "23  petrol00005761  2023-11-18    2023-11-17         38201  17828.0  45.0   \n", "25  petrol00005867  2023-12-03    2023-12-02         38201  18069.0  45.0   \n", "\n", "    driver  previous_distant  distance_used  mean_distance_used  average  \\\n", "0    38541               0.0            0.0              170.73     5.06   \n", "1    38541           13630.0          138.0              170.73     5.06   \n", "2    38541           13768.0          145.0              170.73     5.06   \n", "3    38541           13913.0          142.0              170.73     5.06   \n", "4    38541           14055.0          144.0              170.73     5.06   \n", "5    38541           14199.0          188.0              170.73     5.06   \n", "6    18519           14387.0          144.0              170.73     5.06   \n", "7    18519           14531.0          121.0              170.73     5.06   \n", "8    38541           14652.0          153.0              170.73     5.06   \n", "9    38541           14805.0          175.0              170.73     5.06   \n", "10   38541           14980.0          164.0              170.73     5.06   \n", "11   38541           15144.0          138.0              170.73     5.06   \n", "12   18519           15282.0          152.0              170.73     5.06   \n", "13   18519           15434.0          150.0              170.73     5.06   \n", "14   38541           15584.0          210.0              170.73     5.06   \n", "15   38541           15794.0          189.0              170.73     5.06   \n", "16   18519           15983.0          222.0              170.73     5.06   \n", "17   38541           16205.0          169.0              170.73     5.06   \n", "18   38541           16374.0          200.0              170.73     5.06   \n", "19   38541           16574.0          195.0              170.73     5.06   \n", "20   38541           16769.0          201.0              170.73     5.06   \n", "21   38541           16970.0          172.0              170.73     5.06   \n", "22   38541           17142.0          223.0              170.73     5.06   \n", "24   38541           17365.0          214.0              170.73     5.06   \n", "23   18519           17579.0          249.0              170.73     5.06   \n", "25   18519           17828.0          241.0              170.73     5.06   \n", "\n", "    average_percent  mean_qty_dri  km_lit_dri  volatility_dri  \\\n", "0           -100.00         34.74        5.03           18.60   \n", "1            -19.17         34.74        5.03           18.60   \n", "2            -15.07         34.74        5.03           18.60   \n", "3            -16.83         34.74        5.03           18.60   \n", "4            -15.66         34.74        5.03           18.60   \n", "5             10.11         34.74        5.03           18.60   \n", "6            -15.66         37.14        5.15           26.42   \n", "7            -29.13         37.14        5.15           26.42   \n", "8            -10.39         34.74        5.03           18.60   \n", "9              2.50         34.74        5.03           18.60   \n", "10            -3.94         34.74        5.03           18.60   \n", "11           -19.17         34.74        5.03           18.60   \n", "12           -10.97         37.14        5.15           26.42   \n", "13           -12.14         37.14        5.15           26.42   \n", "14            23.00         34.74        5.03           18.60   \n", "15            10.70         34.74        5.03           18.60   \n", "16            30.03         37.14        5.15           26.42   \n", "17            -1.01         34.74        5.03           18.60   \n", "18            17.14         34.74        5.03           18.60   \n", "19            14.21         34.74        5.03           18.60   \n", "20            17.73         34.74        5.03           18.60   \n", "21             0.74         34.74        5.03           18.60   \n", "22            30.62         34.74        5.03           18.60   \n", "24            25.34         34.74        5.03           18.60   \n", "23            45.84         37.14        5.15           26.42   \n", "25            41.16         37.14        5.15           26.42   \n", "\n", "    mean_distance_used_dri               period_dri  day_pass_dri  count_dri  \n", "0                   166.32  2023-06-28 - 2023-11-18           143         19  \n", "1                   166.32  2023-06-28 - 2023-11-18           143         19  \n", "2                   166.32  2023-06-28 - 2023-11-18           143         19  \n", "3                   166.32  2023-06-28 - 2023-11-18           143         19  \n", "4                   166.32  2023-06-28 - 2023-11-18           143         19  \n", "5                   166.32  2023-06-28 - 2023-11-18           143         19  \n", "6                   182.71  2023-07-23 - 2023-12-03           133          7  \n", "7                   182.71  2023-07-23 - 2023-12-03           133          7  \n", "8                   166.32  2023-06-28 - 2023-11-18           143         19  \n", "9                   166.32  2023-06-28 - 2023-11-18           143         19  \n", "10                  166.32  2023-06-28 - 2023-11-18           143         19  \n", "11                  166.32  2023-06-28 - 2023-11-18           143         19  \n", "12                  182.71  2023-07-23 - 2023-12-03           133          7  \n", "13                  182.71  2023-07-23 - 2023-12-03           133          7  \n", "14                  166.32  2023-06-28 - 2023-11-18           143         19  \n", "15                  166.32  2023-06-28 - 2023-11-18           143         19  \n", "16                  182.71  2023-07-23 - 2023-12-03           133          7  \n", "17                  166.32  2023-06-28 - 2023-11-18           143         19  \n", "18                  166.32  2023-06-28 - 2023-11-18           143         19  \n", "19                  166.32  2023-06-28 - 2023-11-18           143         19  \n", "20                  166.32  2023-06-28 - 2023-11-18           143         19  \n", "21                  166.32  2023-06-28 - 2023-11-18           143         19  \n", "22                  166.32  2023-06-28 - 2023-11-18           143         19  \n", "24                  166.32  2023-06-28 - 2023-11-18           143         19  \n", "23                  182.71  2023-07-23 - 2023-12-03           133          7  \n", "25                  182.71  2023-07-23 - 2023-12-03           133          7  "]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["import warnings\n", "warnings.simplefilter('ignore')\n", "\n", "import pandas as pd\n", "import sqlalchemy\n", "from sqlalchemy import create_engine, tuple_\n", "from sqlalchemy.pool import NullPool\n", "import numpy as np\n", "import json\n", "from sqlalchemy.ext.declarative import declarative_base\n", "from sqlalchemy import Column, Integer, String, Date, DateTime, ForeignKey, TEXT\n", "from sqlalchemy.sql.expression import table, text\n", "from sqlalchemy.dialects.postgresql.json import JSONB\n", "from sqlalchemy.orm import sessionmaker\n", "from sqlalchemy import and_, or_, not_\n", "from sqlalchemy import extract, cast\n", "from sqlalchemy import literal\n", "from datetime import date, datetime, timedelta\n", "import datetime\n", "import itertools\n", "from sqlalchemy.orm.attributes import flag_modified\n", "from functools import reduce\n", "from sqlalchemy import func  # Add this import statement\n", "from sqlalchemy.types import Integer\n", "from sklearn.ensemble import IsolationForest\n", "\n", "\n", "DATABASE_URL = \"**************************************************/n_data\"\n", "engine = create_engine(DATABASE_URL, poolclass=NullPool)\n", "Session = sessionmaker(bind=engine)\n", "\n", "Base = declarative_base()\n", "\n", "class PetrolModel(Base):\n", "    __tablename__ = 'petrol'\n", "    auto_id = Column(Integer, primary_key=True)\n", "    petrol_id = Column(TEXT(convert_unicode=True), server_default=text(\"'petrol'||lpad(NEXTVAL('petrol_sequence'::regclass)::text,8, '0')\"))\n", "    datetime = Column(DateTime)\n", "    data_details = Column(JSONB, nullable=False, server_default='[]')\n", "    status_details = Column(JSONB, nullable=False, server_default='{}')\n", "    update_time = Column(DateTime)\n", "    datetime_bill = Column(DateTime)\n", "    \n", "session = Session()\n", "\n", "\n", "\n", "import datetime as DT\n", "today = DT.date.today()\n", "week_ago = today - DT.<PERSON><PERSON><PERSON>(days=180)\n", "print(week_ago)\n", "\n", "che_liang_id = 38201\n", "\n", "df20 = pd.read_sql(sql = session.query(PetrolModel)\\\n", "                            .with_entities(\n", "                                PetrolModel.petrol_id,\n", "                                PetrolModel.datetime,\n", "                                PetrolModel.datetime_bill,\n", "                                PetrolModel.data_details[0]['che_liang_id'],\n", "                                PetrolModel.data_details[0]['distant'],\n", "                                PetrolModel.data_details[0]['qty'],\n", "                                PetrolModel.data_details[0]['driver'],\n", "                                )\n", "                                .filter(\n", "                                    PetrolModel.datetime > week_ago,\n", "                                    func.cast(\n", "                                        PetrolModel.data_details[0]['che_liang_id'].astext,\n", "                                        Integer\n", "                                    ) == che_liang_id\n", "                                ).statement,\n", "                    con = session.bind)\n", "df20 = df20.rename(columns={\n", "    \"anon_1\": 'che_liang_id',\n", "    \"anon_2\": 'distant',\n", "    \"anon_3\": 'qty',\n", "    \"anon_4\": 'driver',\n", "    })\n", "\n", "\n", "df50 = df20\n", "\n", "# df50['previous_distant'] = df50['distant'].shift().fillna(0)\n", "# df50['distance_used'] = df50.apply(lambda x: x['previous_distant'] if x['previous_distant'] == 0 else (x['distant'] if x['distant'] == 0 else x['distant'] - x['previous_distant']), axis=1)\n", "# df50['previous_qty'] = df50['qty'].shift()\n", "# df50['km_lit'] = (df50['distance_used'] / df50['previous_qty'])\n", "\n", "# outlier_detector = IsolationForest(random_state=42)\n", "# distance_used_fillna = df50[['km_lit']].fillna(0)\n", "# outlier_detector.fit(distance_used_fillna)\n", "# prediction = outlier_detector.predict(distance_used_fillna)\n", "# prediction_strings = ['Outlier' if x < 0 else 'Standard' for x in prediction]\n", "# df50['outlier_flag'] = prediction_strings\n", "\n", "df50 = df50.sort_values(['datetime'], ascending=[True])\n", "\n", "df50['datetime'] = pd.to_datetime(df50['datetime'])\n", "df50['datetime'] = df50['datetime'].dt.date\n", "\n", "df50['previous_distant'] = df50['distant'].shift().fillna(0)\n", "df50['distance_used'] = df50.apply(lambda x: x['previous_distant'] if x['previous_distant'] == 0 else (x['distant'] if x['distant'] == 0 else x['distant'] - x['previous_distant']), axis=1)\n", "df50['mean_distance_used'] = df50['distance_used'].mean()\n", "df50['previous_qty'] = df50['qty'].shift()\n", "df50['km_lit'] = (df50['distance_used'] / df50['previous_qty'])\n", "df50['average'] = df50[df50['km_lit'] != 0][\"km_lit\"].mean()\n", "df50['average_percent'] = df50.apply(lambda x: x['km_lit'] if x['km_lit'] == 0 else x.mean_distance_used and ((x.distance_used - x.mean_distance_used) / x.mean_distance_used) * 100, axis=1)\n", "\n", "df50['mean_qty_dri'] = df50.groupby('driver')['qty'].transform('mean')\n", "df50['km_lit_dri'] = df50.groupby('driver')['km_lit'].transform('mean')\n", "df50['volatility_dri'] = df50.groupby('driver')['average_percent'].transform(lambda x: x.abs().sum() / len(x))\n", "df50['mean_distance_used_dri'] = df50.groupby('driver')['distance_used'].transform('mean')\n", "df50['period_dri'] = df50.groupby('driver')['datetime'].transform(lambda x: f'{x.min().strftime(\"%Y-%m-%d\")} - {x.max().strftime(\"%Y-%m-%d\")}')\n", "df50['day_pass_dri'] = (df50.groupby('driver')['datetime']\n", "                  .transform(lambda x: (x.max() - x.min()).days))\n", "df50['count_dri'] = df50.groupby('driver')['driver'].transform('count')\n", "\n", "\n", "# columns_to_exclude = ['distant', 'qty', 'previous_distant', 'distance_used', 'mean_distance_used', 'previous_qty', 'km_lit']\n", "columns_to_exclude = ['previous_qty', 'km_lit']\n", "df50 = df50.drop(columns=columns_to_exclude)\n", "\n", "\n", "\n", "# df50 = df50.loc[df50.groupby('driver')['datetime'].idxmax()]\n", "df50 = df50.round(2)\n", "\n", "\n", "df50"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 2}