{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from fpdf import FPDF\n", "\n", "pdf = FPDF()\n", "pdf.add_page()\n", "pdf.set_font('helvetica', size=24)\n", "pdf.cell(txt=\"hello world sdcdsc \")\n", "pdf.output(\"hello_world.jpg\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from reportlab.pdfgen import canvas\n", "my_canvas = canvas.Canvas(\"hello.jpg\")\n", "my_canvas.drawString(100, 750, \" ထမင်းငတ်တယ်။ หิวข้าว Welcome to Reportlab! \")\n", "my_canvas.save()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from reportlab.pdfgen import canvas\n", "from reportlab.lib.units import inch, cm\n", "c = canvas.<PERSON>vas('ex.pdf')\n", "c.drawImage('ar.jpg', 0, 0, 10*cm, 10*cm)\n", "c.showPage()\n", "c.save()"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"data": {"text/plain": ["8101"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["import weasyprint \n", "pdf = weasyprint.HTML('./sample.html').write_pdf()\n", "open('google.pdf', 'wb').write(pdf)"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [], "source": ["from weasyprint import HTML\n", "HTML('./sample.html').write_pdf(\"google.pdf\")\n", "# open('google.pdf', 'wb').write(pdf)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>name</th>\n", "      <th>child_id</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2</td>\n", "      <td>color</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>13</td>\n", "      <td>red</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>14</td>\n", "      <td>blue</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>15</td>\n", "      <td>ruby red</td>\n", "      <td>13</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>16</td>\n", "      <td>mobile</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>17</td>\n", "      <td>iphone</td>\n", "      <td>16</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>18</td>\n", "      <td>sumsung</td>\n", "      <td>16</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   id      name child_id\n", "0   2     color        0\n", "1  13       red        2\n", "2  14      blue        2\n", "3  15  ruby red       13\n", "4  16    mobile        0\n", "5  17    iphone       16\n", "6  18   sumsung       16"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "\n", "data = {\n", "    'id': ['2', '13', '14', '15', '16', '17', '18'],\n", "    'name': ['color', 'red', 'blue', 'ruby red', 'mobile', 'iphone', 'sumsung'],\n", "    'child_id': ['0', '2', '2', '13', '0', '16', '16']\n", "}\n", "  \n", "\n", "df = pd.DataFrame(data)\n", "df"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'name': 'color',\n", "  'id': '2',\n", "  'child_id': '0',\n", "  'children': [{'name': 'red',\n", "    'id': '13',\n", "    'child_id': '2',\n", "    'children': [{'name': 'ruby red', 'id': '15', 'child_id': '13'}]},\n", "   {'name': 'blue', 'id': '14', 'child_id': '2'}]},\n", " {'name': 'mobile',\n", "  'id': '16',\n", "  'child_id': '0',\n", "  'children': [{'name': 'iphone', 'id': '17', 'child_id': '16'},\n", "   {'name': 'sumsung', 'id': '18', 'child_id': '16'}]}]"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["def makehiearchy(data):\n", "    result = []\n", "    d = { \"0\": { \"children\": result } }\n", "    for id, name, child_id in zip(data[\"id\"], data[\"name\"], data[\"child_id\"]):\n", "        d[id] = { \"name\": name, \"id\": id, \"child_id\": child_id }\n", "    \n", "    for id, child_id in zip(data[\"id\"], data[\"child_id\"]):\n", "        parent = d[child_id]\n", "        if \"children\" not in parent:\n", "            parent[\"children\"] = []\n", "        parent[\"children\"].append(d[id])\n", "\n", "    return result\n", "\n", "# Example run\n", "data = {\n", "    'id': ['2', '13', '14', '15', '16', '17', '18'],\n", "    'name': ['color', 'red', 'blue', 'ruby red', 'mobile', 'iphone', 'sumsung'],\n", "    'child_id': ['0', '2', '2', '13', '0', '16', '16']\n", "}\n", "hierarchy = makehiearchy(data)\n", "hierarchy"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3.8.10 64-bit", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}, "orig_nbformat": 4, "vscode": {"interpreter": {"hash": "916dbcbb3f70747c44a77c7bcd40155683ae19c65e1c03b4aa3499c5328201f1"}}}, "nbformat": 4, "nbformat_minor": 2}