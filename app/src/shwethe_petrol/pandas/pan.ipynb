{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import sqlalchemy\n", "from sqlalchemy import create_engine, tuple_\n", "import requests\n", "import numpy as np\n", "import json\n", "\n", "\n", "DATABASE_URL = \"*************************************************/n_data\"\n", "engine = create_engine(DATABASE_URL , poolclass=NullPool)\n", "\n", "\n", "\n", "df = pd.read_sql_table('petrol', engine)\n", "# df = df.head(3)\n", "df = df.to_dict('records')\n", "df = pd.json_normalize(df, \"data_details\", [\"petrol_id\", \"datetime\", \"auto_id\"])\n", "df[\"product_price_b\"] = 0\n", "df['bu_bian'] = 0\n", "df['jin_huo_bian'] = 0\n", "df = df.rename(columns=dict(auto_id=\"a_id\", sub_id=\"b_id\", qty=\"product_qty\", price=\"product_price_b\", petrol_id=\"jin_huo_dang\"))\n", "display(df)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import sqlalchemy\n", "from sqlalchemy import create_engine, tuple_\n", "from sqlalchemy.pool import NullPool\n", "import requests\n", "import numpy as np\n", "import json\n", "\n", "\n", "DATABASE_URL = \"*************************************************/n_data\"\n", "engine = create_engine(DATABASE_URL, poolclass=NullPool)\n", "\n", "# df = df.loc[df['che_liang_id'] == 36555]\n", "# df = df.groupby('che_liang_id').filter(lambda g: len(g) > 1)\n", "# df = df.drop_duplicates(subset=['qty'], keep=\"first\")\n", "\n", "df = pd.read_sql_table('petrol', engine)\n", "# df = df.head(3)\n", "df = df.to_dict('records')\n", "df = pd.json_normalize(df, \"data_details\", [\"petrol_id\", \"datetime\", \"auto_id\"])\n", "df = df.groupby('che_liang_id').filter(lambda g: len(g) > 1)\n", "display(df)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import sqlalchemy\n", "from sqlalchemy import create_engine, tuple_\n", "import requests\n", "import numpy as np\n", "import json\n", "\n", "\n", "DATABASE_URL = \"*************************************************/n_data\"\n", "engine = create_engine(DATABASE_URL, poolclass=NullPool)\n", "\n", "\n", "df = pd.read_sql_table('petrol', engine)\n", "df = df.to_dict('records')\n", "df = pd.json_normalize(df, \"data_details\", [\"petrol_id\", \"datetime\", \"auto_id\"])\n", "df = df.loc[df['che_liang_id'] == 104]\n", "# df = df.iloc[-4:]\n", "# df = df.iloc[-0:-2]\n", "display(df)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import sqlalchemy\n", "from sqlalchemy import create_engine, tuple_\n", "from sqlalchemy.pool import NullPool\n", "import numpy as np\n", "import json\n", "from sqlalchemy.ext.declarative import declarative_base\n", "from sqlalchemy import Column, Integer, String, Date, DateTime, ForeignKey, TEXT\n", "from sqlalchemy.sql.expression import table, text\n", "from sqlalchemy.dialects.postgresql.json import JSONB\n", "from sqlalchemy.orm import sessionmaker\n", "from sqlalchemy import literal\n", "import matplotlib.pyplot as plt\n", "\n", "\n", "DATABASE_URL = \"*************************************************/test_nern\"\n", "engine = create_engine(DATABASE_URL, poolclass=NullPool)\n", "Session = sessionmaker(bind=engine)\n", "\n", "Base = declarative_base()\n", "\n", "class PetrolModel(Base):\n", "    __tablename__ = 'petrol'\n", "    auto_id = Column(Integer, primary_key=True)\n", "    petrol_id = Column(TEXT(convert_unicode=True), server_default=text(\"'petrol'||lpad(NEXTVAL('petrol_sequence'::regclass)::text,8, '0')\"))\n", "    datetime = Column(DateTime)\n", "    data_details = Column(JSONB, nullable=False, server_default='[]')\n", "\n", "\n", "session = Session()\n", "df = pd.read_sql(sql = session.query(PetrolModel)\\\n", "                         .with_entities(\n", "                             PetrolModel.auto_id,\n", "                             PetrolModel.datetime,\n", "                             PetrolModel.data_details[0]['che_liang_id'],\n", "                             PetrolModel.data_details[0]['distant'],\n", "                             PetrolModel.data_details[0]['qty'],\n", "                             ).statement, \n", "                 con = session.bind)\n", "\n", "df = df.rename(columns={\n", "    \"anon_1\": 'che_liang_id',\n", "    \"anon_2\": 'distant',\n", "    \"anon_3\": 'qty',\n", "    })\n", "\n", "\n", "\n", "# mask = (df['datetime'] > '2021-12-30')\n", "mask = df[\"datetime\"].between('2021-10-01', '2022-02-05')\n", "df =df.loc[mask]\n", "# df = df[df['datetime'].dt.year == 2020]\n", "display(df)\n", "\n", "\n", "\n", "df2 = df[['che_liang_id']]\n", "df2 = df2.drop_duplicates(subset='che_liang_id', keep=\"first\")\n", "display(df2)\n", "\n", "\n", "\n", "df3 = df[df[\"che_liang_id\"] == 194]\n", "\n", "# km = df3['distant'].max() - df3['distant'].min()\n", "# lit = df3['qty'].sum()\n", "# km_lit = km / lit\n", "\n", "# df3['distant_2'] = df3['distant'].diff()\n", "# df3['km_lit'] = df3['distant_2'].shift(0) / df3['qty'].shift(1)\n", "\n", "df3['datetime'] = df3['datetime'].dt.strftime(\"%Y-%m-%d\")\n", "\n", "df3['previous_distant'] = df3['distant'].shift()\n", "def fx(x):\n", "    if x['previous_distant'] == 0:\n", "        return x['previous_distant']\n", "    elif x['distant'] == 0:\n", "        return x['distant']\n", "    else:\n", "        return x['distant'] - x['previous_distant']\n", "df3['distance_used'] = df3.apply(lambda x : fx(x),axis=1)\n", "df3['previous_qty'] = df3['qty'].shift()\n", "df3['km_lit'] = df3['distance_used'] / df3['previous_qty']\n", "df3['average'] = df3[df3['km_lit'] != 0][\"km_lit\"].mean()\n", "def fx2(x):\n", "    if x['km_lit'] == 0:\n", "        return x['km_lit']\n", "    else:\n", "        return (x.average - x.km_lit) / x.average * 100\n", "df3['average_percent'] = df3.apply(lambda x : fx2(x),axis=1).fillna(0)\n", "# df['new_val'] = df['new_val'].fillna(0)\n", "df3['sum'] = df3['average_percent'].sum()\n", "\n", "# df3['previous_distant'] = df3['distant'].shift()\n", "# df3['distance_used'] = df3['distant'] - df3['previous_distant']\n", "# df3['previous_qty'] = df3['qty'].shift()\n", "# df3['km_lit'] = df3['distance_used'] / df3['previous_qty']\n", "# df3['average'] = df3['km_lit'].mean()\n", "# df3['average_percent'] = (df3.average - df3.km_lit) / df3.average * 100\n", "display(df3)\n", "\n", "\n", "# df4 = pd.DataFrame(df3,columns=['datetime','km_lit'])\n", "# df5 = pd.DataFrame(df3,columns=['datetime','average'])\n", "# plt.plot('datetime','km_lit',data=df4,marker='o',color='blue',linewidth=2)\n", "# plt.plot('datetime','average',data=df5,color='red',linewidth=2)\n", "# plt.xticks(rotation=90)\n", "\n", "\n", "# df4 = df3.to_html()\n", "# display(df4)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import sqlalchemy\n", "from sqlalchemy import create_engine, tuple_\n", "from sqlalchemy.pool import NullPool\n", "import numpy as np\n", "import json\n", "from sqlalchemy.ext.declarative import declarative_base\n", "from sqlalchemy import Column, Integer, String, Date, DateTime, ForeignKey, TEXT\n", "from sqlalchemy.sql.expression import table, text\n", "from sqlalchemy.dialects.postgresql.json import JSONB\n", "from sqlalchemy.orm import sessionmaker\n", "from sqlalchemy import literal\n", "import matplotlib.pyplot as plt\n", "# import seaborn as sns\n", "# from sklearn.ensemble import IsolationForest\n", "# from sklearn.linear_model import LinearRegression\n", "\n", "DATABASE_URL = \"*************************************************/test_nern\"\n", "engine = create_engine(DATABASE_URL, poolclass=NullPool)\n", "Session = sessionmaker(bind=engine)\n", "\n", "Base = declarative_base()\n", "\n", "class PetrolModel(Base):\n", "    __tablename__ = 'petrol'\n", "    auto_id = Column(Integer, primary_key=True)\n", "    petrol_id = Column(TEXT(convert_unicode=True), server_default=text(\"'petrol'||lpad(NEXTVAL('petrol_sequence'::regclass)::text,8, '0')\"))\n", "    datetime = Column(DateTime)\n", "    data_details = Column(JSONB, nullable=False, server_default='[]')\n", "\n", "\n", "session = Session()\n", "df = pd.read_sql(sql = session.query(PetrolModel)\\\n", "                         .with_entities(\n", "                             PetrolModel.auto_id,\n", "                             PetrolModel.datetime,\n", "                             PetrolModel.data_details[0]['che_liang_id'],\n", "                             PetrolModel.data_details[0]['distant'],\n", "                             PetrolModel.data_details[0]['qty'],\n", "                             ).statement, \n", "                 con = session.bind)\n", "\n", "df = df.rename(columns={\n", "    \"anon_1\": 'che_liang_id',\n", "    \"anon_2\": 'distant',\n", "    \"anon_3\": 'qty',\n", "    })\n", "\n", "\n", "\n", "mask = df[\"datetime\"].between('2021-08-01', '2022-02-05')\n", "# year = datetime.now().year\n", "# mask = (df['datetime'] > str(year))\n", "df =df.loc[mask]\n", "display(df)\n", "\n", "\n", "\n", "df2 = df[['che_liang_id']]\n", "df2 = df2.drop_duplicates(subset='che_liang_id', keep=\"first\")\n", "display(df2)\n", "\n", "\n", "df3 = df[df[\"che_liang_id\"] == 22046]\n", "df3['datetime'] = df3['datetime'].dt.strftime(\"%Y-%m-%d\")\n", "df3['previous_distant'] = df3['distant'].shift()\n", "def fx(x):\n", "    if x['previous_distant'] == 0:\n", "        return x['previous_distant']\n", "    elif x['distant'] == 0:\n", "        return x['distant']\n", "    else:\n", "        return x['distant'] - x['previous_distant']\n", "df3['distance_used'] = df3.apply(lambda x : fx(x),axis=1)\n", "df3['previous_qty'] = df3['qty'].shift()\n", "df3['km_lit'] = df3['distance_used'] / df3['previous_qty']               \n", "df3['average'] = df3[df3['km_lit'] != 0][\"km_lit\"].mean()\n", "def fx2(x):\n", "    if x['km_lit'] == 0:\n", "        return x['km_lit']\n", "    else:\n", "        return (x.average - x.km_lit) / x.average * 100\n", "df3['average_percent'] = df3.apply(lambda x : fx2(x),axis=1).fillna(0)\n", "\n", "def remove_outliers(df3, q=0.05):\n", "    upper = df3.quantile(1-q)\n", "    lower = df3.quantile(q)\n", "    mask = (df3 < upper) & (df3 > lower)\n", "    return mask\n", "df3['outliers'] = remove_outliers(df3['km_lit'], 0.1)\n", "\n", "def fx3(x):\n", "    if x['outliers'] == False:\n", "        return None\n", "    else:\n", "        return x['distance_used'] / x['previous_qty']\n", "df3['km_lit_2'] = df3.apply(lambda x : fx3(x),axis=1)\n", "df3['average_2'] = df3[df3['km_lit_2'] != 0][\"km_lit_2\"].mean()\n", "def fx4(x):\n", "    if x['km_lit_2'] == None:\n", "        return x['km_lit_2']\n", "    else:\n", "        return (x.average_2 - x.km_lit_2) / x.average_2 * 100\n", "df3['average_percent_2'] = df3.apply(lambda x : fx4(x),axis=1).fillna(0)\n", "df3['mean'] = (df3.average - df3.average_2) / df3.average * 100\n", "display(df3)\n", "\n", "\n", "# df4 = pd.DataFrame(df3,columns=['datetime','km_lit'])\n", "# df5 = pd.DataFrame(df3,columns=['datetime','average'])\n", "# plt.plot('datetime','km_lit',data=df4,marker='o',color='blue',linewidth=2)\n", "# plt.plot('datetime','average',data=df5,color='red',linewidth=2)\n", "# plt.xticks(rotation=90)\n", "\n", "\n", "# df4 = pd.DataFrame(df3,columns=['datetime','km_lit_2'])\n", "# df5 = pd.DataFrame(df3,columns=['datetime','average_2'])\n", "# plt.plot('datetime','km_lit_2',data=df4,marker='o',color='blue',linewidth=2)\n", "# plt.plot('datetime','average_2',data=df5,color='red',linewidth=2)\n", "# plt.xticks(rotation=90)\n", "\n", "# distance_used\n", "# previous_distant\n", "\n", "# data = pd.DataFrame(df3)\n", "# data = data.dropna()\n", "# X = data[\"km_lit\"].values.reshape(-1, 1)\n", "# Y = data[\"previous_distant\"].values.reshape(-1, 1)\n", "# linear_regressor = LinearRegression() \n", "# linear_regressor.fit(X, Y)\n", "# Y_pred = linear_regressor.predict(X)  \n", "# plt.scatter(X, Y)\n", "# plt.plot(X, Y_pred, color='red')\n", "# plt.show()\n", "\n", "# print(X)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt  # To visualize\n", "import pandas as pd  # To read data\n", "from sklearn.linear_model import LinearRegression\n", "\n", "# 1, 2, 3, 4, 5, 6, 7, 8, 9, 10\n", "# 5, 8, 3, 1, 5, 6, 12, 8, 16, 16\n", "# data = pd.read_csv('data.csv')  # load data set\n", "dataset = {'mark1':[5, 8, 3, 1, 5, 6, 12, 8, 16, 16],\n", "        'mark2':[1, 2, 3, 4, 5, 6, 7, 8, 9, 10]}\n", "data = pd.DataFrame(dataset)\n", "X = data.iloc[:, 0].values.reshape(-1, 1)  # values converts it into a numpy array\n", "Y = data.iloc[:, 1].values.reshape(-1, 1)  # -1 means that calculate the dimension of rows, but have 1 column\n", "linear_regressor = LinearRegression()  # create object for the class\n", "linear_regressor.fit(X, Y)  # perform linear regression\n", "Y_pred = linear_regressor.predict(X)  # make predictions\n", "plt.scatter(X, Y)\n", "plt.plot(X, Y_pred, color='red')\n", "plt.show()\n", "\n", "print(X)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import sqlalchemy\n", "from sqlalchemy import create_engine, tuple_\n", "from sqlalchemy.pool import NullPool\n", "import numpy as np\n", "import json\n", "from sqlalchemy.ext.declarative import declarative_base\n", "from sqlalchemy import Column, Integer, String, Date, DateTime, ForeignKey, TEXT\n", "from sqlalchemy.sql.expression import table, text\n", "from sqlalchemy.dialects.postgresql.json import JSONB\n", "from sqlalchemy.orm import sessionmaker\n", "from sqlalchemy import literal\n", "import datetime\n", "\n", "\n", "DATABASE_URL = \"*************************************************/test_nern\"\n", "engine = create_engine(DATABASE_URL, poolclass=NullPool)\n", "Session = sessionmaker(bind=engine)\n", "\n", "Base = declarative_base()\n", "\n", "class PetrolModel(Base):\n", "    __tablename__ = 'petrol'\n", "    auto_id = Column(Integer, primary_key=True)\n", "    petrol_id = Column(TEXT(convert_unicode=True), server_default=text(\"'petrol'||lpad(NEXTVAL('petrol_sequence'::regclass)::text,8, '0')\"))\n", "    datetime = Column(DateTime)\n", "    data_details = Column(JSONB, nullable=False, server_default='[]')\n", "\n", "\n", "session = Session()\n", "df = pd.read_sql(sql = session.query(PetrolModel)\\\n", "                         .with_entities(\n", "                             PetrolModel.auto_id,\n", "                             PetrolModel.datetime,\n", "                             PetrolModel.data_details[0]['che_liang_id'],\n", "                             PetrolModel.data_details[0]['distant'],\n", "                             PetrolModel.data_details[0]['qty'],\n", "                             ).statement, \n", "                 con = session.bind)\n", "\n", "df = df.rename(columns={\n", "    \"anon_1\": 'che_liang_id',\n", "    \"anon_2\": 'distant',\n", "    \"anon_3\": 'qty',\n", "    })\n", "\n", "\n", "\n", "\n", "today = datetime.date.today()\n", "future_day = today.day\n", "future_month = (today.month - 6) % 12\n", "future_year = today.year + ((today.month - 6) // 12)\n", "six_months_later = datetime.date(future_year, future_month, future_day)\n", "mask = (df['datetime'] > str(six_months_later))\n", "df =df.loc[mask]\n", "display(df, six_months_later)\n", "\n", "\n", "\n", "df2 = df[['che_liang_id']]\n", "df2 = df2.drop_duplicates(subset='che_liang_id', keep=\"first\")\n", "display(df2)\n", "\n", "\n", "df3 = df[df[\"che_liang_id\"] == 194]\n", "df3['datetime'] = df3['datetime'].dt.strftime(\"%Y-%m-%d\")\n", "df3['previous_distant'] = df3['distant'].shift()\n", "def fx(x):\n", "    if x['previous_distant'] == 0:\n", "        return x['previous_distant']\n", "    elif x['distant'] == 0:\n", "        return x['distant']\n", "    else:\n", "        return x['distant'] - x['previous_distant']\n", "df3['distance_used'] = df3.apply(lambda x : fx(x),axis=1)\n", "df3['previous_qty'] = df3['qty'].shift()\n", "df3['km_lit'] = df3['distance_used'] / df3['previous_qty']               \n", "df3['average'] = df3[df3['km_lit'] != 0][\"km_lit\"].mean()\n", "def fx2(x):\n", "    if x['km_lit'] == 0:\n", "        return x['km_lit']\n", "    else:\n", "        return (x.average - x.km_lit) / x.average * 100\n", "df3['average_percent'] = df3.apply(lambda x : fx2(x),axis=1).fillna(0)\n", "\n", "def remove_outliers(df3, q=0.05):\n", "    upper = df3.quantile(1-q)\n", "    lower = df3.quantile(q)\n", "    mask = (df3 < upper) & (df3 > lower)\n", "    return mask\n", "df3['outliers'] = remove_outliers(df3['km_lit'], 0.1)\n", "\n", "def fx3(x):\n", "    if x['outliers'] == False:\n", "        return None\n", "    else:\n", "        return x['distance_used'] / x['previous_qty']\n", "df3['km_lit_2'] = df3.apply(lambda x : fx3(x),axis=1)\n", "df3['average_2'] = df3[df3['km_lit_2'] != 0][\"km_lit_2\"].mean()\n", "def fx4(x):\n", "    if x['km_lit_2'] == None:\n", "        return x['km_lit_2']\n", "    else:\n", "        return (x.average_2 - x.km_lit_2) / x.average_2 * 100\n", "df3['average_percent_2'] = df3.apply(lambda x : fx4(x),axis=1).fillna(0)\n", "df3['mean'] = (df3.average - df3.average_2) / df3.average * 100\n", "display(df3)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import sqlalchemy\n", "from sqlalchemy import create_engine, tuple_\n", "from sqlalchemy.pool import NullPool\n", "import numpy as np\n", "import json\n", "from sqlalchemy.ext.declarative import declarative_base\n", "from sqlalchemy import Column, Integer, String, Date, DateTime, ForeignKey, TEXT\n", "from sqlalchemy.sql.expression import table, text\n", "from sqlalchemy.dialects.postgresql.json import JSONB\n", "from sqlalchemy.orm import sessionmaker\n", "from sqlalchemy import literal\n", "import datetime\n", "\n", "\n", "DATABASE_URL = \"*************************************************/test_nern\"\n", "engine = create_engine(DATABASE_URL, poolclass=NullPool)\n", "Session = sessionmaker(bind=engine)\n", "\n", "Base = declarative_base()\n", "\n", "class PetrolModel(Base):\n", "    __tablename__ = 'petrol'\n", "    auto_id = Column(Integer, primary_key=True)\n", "    petrol_id = Column(TEXT(convert_unicode=True), server_default=text(\"'petrol'||lpad(NEXTVAL('petrol_sequence'::regclass)::text,8, '0')\"))\n", "    datetime = Column(DateTime)\n", "    data_details = Column(JSONB, nullable=False, server_default='[]')\n", "\n", "\n", "session = Session()\n", "\n", "\n", "page_size = 10\n", "page = 1\n", "page -= 1\n", "df10 = pd.read_sql(sql = session.query(PetrolModel.auto_id,\n", "                                     PetrolModel.datetime,\n", "                                     PetrolModel.data_details[0]['che_liang_id'],\n", "                                     ).order_by(PetrolModel.datetime.desc()).limit(page_size).offset(page*page_size).statement, con = session.bind)\n", "\n", "df10 = df10.rename(columns={\n", "    \"anon_1\": 'che_liang_id',\n", "    \"anon_2\": 'distant',\n", "    \"anon_3\": 'qty',\n", "    })\n", "\n", "dataList = [] \n", "for row in df10.itertuples(): \n", "    mylist = [row.che_liang_id]\n", "    dataList.extend(mylist)\n", "# aaa = dataList = 194\n", "display(df10)\n", "\n", "\n", "df20 = pd.read_sql(sql = session.query(PetrolModel.auto_id,\n", "                                     PetrolModel.petrol_id,\n", "                                     PetrolModel.datetime,\n", "                                     PetrolModel.data_details[0]['che_liang_id'],\n", "                                     PetrolModel.data_details[0]['distant'],\n", "                                     PetrolModel.data_details[0]['qty']\n", "                                     ) \n", "                    .filter(PetrolModel.data_details[0]['che_liang_id'].astext.cast(Integer).in_(dataList)).statement, \n", "                    con = session.bind)\n", "\n", "df20 = df20.rename(columns={\n", "    \"anon_1\": 'che_liang_id',\n", "    \"anon_2\": 'distant',\n", "    \"anon_3\": 'qty',\n", "    })\n", "# display(df20)\n", "\n", "\n", "\n", "today = datetime.date.today()\n", "future_day = today.day\n", "future_month = (today.month - 6) % 12\n", "future_year = today.year + ((today.month - 6) // 12)\n", "six_months_later = datetime.date(future_year, future_month, future_day)\n", "mask = (df20['datetime'] > str(six_months_later))\n", "df20 = df20.loc[mask]\n", "display(df20)\n", "\n", "\n", "# df21 = df20[df20['che_liang_id'] == 194]\n", "# display(df21)\n", "\n", "\n", "# df21['datetime'] = df21['datetime'].dt.strftime(\"%Y-%m-%d\")\n", "# df21['previous_distant'] = df21['distant'].shift()\n", "# def fx(x):\n", "#     if x['previous_distant'] == 0:\n", "#         return x['previous_distant']\n", "#     elif x['distant'] == 0:\n", "#         return x['distant']\n", "#     else:\n", "#         return x['distant'] - x['previous_distant']\n", "# df21['distance_used'] = df21.apply(lambda x : fx(x),axis=1)\n", "# df21['previous_qty'] = df21['qty'].shift()\n", "# df21['km_lit'] = df21['distance_used'] / df21['previous_qty']               \n", "# df21['average'] = df21[df21['km_lit'] != 0][\"km_lit\"].mean()\n", "# def fx2(x):\n", "#     if x['km_lit'] == 0:\n", "#         return x['km_lit']\n", "#     else:\n", "#         return (x.average - x.km_lit) / x.average * 100\n", "# df21['average_percent'] = df21.apply(lambda x : fx2(x),axis=1).fillna(0)\n", "\n", "# def remove_outliers(df21, q=0.05):\n", "#     upper = df21.quantile(1-q)\n", "#     lower = df21.quantile(q)\n", "#     mask = (df21 < upper) & (df21 > lower)\n", "#     return mask\n", "# df21['outliers'] = remove_outliers(df21['km_lit'], 0.1)\n", "\n", "# def fx3(x):\n", "#     if x['outliers'] == False:\n", "#         return None\n", "#     else:\n", "#         return x['distance_used'] / x['previous_qty']\n", "# df21['km_lit_2'] = df21.apply(lambda x : fx3(x),axis=1)\n", "# df21['average_2'] = df21[df21['km_lit_2'] != 0][\"km_lit_2\"].mean()\n", "# def fx4(x):\n", "#     if x['km_lit_2'] == None:\n", "#         return x['km_lit_2']\n", "#     else:\n", "#         return (x.average_2 - x.km_lit_2) / x.average_2 * 100\n", "# df21['average_percent_2'] = df21.apply(lambda x : fx4(x),axis=1).fillna(0)\n", "# df21['mean'] = (df21.average - df21.average_2) / df21.average * 100\n", "# display(df21)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import sqlalchemy\n", "from sqlalchemy import create_engine, tuple_\n", "from sqlalchemy.pool import NullPool\n", "import numpy as np\n", "import json\n", "from sqlalchemy.ext.declarative import declarative_base\n", "from sqlalchemy import Column, Integer, String, Date, DateTime, ForeignKey, TEXT\n", "from sqlalchemy.sql.expression import table, text\n", "from sqlalchemy.dialects.postgresql.json import JSONB\n", "from sqlalchemy.orm import sessionmaker\n", "from sqlalchemy import literal\n", "import datetime\n", "\n", "\n", "DATABASE_URL = \"*************************************************/test_nern\"\n", "engine = create_engine(DATABASE_URL, poolclass=NullPool)\n", "Session = sessionmaker(bind=engine)\n", "\n", "Base = declarative_base()\n", "\n", "class PetrolModel(Base):\n", "    __tablename__ = 'petrol'\n", "    auto_id = Column(Integer, primary_key=True)\n", "    petrol_id = Column(TEXT(convert_unicode=True), server_default=text(\"'petrol'||lpad(NEXTVAL('petrol_sequence'::regclass)::text,8, '0')\"))\n", "    datetime = Column(DateTime)\n", "    data_details = Column(JSONB, nullable=False, server_default='[]')\n", "\n", "\n", "session = Session()\n", "\n", "\n", "page_size = 10\n", "page = 2\n", "page -= 1\n", "df10 = pd.read_sql(sql = session.query(PetrolModel.auto_id,\n", "                                     PetrolModel.datetime,\n", "                                     PetrolModel.data_details[0]['che_liang_id'],\n", "                                     ).order_by(PetrolModel.datetime.desc()).limit(page_size).offset(page*page_size).statement, con = session.bind)\n", "\n", "df10 = df10.rename(columns={\n", "    \"anon_1\": 'che_liang_id',\n", "    \"anon_2\": 'distant',\n", "    \"anon_3\": 'qty',\n", "    })\n", "\n", "dataList = [] \n", "for row in df10.itertuples(): \n", "    mylist = [row.che_liang_id]\n", "    dataList.extend(mylist)\n", "# aaa = dataList = 194\n", "display(df10)\n", "\n", "\n", "\n", "\n", "\n", "df20 = pd.read_sql(sql = session.query(PetrolModel)\\\n", "                        .with_entities(\n", "                            PetrolModel.auto_id,\n", "                            PetrolModel.petrol_id,\n", "                            PetrolModel.datetime,\n", "                            PetrolModel.data_details[0]['che_liang_id'],\n", "                            PetrolModel.data_details[0]['distant'],\n", "                            PetrolModel.data_details[0]['qty'],\n", "                            )\n", "                        # .filter(PetrolModel.data_details[0]['che_liang_id'].astext.cast(Integer) == 194).statement,\n", "                        # .filter(PetrolModel.data_details[0]['che_liang_id'].astext.cast(Integer).in_(dataList)).statement, \n", "                        # .filter(PetrolModel.data_details[0]['che_liang_id'].astext.cast(Integer).in_([f for f in dataList])).statement, \n", "                con = session.bind)\n", "\n", "\n", "df20 = df20.rename(columns={\n", "    \"anon_1\": 'che_liang_id',\n", "    \"anon_2\": 'distant',\n", "    \"anon_3\": 'qty',\n", "    })\n", "\n", "display(df20)\n", "\n", "\n", "\n", "# today = datetime.date.today()\n", "# future_day = today.day\n", "# future_month = (today.month - 6) % 12\n", "# future_year = today.year + ((today.month - 6) // 12)\n", "# six_months_later = datetime.date(future_year, future_month, future_day)\n", "# mask = (df20['datetime'] > str(six_months_later))\n", "# df20 = df20.loc[mask]\n", "# df20 = df20.head(10)\n", "# df20['zz'] = 'aa'\n", "\n", "# df20['datetime'] = df20['datetime'].dt.strftime(\"%Y-%m-%d\")\n", "# df20['previous_distant'] = df20['distant'].shift()\n", "# def fx(x):\n", "#     if x['previous_distant'] == 0:\n", "#         return x['previous_distant']\n", "#     elif x['distant'] == 0:\n", "#         return x['distant']\n", "#     else:\n", "#         return x['distant'] - x['previous_distant']\n", "# df20['distance_used'] = df20.apply(lambda x : fx(x),axis=1)\n", "# df20['previous_qty'] = df20['qty'].shift()\n", "# df20['km_lit'] = df20['distance_used'] / df20['previous_qty']               \n", "# df20['average'] = df20[df20['km_lit'] != 0][\"km_lit\"].mean()\n", "# def fx2(x):\n", "#     if x['km_lit'] == 0:\n", "#         return x['km_lit']\n", "#     else:\n", "#         return (x.average - x.km_lit) / x.average * 100\n", "# df20['average_percent'] = df20.apply(lambda x : fx2(x),axis=1).fillna(0)\n", "\n", "# def remove_outliers(df20, q=0.05):\n", "#     upper = df20.quantile(1-q)\n", "#     lower = df20.quantile(q)\n", "#     mask = (df20 < upper) & (df20 > lower)\n", "#     return mask\n", "# df20['outliers'] = remove_outliers(df20['km_lit'], 0.1)\n", "\n", "# def fx3(x):\n", "#     if x['outliers'] == False:\n", "#         return None\n", "#     else:\n", "#         return x['distance_used'] / x['previous_qty']\n", "# df20['km_lit_2'] = df20.apply(lambda x : fx3(x),axis=1)\n", "# df20['average_2'] = df20[df20['km_lit_2'] != 0][\"km_lit_2\"].mean()\n", "# def fx4(x):\n", "#     if x['km_lit_2'] == None:\n", "#         return x['km_lit_2']\n", "#     else:\n", "#         return (x.average_2 - x.km_lit_2) / x.average_2 * 100\n", "# df20['average_percent_2'] = df20.apply(lambda x : fx4(x),axis=1).fillna(0)\n", "# df20['mean'] = (df20.average - df20.average_2) / df20.average * 100\n", "# display(df20)\n", "\n", "\n", "\n", "# df30 = df10.merge(df20, on='auto_id', how='left')\n", "# display(df30)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import sqlalchemy\n", "from sqlalchemy import create_engine, tuple_\n", "from sqlalchemy.pool import NullPool\n", "import numpy as np\n", "import json\n", "from sqlalchemy.ext.declarative import declarative_base\n", "from sqlalchemy import Column, Integer, String, Date, DateTime, ForeignKey, TEXT\n", "from sqlalchemy.sql.expression import table, text\n", "from sqlalchemy.dialects.postgresql.json import JSONB\n", "from sqlalchemy.orm import sessionmaker\n", "from sqlalchemy import and_, or_, not_\n", "from sqlalchemy import literal\n", "import datetime\n", "import itertools\n", "\n", "DATABASE_URL = \"*************************************************/test_nern\"\n", "engine = create_engine(DATABASE_URL, poolclass=NullPool)\n", "Session = sessionmaker(bind=engine)\n", "\n", "Base = declarative_base()\n", "\n", "class PetrolModel(Base):\n", "    __tablename__ = 'petrol'\n", "    auto_id = Column(Integer, primary_key=True)\n", "    petrol_id = Column(TEXT(convert_unicode=True), server_default=text(\"'petrol'||lpad(NEXTVAL('petrol_sequence'::regclass)::text,8, '0')\"))\n", "    datetime = Column(DateTime)\n", "    data_details = Column(JSONB, nullable=False, server_default='[]')\n", "\n", "\n", "session = Session()\n", "\n", "\n", "page_size = 2\n", "page = 5\n", "page -= 1\n", "df10 = pd.read_sql(sql = session.query(PetrolModel.auto_id,\n", "                                     PetrolModel.datetime,\n", "                                     PetrolModel.data_details[0]['che_liang_id'],\n", "                                     ).order_by(PetrolModel.datetime.desc()).limit(page_size).offset(page*page_size).statement, con = session.bind)\n", "\n", "df10 = df10.rename(columns={\n", "    \"anon_1\": 'che_liang_id',\n", "    \"anon_2\": 'distant',\n", "    \"anon_3\": 'qty',\n", "    })\n", "\n", "dataList = [] \n", "for row in df10.itertuples(): \n", "    mylist = [row.che_liang_id]\n", "    dataList.extend(mylist)\n", "# aaa = dataList = 194\n", "display(df10)\n", "\n", "\n", "\n", "today = datetime.date.today()\n", "future_day = today.day\n", "future_month = (today.month - 6) % 12\n", "future_year = today.year + ((today.month - 6) // 12)\n", "six_months_ago = datetime.date(future_year, future_month, future_day)\n", "df20 = pd.read_sql(sql = session.query(PetrolModel)\\\n", "                        .with_entities(\n", "                            PetrolModel.auto_id,\n", "                            PetrolModel.petrol_id,\n", "                            PetrolModel.datetime,\n", "                            PetrolModel.data_details[0]['che_liang_id'],\n", "                            PetrolModel.data_details[0]['distant'],\n", "                            PetrolModel.data_details[0]['qty'],\n", "                            )\n", "                        # .filter(PetrolModel.data_details[0]['che_liang_id'].astext.cast(Integer) == 194).statement,\n", "                        # .filter(PetrolModel.data_details[0]['che_liang_id'].astext.cast(Integer).in_(dataList)).statement, \n", "                            .filter(\n", "                                and_(\n", "                                    PetrolModel.datetime > six_months_ago,\n", "                                    PetrolModel.data_details[0]['che_liang_id'].astext.cast(Integer).in_(dataList),\n", "                                    )\n", "                                ).statement,\n", "                con = session.bind)\n", "\n", "\n", "df20 = df20.rename(columns={\n", "    \"anon_1\": 'che_liang_id',\n", "    \"anon_2\": 'distant',\n", "    \"anon_3\": 'qty',\n", "    })\n", "\n", "# display(df20)\n", "\n", "ccc = []\n", "mylist = list(dict.fromkeys(dataList))\n", "for i in mylist:\n", "    df21 = df20[df20['che_liang_id'] == i]\n", "    # display(df21)\n", "    # df21['previous_distant'] = df21['distant'].shift()\n", "    # def fx(x):\n", "    #     if x['previous_distant'] == 0:\n", "    #         return x['previous_distant']\n", "    #     elif x['distant'] == 0:\n", "    #         return x['distant']\n", "    #     else:\n", "    #         return x['distant'] - x['previous_distant']\n", "    # df21['distance_used'] = df21.apply(lambda x : fx(x),axis=1)\n", "    # df21['previous_qty'] = df21['qty'].shift()\n", "    # df21['km_lit'] = df21['distance_used'] / df21['previous_qty']               \n", "    # df21['average'] = df21[df21['km_lit'] != 0][\"km_lit\"].mean()\n", "    # def fx2(x):\n", "    #     if x['km_lit'] == 0:\n", "    #         return x['km_lit']\n", "    #     else:\n", "    #         return (x.average - x.km_lit) / x.average * 100\n", "    # df21['average_percent'] = df21.apply(lambda x : fx2(x),axis=1).fillna(0)\n", "\n", "    # def remove_outliers(df21, q=0.05):\n", "    #     upper = df21.quantile(1-q)\n", "    #     lower = df21.quantile(q)\n", "    #     mask = (df21 < upper) & (df21 > lower)\n", "    #     return mask\n", "    # df21['outliers'] = remove_outliers(df21['km_lit'], 0.1)\n", "\n", "    # def fx3(x):\n", "    #     if x['outliers'] == False:\n", "    #         return None\n", "    #     else:\n", "    #         return x['distance_used'] / x['previous_qty']\n", "    # df21['km_lit_2'] = df21.apply(lambda x : fx3(x),axis=1)\n", "    # df21['average_2'] = df21[df21['km_lit_2'] != 0][\"km_lit_2\"].mean()\n", "    # def fx4(x):\n", "    #     if x['km_lit_2'] == None:\n", "    #         return x['km_lit_2']\n", "    #     else:\n", "    #         return (x.average_2 - x.km_lit_2) / x.average_2 * 100\n", "    # df21['average_percent_2'] = df21.apply(lambda x : fx4(x),axis=1).fillna(0)\n", "    # df21['mean'] = (df21.average - df21.average_2) / df21.average * 100\n", "    \n", "    # df30 = df10.merge(df21, on='auto_id', how='inner')\n", "    \n", "    \n", "    # df30 = df21.to_dict('records')\n", "    # ccc.extend(df30)\n", "    display(df21)\n", "# display(mylist)\n", "\n", "# df30 = df10.merge(df21, on='auto_id', how='inner')\n", "# display(df30)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import sqlalchemy\n", "from sqlalchemy import create_engine, tuple_\n", "from sqlalchemy.pool import NullPool\n", "import numpy as np\n", "import json\n", "from sqlalchemy.ext.declarative import declarative_base\n", "from sqlalchemy import Column, Integer, String, Date, DateTime, ForeignKey, TEXT\n", "from sqlalchemy.sql.expression import table, text\n", "from sqlalchemy.dialects.postgresql.json import JSONB\n", "from sqlalchemy.orm import sessionmaker\n", "from sqlalchemy import and_, or_, not_\n", "from sqlalchemy import literal\n", "import datetime\n", "import itertools\n", "\n", "DATABASE_URL = \"*************************************************/n_data\"\n", "engine = create_engine(DATABASE_URL, poolclass=NullPool)\n", "Session = sessionmaker(bind=engine)\n", "\n", "Base = declarative_base()\n", "\n", "class PetrolModel(Base):\n", "    __tablename__ = 'petrol'\n", "    auto_id = Column(Integer, primary_key=True)\n", "    petrol_id = Column(TEXT(convert_unicode=True), server_default=text(\"'petrol'||lpad(NEXTVAL('petrol_sequence'::regclass)::text,8, '0')\"))\n", "    datetime = Column(DateTime)\n", "    data_details = Column(JSONB, nullable=False, server_default='[]')\n", "    \n", "    \n", "class PetrolModel2(Base):\n", "    __tablename__ = 'petrol2'\n", "    auto_id = Column(Integer, primary_key=True)\n", "    petrol_id = Column(TEXT(convert_unicode=True), server_default=text(\"'petrol'||lpad(NEXTVAL('petrol_sequence'::regclass)::text,8, '0')\"))\n", "    datetime = Column(DateTime)\n", "    data_details = Column(JSONB, nullable=False, server_default='[]')\n", "    status_details = Column(JSONB, nullable=False, server_default='{}')    \n", "\n", "\n", "session = Session()\n", "\n", "\n", "page_size = 2\n", "page = 1\n", "page -= 1\n", "df10 = pd.read_sql(sql = session.query(PetrolModel.petrol_id,\n", "                                     PetrolModel.datetime,\n", "                                     PetrolModel.data_details[0]['che_liang_id'],\n", "                                     ).order_by(PetrolModel.datetime.desc()).limit(page_size).offset(page*page_size).statement, con = session.bind)\n", "\n", "df10 = df10.rename(columns={\n", "    \"anon_1\": 'che_liang_id',\n", "    \"anon_2\": 'distant',\n", "    \"anon_3\": 'qty',\n", "    })\n", "\n", "dataList = [] \n", "for row in df10.itertuples(): \n", "    mylist = [row.che_liang_id]\n", "    dataList.extend(mylist)\n", "# aaa = dataList = 194\n", "display(df10)\n", "\n", "\n", "\n", "today = datetime.date.today()\n", "future_day = today.day\n", "future_month = (today.month - 6) % 12\n", "future_year = today.year + ((today.month - 6) // 12)\n", "six_months_ago = datetime.date(future_year, future_month, future_day)\n", "\n", "\n", "df20 = pd.read_sql(sql = session.query(PetrolModel)\\\n", "                        .with_entities(\n", "                            PetrolModel.petrol_id,\n", "                            PetrolModel.datetime,\n", "                            PetrolModel.data_details[0]['che_liang_id'],\n", "                            PetrolModel.data_details[0]['distant'],\n", "                            PetrolModel.data_details[0]['qty'],\n", "                            )\n", "                        # .filter(PetrolModel.data_details[0]['che_liang_id'].astext.cast(Integer) == 194).statement,\n", "                        # .filter(PetrolModel.data_details[0]['che_liang_id'].astext.cast(Integer).in_(dataList)).statement, \n", "                            .filter(\n", "                                and_(\n", "                                    PetrolModel.datetime > six_months_ago,\n", "                                    PetrolModel.data_details[0]['che_liang_id'].astext.cast(Integer).in_(dataList),\n", "                                    )\n", "                                ).statement,\n", "                con = session.bind)\n", "\n", "\n", "df20 = df20.rename(columns={\n", "    \"anon_1\": 'che_liang_id',\n", "    \"anon_2\": 'distant',\n", "    \"anon_3\": 'qty',\n", "    })\n", "\n", "# display(df20)\n", "\n", "\n", "df30 = pd.read_sql(sql = session.query(PetrolModel2)\\\n", "                        .with_entities(\n", "                            # PetrolModel2.auto_id,\n", "                            PetrolModel2.petrol_id,\n", "                            PetrolModel2.datetime,\n", "                            PetrolModel2.data_details[0]['che_liang_id'],\n", "                            PetrolModel2.data_details[0]['distant'],\n", "                            PetrolModel2.data_details[0]['qty'],\n", "                            )\n", "                        # .filter(PetrolModel.data_details[0]['che_liang_id'].astext.cast(Integer) == 194).statement,\n", "                        # .filter(PetrolModel.data_details[0]['che_liang_id'].astext.cast(Integer).in_(dataList)).statement, \n", "                            .filter(\n", "                                and_(\n", "                                    PetrolModel2.datetime > six_months_ago,\n", "                                    PetrolModel2.data_details[0]['che_liang_id'].astext.cast(Integer).in_(dataList),\n", "                                    )\n", "                                ).statement,\n", "                con = session.bind)\n", "\n", "\n", "df30 = df30.rename(columns={\n", "    \"anon_1\": 'che_liang_id',\n", "    \"anon_2\": 'distant',\n", "    \"anon_3\": 'qty',\n", "    })\n", "\n", "# display(df30)\n", "\n", "\n", "df40 = pd.concat([df20, df30], ignore_index=True)\n", "# display(df40)\n", "\n", "\n", "ccc = []\n", "mylist = list(dict.fromkeys(dataList))\n", "for i in mylist:\n", "    df50 = df40[df40['che_liang_id'] == i ]\n", "    df50['previous_distant'] = df50['distant'].shift().fillna(0)\n", "    def fx(x):\n", "        if x['previous_distant'] == 0:\n", "            return x['previous_distant']\n", "        elif x['distant'] == 0:\n", "            return x['distant']\n", "        else:\n", "            return x['distant'] - x['previous_distant']\n", "    df50['distance_used'] = df50.apply(lambda x : fx(x),axis=1)\n", "    df50['previous_qty'] = df50['qty'].shift()\n", "    df50['km_lit'] = df50['distance_used'] / df50['previous_qty']               \n", "    df50['average'] = df50[df50['km_lit'] != 0][\"km_lit\"].mean()\n", "    def fx2(x):\n", "        if x['km_lit'] == 0:\n", "            return x['km_lit']\n", "        else:\n", "            return (x.average - x.km_lit) / x.average * 100\n", "    df50['average_percent'] = df50.apply(lambda x : fx2(x),axis=1).fillna(0)\n", "\n", "    def remove_outliers(df50, q=0.05):\n", "        upper = df50.quantile(1-q)\n", "        lower = df50.quantile(q)\n", "        mask = (df50 < upper) & (df50 > lower)\n", "        return mask\n", "    df50['outliers'] = remove_outliers(df50['km_lit'], 0.1)\n", "\n", "    def fx3(x):\n", "        if x['outliers'] == False:\n", "            return None\n", "        else:\n", "            return x['distance_used'] / x['previous_qty']\n", "    df50['km_lit_2'] = df50.apply(lambda x : fx3(x),axis=1)\n", "    df50['average_2'] = df50[df50['km_lit_2'] != 0][\"km_lit_2\"].mean()\n", "    def fx4(x):\n", "        if x['km_lit_2'] == None:\n", "            return x['km_lit_2']\n", "        else:\n", "            return (x.average_2 - x.km_lit_2) / x.average_2 * 100\n", "    df50['average_percent_2'] = df50.apply(lambda x : fx4(x),axis=1).fillna(0)\n", "    df50['mean'] = (df50.average - df50.average_2) / df50.average * 100\n", "    \n", "    # df60 = df10.merge(df50, on='petrol_id', how='inner')\n", "    # df60 =df60.to_dict(\"records\")\n", "    \n", "\n", "    \n", "    display(df50)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import sqlalchemy\n", "from sqlalchemy import create_engine, tuple_\n", "from sqlalchemy.pool import NullPool\n", "import numpy as np\n", "import json\n", "from sqlalchemy.ext.declarative import declarative_base\n", "from sqlalchemy import Column, Integer, String, Date, DateTime, ForeignKey, TEXT\n", "from sqlalchemy.sql.expression import table, text\n", "from sqlalchemy.dialects.postgresql.json import JSONB\n", "from sqlalchemy.orm import sessionmaker\n", "from sqlalchemy import and_, or_, not_\n", "from sqlalchemy import literal\n", "import datetime\n", "import itertools\n", "\n", "DATABASE_URL = \"*************************************************/n_data\"\n", "engine = create_engine(DATABASE_URL, poolclass=NullPool)\n", "Session = sessionmaker(bind=engine)\n", "\n", "Base = declarative_base()\n", "\n", "class PetrolModel(Base):\n", "    __tablename__ = 'petrol'\n", "    auto_id = Column(Integer, primary_key=True)\n", "    petrol_id = Column(TEXT(convert_unicode=True), server_default=text(\"'petrol'||lpad(NEXTVAL('petrol_sequence'::regclass)::text,8, '0')\"))\n", "    datetime = Column(DateTime)\n", "    data_details = Column(JSONB, nullable=False, server_default='[]')\n", "    \n", "    \n", "class PetrolModel2(Base):\n", "    __tablename__ = 'petrol2'\n", "    auto_id = Column(Integer, primary_key=True)\n", "    petrol_id = Column(TEXT(convert_unicode=True), server_default=text(\"'petrol'||lpad(NEXTVAL('petrol_sequence'::regclass)::text,8, '0')\"))\n", "    datetime = Column(DateTime)\n", "    data_details = Column(JSONB, nullable=False, server_default='[]')\n", "    status_details = Column(JSONB, nullable=False, server_default='{}')    \n", "\n", "\n", "session = Session()\n", "\n", "\n", "page_size = 2\n", "page = 2\n", "page -= 1\n", "df10 = pd.read_sql(sql = session.query(PetrolModel.petrol_id,\n", "                                     PetrolModel.datetime,\n", "                                     PetrolModel.data_details[0]['che_liang_id'],\n", "                                     ).order_by(PetrolModel.datetime.desc()).limit(page_size).offset(page*page_size).statement, con = session.bind)\n", "\n", "df10 = df10.rename(columns={\n", "    \"anon_1\": 'che_liang_id',\n", "    \"anon_2\": 'distant',\n", "    \"anon_3\": 'qty',\n", "    })\n", "\n", "dataList = [] \n", "for row in df10.itertuples(): \n", "    mylist = [row.che_liang_id]\n", "    dataList.extend(mylist)\n", "# aaa = dataList = 194\n", "display(\"df10\", df10)\n", "\n", "\n", "\n", "today = datetime.date.today()\n", "future_day = today.day\n", "future_month = (today.month - 2) % 12\n", "future_year = today.year + ((today.month - 2) // 12)\n", "six_months_ago = datetime.date(future_year, future_month, future_day)\n", "\n", "\n", "df20 = pd.read_sql(sql = session.query(PetrolModel)\\\n", "                        .with_entities(\n", "                            PetrolModel.petrol_id,\n", "                            PetrolModel.datetime,\n", "                            PetrolModel.data_details[0]['che_liang_id'],\n", "                            PetrolModel.data_details[0]['distant'],\n", "                            PetrolModel.data_details[0]['qty'],\n", "                            )\n", "                        # .filter(PetrolModel.data_details[0]['che_liang_id'].astext.cast(Integer) == 194).statement,\n", "                        # .filter(PetrolModel.data_details[0]['che_liang_id'].astext.cast(Integer).in_(dataList)).statement, \n", "                            .filter(\n", "                                and_(\n", "                                    PetrolModel.datetime > six_months_ago,\n", "                                    PetrolModel.data_details[0]['che_liang_id'].astext.cast(Integer).in_(dataList),\n", "                                    )\n", "                                ).statement,\n", "                con = session.bind)\n", "\n", "\n", "df20 = df20.rename(columns={\n", "    \"anon_1\": 'che_liang_id',\n", "    \"anon_2\": 'distant',\n", "    \"anon_3\": 'qty',\n", "    })\n", "\n", "# display(\"df20\", df20)\n", "\n", "\n", "df30 = pd.read_sql(sql = session.query(PetrolModel2)\\\n", "                        .with_entities(\n", "                            # PetrolModel2.auto_id,\n", "                            PetrolModel2.petrol_id,\n", "                            PetrolModel2.datetime,\n", "                            PetrolModel2.data_details[0]['che_liang_id'],\n", "                            PetrolModel2.data_details[0]['distant'],\n", "                            PetrolModel2.data_details[0]['qty'],\n", "                            )\n", "                        # .filter(PetrolModel.data_details[0]['che_liang_id'].astext.cast(Integer) == 194).statement,\n", "                        # .filter(PetrolModel.data_details[0]['che_liang_id'].astext.cast(Integer).in_(dataList)).statement, \n", "                            .filter(\n", "                                and_(\n", "                                    PetrolModel2.datetime > six_months_ago,\n", "                                    PetrolModel2.data_details[0]['che_liang_id'].astext.cast(Integer).in_(dataList),\n", "                                    )\n", "                                ).statement,\n", "                con = session.bind)\n", "\n", "\n", "df30 = df30.rename(columns={\n", "    \"anon_1\": 'che_liang_id',\n", "    \"anon_2\": 'distant',\n", "    \"anon_3\": 'qty',\n", "    })\n", "\n", "# display(\"df30\", df30)\n", "\n", "\n", "df40 = pd.concat([df20, df30], ignore_index=True)\n", "df40 = df40.sort_values(['datetime'], ascending=[True])\n", "# display(df40)\n", "\n", "\n", "df50 = df40[df40['che_liang_id'] ==  20141]\n", "display(df50)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import sqlalchemy\n", "from sqlalchemy import create_engine, tuple_\n", "from sqlalchemy.pool import NullPool\n", "import numpy as np\n", "import json\n", "from sqlalchemy.ext.declarative import declarative_base\n", "from sqlalchemy import Column, Integer, String, Date, DateTime, ForeignKey, TEXT\n", "from sqlalchemy.sql.expression import table, text\n", "from sqlalchemy.dialects.postgresql.json import JSONB\n", "from sqlalchemy.orm import sessionmaker\n", "from sqlalchemy import and_, or_, not_\n", "from sqlalchemy import literal\n", "import datetime\n", "import itertools\n", "\n", "DATABASE_URL = \"*************************************************/n_data\"\n", "engine = create_engine(DATABASE_URL, poolclass=NullPool)\n", "Session = sessionmaker(bind=engine)\n", "\n", "Base = declarative_base()\n", "\n", "class PetrolModel(Base):\n", "    __tablename__ = 'petrol'\n", "    auto_id = Column(Integer, primary_key=True)\n", "    petrol_id = Column(TEXT(convert_unicode=True), server_default=text(\"'petrol'||lpad(NEXTVAL('petrol_sequence'::regclass)::text,8, '0')\"))\n", "    datetime = Column(DateTime)\n", "    data_details = Column(JSONB, nullable=False, server_default='[]')\n", "    \n", "    \n", "class PetrolModel2(Base):\n", "    __tablename__ = 'petrol2'\n", "    auto_id = Column(Integer, primary_key=True)\n", "    petrol_id = Column(TEXT(convert_unicode=True), server_default=text(\"'petrol'||lpad(NEXTVAL('petrol_sequence'::regclass)::text,8, '0')\"))\n", "    datetime = Column(DateTime)\n", "    data_details = Column(JSONB, nullable=False, server_default='[]')\n", "    status_details = Column(JSONB, nullable=False, server_default='{}')    \n", "\n", "\n", "session = Session()\n", "\n", "\n", "\n", "today = datetime.date.today()\n", "future_day = today.day\n", "future_month = (today.month - 12) % 12\n", "future_year = today.year + ((today.month - 12) // 12)\n", "six_months_ago = datetime.date(future_year, future_month, future_day)\n", "\n", "\n", "df20 = pd.read_sql(sql = session.query(PetrolModel)\\\n", "                        .with_entities(\n", "                            PetrolModel.petrol_id,\n", "                            PetrolModel.datetime,\n", "                            PetrolModel.data_details[0]['che_liang_id'],\n", "                            PetrolModel.data_details[0]['distant'],\n", "                            PetrolModel.data_details[0]['qty'],\n", "                            )\n", "                            .filter(PetrolModel.datetime > six_months_ago).statement,\n", "                con = session.bind)\n", "\n", "\n", "df20 = df20.rename(columns={\n", "    \"anon_1\": 'che_liang_id',\n", "    \"anon_2\": 'distant',\n", "    \"anon_3\": 'qty',\n", "    })\n", "\n", "# display(\"df20\", df20)\n", "\n", "\n", "df30 = pd.read_sql(sql = session.query(PetrolModel2)\\\n", "                        .with_entities(\n", "                            PetrolModel2.petrol_id,\n", "                            PetrolModel2.datetime,\n", "                            PetrolModel2.data_details[0]['che_liang_id'],\n", "                            PetrolModel2.data_details[0]['distant'],\n", "                            PetrolModel2.data_details[0]['qty'],\n", "                            )\n", "                            .filter( PetrolModel2.datetime > six_months_ago).statement,\n", "                con = session.bind)\n", "\n", "\n", "df30 = df30.rename(columns={\n", "    \"anon_1\": 'che_liang_id',\n", "    \"anon_2\": 'distant',\n", "    \"anon_3\": 'qty',\n", "    })\n", "\n", "# display(\"df30\", df30)\n", "\n", "\n", "df40 = pd.concat([df20, df30], ignore_index=True)\n", "df40 = df40.sort_values(['datetime'], ascending=[True])\n", "# display(df40)\n", "\n", "\n", "df50 = df40[df40['che_liang_id'] ==  37650 ]\n", "df50['previous_distant'] = df50['distant'].shift()\n", "def fx(x):\n", "    if x['previous_distant'] == 0:\n", "        return x['previous_distant']\n", "    elif x['distant'] == 0:\n", "        return x['distant']\n", "    else:\n", "        return x['distant'] - x['previous_distant']\n", "df50['distance_used'] = df50.apply(lambda x : fx(x),axis=1)\n", "df50['previous_qty'] = df50['qty'].shift()\n", "df50['km_lit'] = (df50['distance_used'] / df50['previous_qty'])           \n", "df50['average'] = df50[df50['km_lit'] != 0][\"km_lit\"].mean()\n", "def fx2(x):\n", "    if x['km_lit'] == 0:\n", "        return x['km_lit']\n", "    else:\n", "        return (x.average - x.km_lit) / x.average * 100\n", "df50['average_percent'] = df50.apply(lambda x : fx2(x),axis=1)\n", "\n", "def remove_outliers(df50, q=0.05):\n", "    upper = df50.quantile(1-q)\n", "    lower = df50.quantile(q)\n", "    mask = (df50 < upper) & (df50 > lower)\n", "    return mask\n", "df50['outliers'] = remove_outliers(df50['km_lit'], 0.1)\n", "\n", "def fx3(x):\n", "    if x['outliers'] == False:\n", "        return None\n", "    else:\n", "        return x['distance_used'] / x['previous_qty']\n", "df50['km_lit_2'] = df50.apply(lambda x : fx3(x),axis=1)\n", "df50['average_2'] = df50[df50['km_lit_2'] != 0][\"km_lit_2\"].mean()\n", "def fx4(x):\n", "    if x['km_lit_2'] == None:\n", "        return x['km_lit_2']\n", "    else:\n", "        return (x.average_2 - x.km_lit_2) / x.average_2 * 100\n", "df50['average_percent_2'] = df50.apply(lambda x : fx4(x),axis=1)\n", "df50['mean'] = (df50.average - df50.average_2) / df50.average * 100\n", "df50 = df50.fillna(0)\n", "display(df50)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import sqlalchemy\n", "from sqlalchemy import create_engine, tuple_\n", "from sqlalchemy.pool import NullPool\n", "import numpy as np\n", "import json\n", "from sqlalchemy.ext.declarative import declarative_base\n", "from sqlalchemy import Column, Integer, String, Date, DateTime, ForeignKey, TEXT\n", "from sqlalchemy.sql.expression import table, text\n", "from sqlalchemy.dialects.postgresql.json import JSONB\n", "from sqlalchemy.orm import sessionmaker\n", "from sqlalchemy import and_, or_, not_\n", "from sqlalchemy import literal\n", "import datetime\n", "import itertools\n", "\n", "DATABASE_URL = \"*************************************************/n_data\"\n", "engine = create_engine(DATABASE_URL, poolclass=NullPool)\n", "Session = sessionmaker(bind=engine)\n", "\n", "Base = declarative_base()\n", "\n", "class PetrolModel(Base):\n", "    __tablename__ = 'petrol'\n", "    auto_id = Column(Integer, primary_key=True)\n", "    petrol_id = Column(TEXT(convert_unicode=True), server_default=text(\"'petrol'||lpad(NEXTVAL('petrol_sequence'::regclass)::text,8, '0')\"))\n", "    datetime = Column(DateTime)\n", "    data_details = Column(JSONB, nullable=False, server_default='[]')\n", "    \n", "    \n", "class PetrolModel2(Base):\n", "    __tablename__ = 'petrol2'\n", "    auto_id = Column(Integer, primary_key=True)\n", "    petrol_id = Column(TEXT(convert_unicode=True), server_default=text(\"'petrol'||lpad(NEXTVAL('petrol_sequence'::regclass)::text,8, '0')\"))\n", "    datetime = Column(DateTime)\n", "    data_details = Column(JSONB, nullable=False, server_default='[]')\n", "    status_details = Column(JSONB, nullable=False, server_default='{}')    \n", "\n", "\n", "session = Session()\n", "\n", "\n", "\n", "today = datetime.date.today()\n", "future_day = today.day\n", "future_month = (today.month - 12) % 12\n", "future_year = today.year + ((today.month - 12) // 12)\n", "six_months_ago = datetime.date(future_year, future_month, future_day)\n", "\n", "\n", "df20 = pd.read_sql(sql = session.query(PetrolModel)\\\n", "                        .with_entities(\n", "                            PetrolModel.petrol_id,\n", "                            PetrolModel.datetime,\n", "                            PetrolModel.data_details[0]['che_liang_id'],\n", "                            PetrolModel.data_details[0]['distant'],\n", "                            PetrolModel.data_details[0]['qty'],\n", "                            )\n", "                            .filter(PetrolModel.datetime > six_months_ago).statement,\n", "                con = session.bind)\n", "\n", "\n", "df20 = df20.rename(columns={\n", "    \"anon_1\": 'che_liang_id',\n", "    \"anon_2\": 'distant',\n", "    \"anon_3\": 'qty',\n", "    })\n", "\n", "# display(\"df20\", df20)\n", "\n", "\n", "df30 = pd.read_sql(sql = session.query(PetrolModel2)\\\n", "                        .with_entities(\n", "                            PetrolModel2.petrol_id,\n", "                            PetrolModel2.datetime,\n", "                            PetrolModel2.data_details[0]['che_liang_id'],\n", "                            PetrolModel2.data_details[0]['distant'],\n", "                            PetrolModel2.data_details[0]['qty'],\n", "                            )\n", "                            .filter( PetrolModel2.datetime > six_months_ago).statement,\n", "                con = session.bind)\n", "\n", "\n", "df30 = df30.rename(columns={\n", "    \"anon_1\": 'che_liang_id',\n", "    \"anon_2\": 'distant',\n", "    \"anon_3\": 'qty',\n", "    })\n", "\n", "# display(\"df30\", df30)\n", "\n", "\n", "df40 = pd.concat([df20, df30], ignore_index=True)\n", "df40 = df40.sort_values(['datetime'], ascending=[True])\n", "# display(df40)\n", "\n", "\n", "df50 = df40[df40['che_liang_id'] ==  38755 ]\n", "df50['previous_distant'] = df50['distant'].shift()\n", "def fx(x):\n", "    if x['previous_distant'] == 0:\n", "        return x['previous_distant']\n", "    elif x['distant'] == 0:\n", "        return x['distant']\n", "    else:\n", "        return x['distant'] - x['previous_distant']\n", "df50['distance_used'] = df50.apply(lambda x : fx(x),axis=1)\n", "df50['previous_qty'] = df50['qty'].shift()\n", "df50['duplicated'] = df50['distance_used'].duplicated(keep=False)\n", "df50['count'] = df50['distance_used']\n", "\n", "\n", "df50['km_lit'] = (df50['distance_used'] / df50['previous_qty'])           \n", "df50['average'] = df50[df50['km_lit'] != 0][\"km_lit\"].mean()\n", "def fx2(x):\n", "    if x['km_lit'] == 0:\n", "        return x['km_lit']\n", "    else:\n", "        return (x.km_lit - x.average) / x.average * 100\n", "df50['average_percent'] = df50.apply(lambda x : fx2(x),axis=1).round(0)\n", "\n", "def remove_outliers(df50, q=0.05):\n", "    upper = df50.quantile(1-q)\n", "    lower = df50.quantile(q)\n", "    mask = (df50 < upper) & (df50 > lower)\n", "    return mask\n", "df50['outliers'] = remove_outliers(df50['km_lit'], 0.1)\n", "\n", "def fx3(x):\n", "    if x['outliers'] == False:\n", "        return None\n", "    else:\n", "        return x['distance_used'] / x['previous_qty']\n", "df50['km_lit_2'] = df50.apply(lambda x : fx3(x),axis=1)\n", "df50['average_2'] = df50[df50['km_lit_2'] != 0][\"km_lit_2\"].mean()\n", "def fx4(x):\n", "    if x['km_lit_2'] == None:\n", "        return x['km_lit_2']\n", "    else:\n", "        return (x.km_lit_2 - x.average_2) / x.average_2 * 100\n", "df50['average_percent_2'] = df50.apply(lambda x : fx4(x),axis=1)\n", "df50['mean'] = ((df50.average_2 - df50.average) / df50.average * 100).round(0)\n", "df50['average_mean'] = df50[\"average_percent\"].astype(str) + ' , ' + df50[\"mean\"].astype(str)\n", "# display(df50)\n", "\n", "\n", "df60 = df50.pivot_table(columns=['distance_used'], aggfunc='size')\n", "df60 = pd.DataFrame(df60)\n", "df60 = df60.rename(columns={0: 'countoo'}).reset_index()\n", "# display(df60)\n", "\n", "\n", "df50['count'] = df50[['count']].replace(to_replace =df60['distance_used'].to_list(), \n", "                        value =df60['countoo'].to_list())\n", "df50['show'] = df50[\"average_mean\"].astype(str) + ' | ' + df50[\"count\"].astype(str)\n", "df50 = df50.fillna(0)\n", "display(df50)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import sqlalchemy\n", "from sqlalchemy import create_engine, tuple_\n", "from sqlalchemy.pool import NullPool\n", "import numpy as np\n", "import json\n", "from sqlalchemy.ext.declarative import declarative_base\n", "from sqlalchemy import Column, Integer, String, Date, DateTime, ForeignKey, TEXT\n", "from sqlalchemy.sql.expression import table, text\n", "from sqlalchemy.dialects.postgresql.json import JSONB\n", "from sqlalchemy.orm import sessionmaker\n", "from sqlalchemy import and_, or_, not_\n", "from sqlalchemy import literal\n", "import datetime\n", "import itertools\n", "\n", "DATABASE_URL = \"*************************************************/n_data\"\n", "engine = create_engine(DATABASE_URL, poolclass=NullPool)\n", "Session = sessionmaker(bind=engine)\n", "\n", "Base = declarative_base()\n", "\n", "class PetrolModel(Base):\n", "    __tablename__ = 'petrol'\n", "    auto_id = Column(Integer, primary_key=True)\n", "    petrol_id = Column(TEXT(convert_unicode=True), server_default=text(\"'petrol'||lpad(NEXTVAL('petrol_sequence'::regclass)::text,8, '0')\"))\n", "    datetime = Column(DateTime)\n", "    data_details = Column(JSONB, nullable=False, server_default='[]')\n", "    \n", "    \n", "class PetrolModel2(Base):\n", "    __tablename__ = 'petrol2'\n", "    auto_id = Column(Integer, primary_key=True)\n", "    petrol_id = Column(TEXT(convert_unicode=True), server_default=text(\"'petrol'||lpad(NEXTVAL('petrol_sequence'::regclass)::text,8, '0')\"))\n", "    datetime = Column(DateTime)\n", "    data_details = Column(JSONB, nullable=False, server_default='[]')\n", "    status_details = Column(JSONB, nullable=False, server_default='{}')    \n", "\n", "\n", "session = Session()\n", "\n", "\n", "\n", "today = datetime.date.today()\n", "future_day = today.day\n", "future_month = (today.month - 12) % 12\n", "future_year = today.year + ((today.month - 12) // 12)\n", "six_months_ago = datetime.date(future_year, future_month, future_day)\n", "\n", "\n", "df20 = pd.read_sql(sql = session.query(PetrolModel)\\\n", "                        .with_entities(\n", "                            PetrolModel.petrol_id,\n", "                            PetrolModel.datetime,\n", "                            PetrolModel.data_details[0]['che_liang_id'],\n", "                            PetrolModel.data_details[0]['distant'],\n", "                            PetrolModel.data_details[0]['qty'],\n", "                            )\n", "                            .filter(PetrolModel.datetime > six_months_ago).statement,\n", "                con = session.bind)\n", "\n", "\n", "df20 = df20.rename(columns={\n", "    \"anon_1\": 'che_liang_id',\n", "    \"anon_2\": 'distant',\n", "    \"anon_3\": 'qty',\n", "    })\n", "\n", "# display(\"df20\", df20)\n", "\n", "\n", "df30 = pd.read_sql(sql = session.query(PetrolModel2)\\\n", "                        .with_entities(\n", "                            PetrolModel2.petrol_id,\n", "                            PetrolModel2.datetime,\n", "                            PetrolModel2.data_details[0]['che_liang_id'],\n", "                            PetrolModel2.data_details[0]['distant'],\n", "                            PetrolModel2.data_details[0]['qty'],\n", "                            )\n", "                            .filter( PetrolModel2.datetime > six_months_ago).statement,\n", "                con = session.bind)\n", "\n", "\n", "df30 = df30.rename(columns={\n", "    \"anon_1\": 'che_liang_id',\n", "    \"anon_2\": 'distant',\n", "    \"anon_3\": 'qty',\n", "    })\n", "\n", "# display(\"df30\", df30)\n", "\n", "\n", "df40 = pd.concat([df20, df30], ignore_index=True)\n", "df40 = df40.sort_values(['datetime'], ascending=[True])\n", "# display(df40)\n", "\n", "\n", "df50 = df40[df40['che_liang_id'] ==  19937 ]\n", "df50['previous_distant'] = df50['distant'].shift()\n", "def fx(x):\n", "    if x['previous_distant'] == 0:\n", "        return x['previous_distant']\n", "    elif x['distant'] == 0:\n", "        return x['distant']\n", "    else:\n", "        return x['distant'] - x['previous_distant']\n", "df50['distance_used'] = df50.apply(lambda x : fx(x),axis=1)\n", "df50['previous_qty'] = df50['qty'].shift()\n", "df50['duplicated'] = df50['distance_used'].duplicated(keep=False)\n", "df50['count'] = df50['distance_used']\n", "\n", "\n", "df50['km_lit'] = (df50['distance_used'] / df50['previous_qty'])         \n", "df50['average'] = df50[df50['km_lit'] != 0][\"km_lit\"].mean()\n", "def fx2(x):\n", "    if x['km_lit'] == 0:\n", "        return x['km_lit']\n", "    else:\n", "        return (x.km_lit - x.average) / x.average * 100\n", "df50['average_percent'] = df50.apply(lambda x : fx2(x),axis=1)\n", "\n", "def remove_outliers(df50, q=0.05):\n", "    upper = df50.quantile(1-q)\n", "    lower = df50.quantile(q)\n", "    mask = (df50 < upper) & (df50 > lower)\n", "    return mask\n", "df50['outliers'] = remove_outliers(df50['km_lit'], 0.1)\n", "\n", "def fx3(x):\n", "    if x['outliers'] == False:\n", "        return None\n", "    else:\n", "        return x['distance_used'] / x['previous_qty']\n", "df50['km_lit_2'] = df50.apply(lambda x : fx3(x),axis=1)\n", "df50['average_2'] = df50[df50['km_lit_2'] != 0][\"km_lit_2\"].mean()\n", "def fx4(x):\n", "    if x['km_lit_2'] == None:\n", "        return x['km_lit_2']\n", "    else:\n", "        return (x.km_lit_2 - x.average_2) / x.average_2 * 100\n", "df50['average_percent_2'] = df50.apply(lambda x : fx4(x),axis=1)\n", "df50['mean'] = ((df50.average_2 - df50.average) / df50.average * 100)\n", "df50['average_mean'] = df50[\"average_percent\"].astype(str) + ' , ' + df50[\"mean\"].astype(str)\n", "# display(df50)\n", "\n", "\n", "df60 = df50.pivot_table(columns=['distance_used'], aggfunc='size')\n", "df60 = pd.DataFrame(df60)\n", "df60 = df60.rename(columns={0: 'countoo'}).reset_index()\n", "# display(df60)\n", "\n", "\n", "df50['count'] = df50[['count']].replace(to_replace =df60['distance_used'].to_list(), \n", "                        value =df60['countoo'].to_list())\n", "df50['show'] = df50[\"average_mean\"].astype(str) + ' | ' + df50[\"count\"].astype(str)\n", "\n", "# df50 = df50.describe()\n", "# df50['std'] = df50['distance_used'].std()\n", "df50 = df50.fillna(0).round(decimals = 1)\n", "display(df50)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import sqlalchemy\n", "from sqlalchemy import create_engine, tuple_\n", "from sqlalchemy.pool import NullPool\n", "import numpy as np\n", "import json\n", "from sqlalchemy.ext.declarative import declarative_base\n", "from sqlalchemy import Column, Integer, String, Date, DateTime, ForeignKey, TEXT, Float\n", "from sqlalchemy.sql.expression import table, text\n", "from sqlalchemy.dialects.postgresql.json import JSONB\n", "from sqlalchemy.orm import sessionmaker\n", "from sqlalchemy import and_, or_, not_\n", "from sqlalchemy import literal\n", "import datetime\n", "import itertools\n", "import requests\n", "\n", "DATABASE_URL = \"*************************************************/n_data\"\n", "engine = create_engine(DATABASE_URL, poolclass=NullPool)\n", "Session = sessionmaker(bind=engine)\n", "\n", "Base = declarative_base()\n", "\n", "class PetrolModel(Base):\n", "    __tablename__ = 'petrol'\n", "    auto_id = Column(Integer, primary_key=True)\n", "    petrol_id = Column(TEXT(convert_unicode=True), server_default=text(\"'petrol'||lpad(NEXTVAL('petrol_sequence'::regclass)::text,8, '0')\"))\n", "    datetime = Column(DateTime)\n", "    data_details = Column(JSONB, nullable=False, server_default='[]')\n", "    \n", "    \n", "class Calculate(Base):\n", "    __tablename__ = 'petrol_calculate'\n", "    auto_id = Column(Integer, primary_key=True)\n", "    head = Column(JSONB, nullable=False, server_default='{}')  \n", "    \n", "\n", "session = Session()\n", "\n", "\n", "\n", "df81 = pd.read_sql(sql = session.query(PetrolModel)\n", "                        .with_entities(\n", "                            PetrolModel.data_details[0]['che_liang_id'],\n", "                            PetrolModel.data_details[0]['fen_dian_id'],\n", "                        )\n", "                        .distinct(PetrolModel.data_details[0]['che_liang_id'])\n", "                        .filter(\n", "                            not_(\n", "                                PetrolModel.data_details[0]['bill'].astext.cast(String) == \"\",\n", "                                )\n", "                            )\n", "                        .statement, con = session.bind)\n", "\n", "\n", "df81 = df81.rename(columns={\n", "    \"anon_1\": 'che_liang_id',\n", "    \"anon_2\": 'fen_dian_id',\n", "    })\n", "display(df81)\n", "\n", "\n", "df91 = pd.read_sql(sql = session.query(Calculate)\n", "    .with_entities(\n", "        Calculate.head['petrol_id'],\n", "        Calculate.head['datetime'],\n", "        Calculate.head['che_liang_id'],\n", "        Calculate.head['average'],\n", "        Calculate.head['minute'],\n", "        Calculate.head['oneKmLit'],\n", "        Calculate.head['hundredKmLit'],\n", "        Calculate.head['average_2'],\n", "        Calculate.head['mean'],\n", "        Calculate.head['kmLitStd'],\n", "        Calculate.head['kmLitMin'],\n", "        Calculate.head['kmLit25Per'],\n", "        Calculate.head['kmLit50Per'],\n", "        Calculate.head['kmLit75Per'],\n", "        Calculate.head['kmLitMax'],\n", "        Calculate.head['countDuplicate'],\n", "        Calculate.head['che_liang_id_Count'],\n", "    )               \n", "    .distinct(Calculate.head['che_liang_id'])\n", "    .order_by(Calculate.head['che_liang_id'], Calculate.head['datetime'].desc())\n", "    .statement, con = session.bind)\n", "\n", "df91 = df91.rename(columns={\n", "    \"anon_1\": 'petrol_id',\n", "    \"anon_2\": 'datetime',\n", "    \"anon_3\": 'che_liang_id',\n", "    \"anon_4\": 'average',\n", "    \"anon_5\": 'minute',\n", "    \"anon_6\": 'oneKmLit',\n", "    \"anon_7\": 'hundredKmLit',\n", "    \"anon_8\": 'average_2',\n", "    \"anon_9\": 'mean',\n", "    \"anon_10\": 'kmLitStd',\n", "    \"anon_11\": 'kmLitMin',\n", "    \"anon_12\": 'kmLit25Per',\n", "    \"anon_13\": 'kmLit50Per',\n", "    \"anon_14\": 'kmLit75Per',\n", "    \"anon_15\": 'kmLitMax',\n", "    \"anon_16\": 'countDuplicate',\n", "    \"anon_17\": 'che_liang_id_Count',\n", "})\n", "display(df91)\n", "\n", "\n", "df100 = df81\n", "df100 = df100.rename(columns={\"che_liang_id\": 'jia_yi_id'})\n", "display(df100)\n", "\n", "url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'\n", "# url = Arter_api + 'jia_yi_name_list_id'\n", "to_dict = df100.to_dict('records')\n", "body_raw = {\"data_api\": to_dict}\n", "df110 = requests.get(url=url, json=body_raw).json()\n", "df110 = pd.DataFrame(df110)\n", "display(df110)\n", "\n", "\n", "merge = df81.merge(df91, on='che_liang_id', how='left').merge(df110, left_on='che_liang_id', right_on='jia_yi_id', how='left')\n", "display(merge)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import sqlalchemy\n", "from sqlalchemy import create_engine, tuple_\n", "from sqlalchemy.pool import NullPool\n", "import numpy as np\n", "import json\n", "from sqlalchemy.ext.declarative import declarative_base\n", "from sqlalchemy import Column, Integer, String, Date, DateTime, ForeignKey, TEXT\n", "from sqlalchemy.sql.expression import table, text\n", "from sqlalchemy.dialects.postgresql.json import JSONB\n", "from sqlalchemy.orm import sessionmaker\n", "from sqlalchemy import and_, or_, not_\n", "from sqlalchemy import literal\n", "import datetime\n", "import itertools\n", "\n", "DATABASE_URL = \"*************************************************/n_data\"\n", "engine = create_engine(DATABASE_URL, poolclass=NullPool)\n", "Session = sessionmaker(bind=engine)\n", "\n", "Base = declarative_base()\n", "\n", "class PetrolModel(Base):\n", "    __tablename__ = 'petrol'\n", "    auto_id = Column(Integer, primary_key=True)\n", "    petrol_id = Column(TEXT(convert_unicode=True), server_default=text(\"'petrol'||lpad(NEXTVAL('petrol_sequence'::regclass)::text,8, '0')\"))\n", "    datetime = Column(DateTime)\n", "    data_details = Column(JSONB, nullable=False, server_default='[]')\n", "    \n", "    \n", "class PetrolModel2(Base):\n", "    __tablename__ = 'petrol2'\n", "    auto_id = Column(Integer, primary_key=True)\n", "    petrol_id = Column(TEXT(convert_unicode=True), server_default=text(\"'petrol'||lpad(NEXTVAL('petrol_sequence'::regclass)::text,8, '0')\"))\n", "    datetime = Column(DateTime)\n", "    data_details = Column(JSONB, nullable=False, server_default='[]')\n", "    status_details = Column(JSONB, nullable=False, server_default='{}')    \n", "\n", "\n", "session = Session()\n", "\n", "\n", "\n", "today = datetime.date.today()\n", "future_day = today.day\n", "future_month = (today.month - 12) % 12\n", "future_year = today.year + ((today.month - 12) // 12)\n", "six_months_ago = datetime.date(future_year, future_month, future_day)\n", "\n", "\n", "df20 = pd.read_sql(sql = session.query(PetrolModel)\\\n", "                        .with_entities(\n", "                            PetrolModel.petrol_id,\n", "                            PetrolModel.datetime,\n", "                            PetrolModel.data_details[0]['che_liang_id'],\n", "                            PetrolModel.data_details[0]['distant'],\n", "                            PetrolModel.data_details[0]['qty'],\n", "                            PetrolModel.data_details[0]['price'],\n", "                            PetrolModel.data_details[0]['product_id'],\n", "                            )\n", "                            .filter(PetrolModel.datetime > six_months_ago).statement,\n", "                con = session.bind)\n", "\n", "\n", "df20 = df20.rename(columns={\n", "    \"anon_1\": 'che_liang_id',\n", "    \"anon_2\": 'distant',\n", "    \"anon_3\": 'qty',\n", "    \"anon_4\": 'price',\n", "    \"anon_5\": 'product_id',\n", "    })\n", "\n", "# display(\"df20\", df20)\n", "\n", "\n", "df30 = pd.read_sql(sql = session.query(PetrolModel2)\\\n", "                        .with_entities(\n", "                            PetrolModel2.petrol_id,\n", "                            PetrolModel2.datetime,\n", "                            PetrolModel2.data_details[0]['che_liang_id'],\n", "                            PetrolModel2.data_details[0]['distant'],\n", "                            PetrolModel2.data_details[0]['qty'],\n", "                            PetrolModel2.data_details[0]['price'],\n", "                            PetrolModel2.data_details[0]['product_id'],\n", "                            )\n", "                            .filter( PetrolModel2.datetime > six_months_ago).statement,\n", "                con = session.bind)\n", "\n", "\n", "df30 = df30.rename(columns={\n", "    \"anon_1\": 'che_liang_id',\n", "    \"anon_2\": 'distant',\n", "    \"anon_3\": 'qty',\n", "    \"anon_4\": 'price',\n", "    \"anon_5\": 'product_id',\n", "    })\n", "\n", "# display(\"df30\", df30)\n", "\n", "\n", "df40 = pd.concat([df20, df30], ignore_index=True)\n", "df40 = df40.sort_values(['datetime'], ascending=[True])\n", "# display(df40)\n", "\n", "\n", "df50 = df40[df40['che_liang_id'] ==  20141 ]\n", "df50['amount'] = df50['qty'] * df50['price']\n", "df50['count'] = 'calculate'\n", "df50['previous_distant'] = df50['distant'].shift()\n", "def fx(x):\n", "        if x['previous_distant'] == 0:\n", "            return x['previous_distant']\n", "        elif x['distant'] == 0:\n", "            return x['distant']\n", "        else:\n", "            return x['distant'] - x['previous_distant']\n", "df50['distance_used'] = df50.apply(lambda x : fx(x),axis=1)\n", "df50['previous_qty'] = df50['qty'].shift()\n", "df50['km_lit'] = (df50['distance_used'] / df50['previous_qty']) \n", "display(df50)\n", "\n", "\n", "df60 = df50.set_index('datetime', inplace=True)\n", "df60 = df50.resample('MS').agg({'che_liang_id': np.max, 'qty': np.sum, 'price': np.mean, 'amount': np.sum, 'count': np.size, 'km_lit': np.mean}).reset_index().round(decimals = 1)\n", "display(df60)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import sqlalchemy\n", "from sqlalchemy import create_engine, tuple_\n", "from sqlalchemy.pool import NullPool\n", "import numpy as np\n", "import json\n", "from sqlalchemy.ext.declarative import declarative_base\n", "from sqlalchemy import Column, Integer, String, Date, DateTime, ForeignKey, TEXT, Float\n", "from sqlalchemy.sql.expression import table, text\n", "from sqlalchemy.dialects.postgresql.json import JSONB\n", "from sqlalchemy.orm import sessionmaker\n", "from sqlalchemy import and_, or_, not_\n", "from sqlalchemy import literal\n", "import datetime\n", "import itertools\n", "import requests\n", "\n", "DATABASE_URL = \"*************************************************/n_data\"\n", "engine = create_engine(DATABASE_URL, poolclass=NullPool)\n", "Session = sessionmaker(bind=engine)\n", "\n", "Base = declarative_base()\n", "\n", "class PetrolModel(Base):\n", "    __tablename__ = 'petrol'\n", "    auto_id = Column(Integer, primary_key=True)\n", "    petrol_id = Column(TEXT(convert_unicode=True), server_default=text(\"'petrol'||lpad(NEXTVAL('petrol_sequence'::regclass)::text,8, '0')\"))\n", "    datetime = Column(DateTime)\n", "    data_details = Column(JSONB, nullable=False, server_default='[]')\n", "    \n", "    \n", "class Calculate(Base):\n", "    __tablename__ = 'petrol_calculate'\n", "    auto_id = Column(Integer, primary_key=True)\n", "    head = Column(JSONB, nullable=False, server_default='{}')  \n", "    \n", "\n", "session = Session()\n", "\n", "\n", "\n", "df81 = pd.read_sql(sql = session.query(PetrolModel)\n", "                        .with_entities(\n", "                            PetrolModel.data_details[0]['che_liang_id'],\n", "                        )\n", "                        .distinct(PetrolModel.data_details[0]['che_liang_id'])\n", "                        .filter(PetrolModel.data_details[0]['bill'].astext.cast(String) == \"\")\n", "                        .statement, con = session.bind)\n", "\n", "\n", "df81 = df81.rename(columns={\n", "    \"anon_1\": 'che_liang_id',\n", "    })\n", "display(df81)\n", "\n", "\n", "df91 = pd.read_sql(sql = session.query(Calculate)\n", "    .with_entities(\n", "        Calculate.head['petrol_id'],\n", "        Calculate.head['datetime'],\n", "        Calculate.head['che_liang_id'],\n", "        Calculate.head['average'],\n", "        Calculate.head['minute'],\n", "        Calculate.head['oneKmLit'],\n", "        Calculate.head['hundredKmLit'],\n", "        Calculate.head['average_2'],\n", "        Calculate.head['mean'],\n", "        Calculate.head['kmLitStd'],\n", "        Calculate.head['kmLitMin'],\n", "        Calculate.head['kmLit25Per'],\n", "        Calculate.head['kmLit50Per'],\n", "        Calculate.head['kmLit75Per'],\n", "        Calculate.head['kmLitMax'],\n", "        Calculate.head['countDuplicate'],\n", "        Calculate.head['che_liang_id_Count'],\n", "    )               \n", "    .distinct(Calculate.head['che_liang_id'])\n", "    .order_by(Calculate.head['che_liang_id'], Calculate.head['datetime'].desc())\n", "    .statement, con = session.bind)\n", "\n", "df91 = df91.rename(columns={\n", "    \"anon_1\": 'petrol_id',\n", "    \"anon_2\": 'datetime',\n", "    \"anon_3\": 'che_liang_id',\n", "    \"anon_4\": 'average',\n", "    \"anon_5\": 'minute',\n", "    \"anon_6\": 'oneKmLit',\n", "    \"anon_7\": 'hundredKmLit',\n", "    \"anon_8\": 'average_2',\n", "    \"anon_9\": 'mean',\n", "    \"anon_10\": 'kmLitStd',\n", "    \"anon_11\": 'kmLitMin',\n", "    \"anon_12\": 'kmLit25Per',\n", "    \"anon_13\": 'kmLit50Per',\n", "    \"anon_14\": 'kmLit75Per',\n", "    \"anon_15\": 'kmLitMax',\n", "    \"anon_16\": 'countDuplicate',\n", "    \"anon_17\": 'che_liang_id_Count',\n", "})\n", "display(df91)\n", "\n", "\n", "df100 = df81\n", "df100 = df100.rename(columns={\"che_liang_id\": 'jia_yi_id'})\n", "display(df100)\n", "\n", "url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'\n", "# url = Arter_api + 'jia_yi_name_list_id'\n", "to_dict = df100.to_dict('records')\n", "body_raw = {\"data_api\": to_dict}\n", "df110 = requests.get(url=url, json=body_raw).json()\n", "df110 = pd.DataFrame(df110)\n", "display(df110)\n", "\n", "\n", "merge = df81.merge(df91, on='che_liang_id', how='left').merge(df110, left_on='che_liang_id', right_on='jia_yi_id', how='left')\n", "display(merge)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import warnings\n", "warnings.simplefilter('ignore')\n", "\n", "import pandas as pd\n", "import sqlalchemy\n", "from sqlalchemy import create_engine, tuple_\n", "from sqlalchemy.pool import NullPool\n", "import numpy as np\n", "import json\n", "from sqlalchemy.ext.declarative import declarative_base\n", "from sqlalchemy import Column, Integer, String, Date, DateTime, ForeignKey, TEXT\n", "from sqlalchemy.sql.expression import table, text\n", "from sqlalchemy.dialects.postgresql.json import JSONB\n", "from sqlalchemy.orm import sessionmaker\n", "from sqlalchemy import and_, or_, not_\n", "from sqlalchemy import literal\n", "import datetime\n", "import itertools\n", "from sklearn.ensemble import IsolationForest\n", "\n", "DATABASE_URL = \"*************************************************/test_nern\"\n", "engine = create_engine(DATABASE_URL, poolclass=NullPool)\n", "Session = sessionmaker(bind=engine)\n", "\n", "Base = declarative_base()\n", "\n", "class PetrolModel(Base):\n", "    __tablename__ = 'petrol'\n", "    auto_id = Column(Integer, primary_key=True)\n", "    petrol_id = Column(TEXT(convert_unicode=True), server_default=text(\"'petrol'||lpad(NEXTVAL('petrol_sequence'::regclass)::text,8, '0')\"))\n", "    datetime = Column(DateTime)\n", "    data_details = Column(JSONB, nullable=False, server_default='[]')\n", "    \n", "    \n", "class PetrolModel2(Base):\n", "    __tablename__ = 'petrol2'\n", "    auto_id = Column(Integer, primary_key=True)\n", "    petrol_id = Column(TEXT(convert_unicode=True), server_default=text(\"'petrol'||lpad(NEXTVAL('petrol_sequence'::regclass)::text,8, '0')\"))\n", "    datetime = Column(DateTime)\n", "    data_details = Column(JSONB, nullable=False, server_default='[]')\n", "    status_details = Column(JSONB, nullable=False, server_default='{}')    \n", "\n", "\n", "session = Session()\n", "\n", "\n", "\n", "today = datetime.date.today()\n", "future_day = today.day\n", "future_month = (today.month - 3) % 12\n", "future_year = today.year + ((today.month - 3) // 12)\n", "six_months_ago = datetime.date(future_year, future_month, future_day)\n", "\n", "\n", "df20 = pd.read_sql(sql = session.query(PetrolModel)\\\n", "                        .with_entities(\n", "                            PetrolModel.petrol_id,\n", "                            PetrolModel.datetime,\n", "                            PetrolModel.data_details[0]['che_liang_id'],\n", "                            PetrolModel.data_details[0]['distant'],\n", "                            PetrolModel.data_details[0]['qty'],\n", "                            )\n", "                            .filter(PetrolModel.datetime > six_months_ago).statement,\n", "                con = session.bind)\n", "\n", "\n", "df20 = df20.rename(columns={\n", "    \"anon_1\": 'che_liang_id',\n", "    \"anon_2\": 'distant',\n", "    \"anon_3\": 'qty',\n", "    })\n", "\n", "# display(\"df20\", df20)\n", "\n", "\n", "df30 = pd.read_sql(sql = session.query(PetrolModel2)\\\n", "                        .with_entities(\n", "                            PetrolModel2.petrol_id,\n", "                            PetrolModel2.datetime,\n", "                            PetrolModel2.data_details[0]['che_liang_id'],\n", "                            PetrolModel2.data_details[0]['distant'],\n", "                            PetrolModel2.data_details[0]['qty'],\n", "                            )\n", "                            .filter( PetrolModel2.datetime > six_months_ago).statement,\n", "                con = session.bind)\n", "\n", "\n", "df30 = df30.rename(columns={\n", "    \"anon_1\": 'che_liang_id',\n", "    \"anon_2\": 'distant',\n", "    \"anon_3\": 'qty',\n", "    })\n", "\n", "# display(\"df30\", df30)\n", "\n", "\n", "df40 = pd.concat([df20, df30], ignore_index=True)\n", "df40 = df40.sort_values(['datetime'], ascending=[True])\n", "# display(df40)\n", "\n", "\n", "df50 = df40[df40['che_liang_id'] ==  18576 ]\n", "df50['previous_distant'] = df50['distant'].shift()\n", "def fx(x):\n", "    if x['previous_distant'] == 0:\n", "        return x['previous_distant']\n", "    elif x['distant'] == 0:\n", "        return x['distant']\n", "    else:\n", "        return x['distant'] - x['previous_distant']\n", "df50['distance_used'] = df50.apply(lambda x : fx(x),axis=1).fillna(0)\n", "df50['previous_qty'] = df50['qty'].shift()\n", "df50['duplicated'] = df50['distance_used'].duplicated(keep=False)\n", "df50['count'] = df50['distance_used']\n", "\n", "\n", "df50['km_lit'] = (df50['distance_used'] / df50['previous_qty'])         \n", "df50['average'] = df50[df50['km_lit'] != 0][\"km_lit\"].mean()\n", "def fx2(x):\n", "    if x['km_lit'] == 0:\n", "        return x['km_lit']\n", "    else:\n", "        return (x.km_lit - x.average) / x.average * 100\n", "df50['average_percent'] = df50.apply(lambda x : fx2(x),axis=1)\n", "\n", "\n", "outlier_detector = IsolationForest(random_state=42)\n", "outlier_detector.fit(df50.loc[:,['distance_used']])\n", "prediction = outlier_detector.predict(df50.loc[:,['distance_used']])\n", "prediction_strings = ['Outlier' if x < 0 else 'Standard' for x in prediction]\n", "df50['outlier_flag'] = prediction_strings\n", "\n", "def fx3(x):\n", "    if x['outlier_flag'] == 'Outlier':\n", "        return None\n", "    else:\n", "        return x['distance_used'] / x['previous_qty']\n", "df50['km_lit_2'] = df50.apply(lambda x : fx3(x),axis=1)\n", "df50['average_2'] = df50[df50['km_lit_2'] != 0][\"km_lit_2\"].mean()\n", "def fx4(x):\n", "    if x['km_lit_2'] == None:\n", "        return x['km_lit_2']\n", "    else:\n", "        return (x.km_lit_2 - x.average_2) / x.average_2 * 100\n", "df50['average_percent_2'] = df50.apply(lambda x : fx4(x),axis=1)\n", "df50['mean'] = ((df50.average_2 - df50.average) / df50.average * 100)\n", "df50['average_mean'] = df50[\"average_percent\"].astype(str) + ' , ' + df50[\"mean\"].astype(str)\n", "# display(df50)\n", "\n", "\n", "df60 = df50.pivot_table(columns=['distance_used'], aggfunc='size')\n", "df60 = pd.DataFrame(df60)\n", "df60 = df60.rename(columns={0: 'countoo'}).reset_index()\n", "# display(df60)\n", "\n", "\n", "df50['count'] = df50[['count']].replace(to_replace =df60['distance_used'].to_list(), \n", "                        value =df60['countoo'].to_list())\n", "df50['show'] = df50[\"average_mean\"].astype(str) + ' | ' + df50[\"count\"].astype(str)\n", "\n", "df50 = df50.fillna(0).round(decimals = 1)\n", "display(df50)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import sqlalchemy\n", "from sqlalchemy import create_engine, tuple_\n", "from sqlalchemy.pool import NullPool\n", "import numpy as np\n", "import json\n", "from sqlalchemy.ext.declarative import declarative_base\n", "from sqlalchemy import Column, Integer, String, Date, DateTime, ForeignKey, TEXT\n", "from sqlalchemy.sql.expression import table, text\n", "from sqlalchemy.dialects.postgresql.json import JSONB\n", "from sqlalchemy.orm import sessionmaker\n", "from sqlalchemy import and_, or_, not_\n", "from sqlalchemy import literal\n", "import datetime\n", "import itertools\n", "from sklearn.ensemble import IsolationForest\n", "from sqlalchemy.orm.attributes import flag_modified\n", "import requests\n", "\n", "DATABASE_URL = \"*************************************************/n_data\"\n", "engine = create_engine(DATABASE_URL, poolclass=NullPool)\n", "# engine = create_engine(DATABASE_URL)\n", "Session = sessionmaker(bind=engine)\n", "\n", "Base = declarative_base()\n", "\n", "class PetrolModel(Base):\n", "    __tablename__ = 'petrol'\n", "    auto_id = Column(Integer, primary_key=True)\n", "    petrol_id = Column(TEXT(convert_unicode=True), server_default=text(\"'petrol'||lpad(NEXTVAL('petrol_sequence'::regclass)::text,8, '0')\"))\n", "    datetime = Column(DateTime)\n", "    data_details = Column(JSONB, nullable=False, server_default='[]')\n", "    status_details = Column(JSONB, nullable=False, server_default='{}')\n", "    update_time = Column(DateTime)\n", "    \n", "    \n", "class PetrolModel2(Base):\n", "    __tablename__ = 'petrol2'\n", "    auto_id = Column(Integer, primary_key=True)\n", "    petrol_id = Column(TEXT(convert_unicode=True), server_default=text(\"'petrol'||lpad(NEXTVAL('petrol_sequence'::regclass)::text,8, '0')\"))\n", "    datetime = Column(DateTime)\n", "    data_details = Column(JSONB, nullable=False, server_default='[]')\n", "    status_details = Column(JSONB, nullable=False, server_default='{}')    \n", "\n", "\n", "session = Session()\n", "\n", "\n", "\n", "# user = session.query(PetrolModel).filter(\n", "#     PetrolModel.petrol_id == 'petrol00000627').first()\n", "# for var in user.data_details:\n", "#     print(var)\n", "#     if var['r_id'] == 'e123':\n", "#         var.update(dict(bill=\"success\"))\n", "# flag_modified(user, 'data_details')\n", "# session.commit()\n", "\n", "\n", "\n", "\n", "# user = session.query(PetrolModel).all()\n", "# for var in user:\n", "#     print(var.data_details[0]['r_id'])\n", "#     if var.data_details[0]['r_id'] == 'e123':\n", "#         aaa = var.data_details[0]\n", "#         aaa.update({'bill': 'sssssssssss'})\n", "# flag_modified(user, 'data_details')\n", "# session.commit()\n", "# session.close()\n", "\n", "\n", "# user = session.query(PetrolModel).all()\n", "# for var in user:\n", "#     print(var.data_details[0])\n", "#     aaa = var.data_details[0]\n", "#     aaa.update({'bill': 'sssssssssss'})\n", "# flag_modified(user, 'data_details')\n", "# session.commit()\n", "# session.close()\n", "\n", "\n", "# info = session.query(PetrolModel).filter_by(petrol_id='petrol00000625').first()\n", "# info.status_details = dict(status='a156')\n", "# session.add(info)\n", "# session.commit()\n", "\n", "\n", "# info = session.query(PetrolModel).filter_by(petrol_id='petrol00000625').first()\n", "# info.data_details[0]['bill'] = 'zxc'\n", "# flag_modified(info, 'data_details')\n", "# session.commit()\n", "\n", "\n", "# info = session.query(PetrolModel).filter_by(petrol_id='petrol00000625').first()\n", "# info.status_details['status'] = '1'\n", "# flag_modified(info, 'status_details')\n", "# session.commit()\n", "# session.close()\n", "\n", "# user = session.query(PetrolModel).all()\n", "# for row in user:  # all() is extra\n", "#     print(row.data_details[0]['distant'])\n", "#     row.data_details[0]['distant'] = int(row.data_details[0]['distant']/100)\n", "#     flag_modified(row, 'data_details')\n", "# session.commit()\n", "# session.close()\n", "\n", "\n", "\n", "\n", "# df81 = session.query(PetrolModel2).filter(PetrolModel2.data_details[0]['che_liang_id'].astext.cast(Integer) == 13417).all()\n", "# for row in df81:\n", "#     print(row.datetime)\n", "#     row.data_details[0]['distant'] = int(row.data_details[0]['distant']/10)\n", "#     flag_modified(row, 'data_details')\n", "# session.commit()\n", "# session.close()\n", "\n", "# df81\n", "\n", "\n", "\n", "bbb = 1005.2\n", "vvv = 1005\n", "print(int(bbb/10))\n", "\n", "\n", "\n", "print(int(str(vvv)[:-1]))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import warnings\n", "warnings.simplefilter('ignore')\n", "\n", "import pandas as pd\n", "import sqlalchemy\n", "from sqlalchemy import create_engine, tuple_\n", "from sqlalchemy.pool import NullPool\n", "import numpy as np\n", "import json\n", "from sqlalchemy.ext.declarative import declarative_base\n", "from sqlalchemy import Column, Integer, String, Date, DateTime, ForeignKey, TEXT\n", "from sqlalchemy.sql.expression import table, text\n", "from sqlalchemy.dialects.postgresql.json import JSONB\n", "from sqlalchemy.orm import sessionmaker\n", "from sqlalchemy import and_, or_, not_\n", "from sqlalchemy import literal\n", "from datetime import date, datetime, timedelta\n", "import datetime\n", "import itertools\n", "from sklearn.ensemble import IsolationForest\n", "from sqlalchemy.orm.attributes import flag_modified\n", "import requests\n", "from functools import reduce\n", "\n", "\n", "DATABASE_URL = \"*************************************************/test_nern\"\n", "engine = create_engine(DATABASE_URL, poolclass=NullPool)\n", "# engine = create_engine(DATABASE_URL)\n", "Session = sessionmaker(bind=engine)\n", "\n", "Base = declarative_base()\n", "\n", "class PetrolModel(Base):\n", "    __tablename__ = 'petrol'\n", "    auto_id = Column(Integer, primary_key=True)\n", "    petrol_id = Column(TEXT(convert_unicode=True), server_default=text(\"'petrol'||lpad(NEXTVAL('petrol_sequence'::regclass)::text,8, '0')\"))\n", "    datetime = Column(DateTime)\n", "    data_details = Column(JSONB, nullable=False, server_default='[]')\n", "    status_details = Column(JSONB, nullable=False, server_default='{}')\n", "    update_time = Column(DateTime)\n", "    \n", "    \n", "class PetrolModel2(Base):\n", "    __tablename__ = 'petrol2'\n", "    auto_id = Column(Integer, primary_key=True)\n", "    petrol_id = Column(TEXT(convert_unicode=True), server_default=text(\"'petrol'||lpad(NEXTVAL('petrol_sequence'::regclass)::text,8, '0')\"))\n", "    datetime = Column(DateTime)\n", "    data_details = Column(JSONB, nullable=False, server_default='[]')\n", "    status_details = Column(JSONB, nullable=False, server_default='{}')    \n", "\n", "\n", "session = Session()\n", "\n", "\n", "day = pd.DataFrame({'day':[pd.to_datetime(\"today\")]})\n", "day[\"day\"] = day[\"day\"].dt.strftime(\"%Y-%m-%d\")\n", "day = day.to_string().replace(\"          day\\n0  \",\"\")\n", "# display(\"day\", day)\n", "\n", "\n", "day2 = pd.DataFrame({'day2':[pd.to_datetime(\"today\")]})\n", "day2[\"day2\"] = day2[\"day2\"].dt.strftime(\"%Y-%m-%d %H:%M:%S\")\n", "day2 = day2.to_string().replace(\"                  day2\\n0  \",\"\")\n", "# display(\"day2\", day2)\n", "\n", "\n", "day3 = pd.DataFrame({'day3':[pd.to_datetime(\"today\")]})\n", "day3[\"day3\"] = day3[\"day3\"].dt.strftime(\"%Y-%m-%d 12:05:00\")\n", "day3 = day3.to_string().replace(\"                  day3\\n0  \",\"\")\n", "# display(\"day3\", day3)\n", "\n", "\n", "yesterday = pd.DataFrame({'yesterday':[pd.to_datetime(\"today\").normalize() - <PERSON><PERSON><PERSON>(1)]})\n", "yesterday = yesterday.to_string().replace(\"   yesterday\\n0 \",\"\")\n", "# display(\"yesterday\", yesterday)\n", "\n", "\n", "auto_id = 'petrol00000720'\n", "sql = \"\"\"SELECT  * from petrol where petrol_id = %s\"\"\"\n", "database = pd.read_sql_query(sql, engine, params=[auto_id])\n", "to_record = database.to_dict('records')\n", "json_nor = pd.json_normalize(to_record, \"data_details\", [\"petrol_id\", \"datetime\", \"status_details\"])\n", "\n", "\n", "df1 = json_nor\n", "df1 = df1.loc[:, df1.columns.isin(['jia_yi_fang_a', 'petrol_id'])]\n", "df1 = df1.rename(columns={\"jia_yi_fang_a\": 'jia_yi_id'})\n", "df1 = pd.DataFrame(df1)\n", "# display('df1', df1)\n", "try: \n", "    url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'\n", "    to_dict = df1.to_dict('records')\n", "    body_raw = {\"data_api\": to_dict}\n", "    df13 = requests.get(url=url, json=body_raw).json()\n", "    df13 = pd.DataFrame(df13)\n", "    # display('df13', df13)\n", "except:\n", "    d = {'jia_yi_id': [0], 'jia_yi_idname': [\"\"], 'jia_yi_mm_name': [\"\"]}\n", "    df13 = pd.DataFrame(d)\n", "    # display('df13', df13)\n", "merge_store = pd.merge(df1, df13, on='jia_yi_id', how='left')\n", "# display('merge_store', merge_store)\n", "\n", "\n", "df2 = json_nor\n", "df2 = df2.loc[:, df2.columns.isin(['jia_yi_fang_b', 'petrol_id'])]\n", "df2 = df2.rename(columns={\"jia_yi_fang_b\": 'jia_yi_id'})\n", "df2 = pd.DataFrame(df2)\n", "# display('df2', df2)\n", "url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'\n", "# url = Arter_api + 'jia_yi_name_list_id'\n", "to_dict = df2.to_dict('records')\n", "body_raw = {\"data_api\": to_dict}\n", "df23 = requests.get(url=url, json=body_raw).json()\n", "df23 = pd.DataFrame(df23)\n", "# display('df23', df23)\n", "merge_car = pd.merge(df2, df23, on='jia_yi_id', how='left')\n", "# display('merge_car', merge_car)\n", "\n", "\n", "df3 = json_nor\n", "df3 = df3.loc[:, df3.columns.isin(['product_id', 'petrol_id'])]\n", "df3 = pd.DataFrame(df3)\n", "# display('df3', df3)\n", "url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/product_list_id'\n", "# url = Arter_api + 'product_list_id'\n", "to_dict = df3.to_dict('records')\n", "body_raw = {\"data_api\": to_dict}\n", "df33 = requests.get(url=url, json=body_raw).json()\n", "df33 = pd.DataFrame(df33)\n", "# display('df33', df33)\n", "merge_petrol = pd.merge(df3, df33, on='product_id', how='left')\n", "# display('merge_petrol', merge_petrol)\n", "\n", "\n", "df4 = json_nor\n", "df4 = df4.loc[:, df4.columns.isin(['bi_zhi', 'petrol_id'])]\n", "# display('df4', df4)\n", "df42 = pd.read_sql_table('bi_zhi', engine)\n", "df42 = df42.loc[:, ~df42.columns.isin(['auto_id'])]\n", "# # display('df42', df42)\n", "merge_bi_zhi = pd.merge(df4, df42, on='bi_zhi', how='left')\n", "# display('merge_bi_zhi', merge_bi_zhi)\n", "\n", "\n", "df5 = json_nor\n", "df5 = df5.loc[:, df5.columns.isin(['lei_a', 'ke_bian', 'petrol_id'])]\n", "df5 = df5.rename(columns={\"ke_bian\": 'jia_yi_id'})\n", "df5 = pd.DataFrame(df5)\n", "# display('df5', df5)\n", "df52 = pd.read_sql_table('lei_type', engine)\n", "df52 = df52.loc[:, ~df52.columns.isin(['auto_id', 'data_sub'])]\n", "df52 = pd.DataFrame(df52)\n", "# display('df52', df52)\n", "url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'\n", "# url = Arter_api + 'jia_yi_name_list_id'\n", "to_dict = df5.to_dict('records')\n", "body_raw = {\"data_api\": to_dict}\n", "df53 = requests.get(url=url, json=body_raw).json()\n", "df53 = [{\"jia_yi_id\": df53}]\n", "new_value = []\n", "for data in df53:\n", "    if data['jia_yi_id'] == False:\n", "        new_value.append(data)\n", "    else:\n", "        new_value.extend(data['jia_yi_id'])\n", "df53 = pd.DataFrame(new_value)\n", "# display('df53', df53)\n", "merge_lei_a_ke_bian = df5.merge(df52, on='lei_a', how='left').merge(df53, on='jia_yi_id', how='left')\n", "# display('merge_lei_a_ke_bian', merge_lei_a_ke_bian)\n", "\n", "\n", "data = json_nor\n", "data = data.loc[:, ~data.columns.isin(['jia_yi_fang_a', 'jia_yi_fang_b', 'product_id', 'bi_zhi', 'lei_a', 'lei_b', 'ke_bian'])]\n", "data = pd.DataFrame(data)\n", "# display('data', data)\n", "merge = [data, merge_store, merge_car, merge_petrol, merge_bi_zhi, merge_lei_a_ke_bian]\n", "merge = reduce(lambda left,right: pd.merge(left,right,on='petrol_id'), merge)\n", "merge = merge.rename(columns=dict(jia_yi_id_x='storeJia_yi_id', \n", "                                jia_yi_idname_x='storeJia_yi_idname_x',\n", "                                jia_yi_mm_name_x='storeJia_yi_mm_name_x',\n", "                                jia_yi_id_y='carJia_yi_id', \n", "                                jia_yi_idname_y='carJia_yi_idname_x',\n", "                                jia_yi_mm_name_y='car<PERSON>ia_yi_mm_name_x',\n", "                                product_id='petrolProduct_id',\n", "                                product_idname='petrolProduct_idname',\n", "                                product_mm_name='petrolProduct_mm_name', \n", "                                product_d_name='petrolProduct_d_name',\n", "                                th_name='petrolTh_name',\n", "                                idname='bi_zhiIdname',\n", "                                jia_yi_id='ke_bianJia_yi_id',\n", "                                jia_yi_idname='ke_bianJia_yi_idname',\n", "                                jia_yi_mm_name='ke_bian<PERSON>ia_yi_mm_name'\n", "                                ))\n", "merge[\"datetime\"] = merge['datetime'].dt.strftime(\"%Y-%m-%d\")\n", "if day == merge[\"datetime\"].all():\n", "    print(\"day\", day, merge[\"datetime\"])\n", "    merge[\"status_update\"] = True\n", "elif yesterday == merge[\"datetime\"].all():\n", "    print(\"yesterday\", yesterday, merge[\"datetime\"])\n", "    if day2 < day3:\n", "        print(\"less up\", day2, day3)\n", "        merge[\"status_update\"] = True\n", "    else:\n", "        print(\"more no\", day2, day3)\n", "        merge[\"status_update\"] = False    \n", "else:\n", "    print(\"else\")\n", "    merge[\"status_update\"] = False\n", "# Cal_id = pd.DataFrame(merge)\n", "# merge = merge.to_dict('records')\n", "# display('merge', merge)\n", "\n", "session = Session()\n", "today = datetime.date.today()\n", "future_day = today.day\n", "future_month = (today.month - 12) % 12\n", "future_year = today.year + ((today.month - 12) // 12)\n", "six_months_ago = datetime.date(future_year, future_month, future_day)\n", "\n", "df20 = pd.read_sql(sql = session.query(PetrolModel)\\\n", "                        .with_entities(\n", "                            PetrolModel.petrol_id,\n", "                            PetrolModel.datetime,\n", "                            PetrolModel.data_details[0]['che_liang_id'],\n", "                            PetrolModel.data_details[0]['distant'],\n", "                            PetrolModel.data_details[0]['qty'],\n", "                            )\n", "                            .filter(PetrolModel.datetime > six_months_ago).statement,\n", "                con = session.bind)\n", "df20 = df20.rename(columns={\n", "    \"anon_1\": 'che_liang_id',\n", "    \"anon_2\": 'distant',\n", "    \"anon_3\": 'qty',\n", "    })\n", "\n", "# display(\"df20\", df20)\n", "\n", "df30 = pd.read_sql(sql = session.query(PetrolModel2)\\\n", "                        .with_entities(\n", "                            PetrolModel2.petrol_id,\n", "                            PetrolModel2.datetime,\n", "                            PetrolModel2.data_details[0]['che_liang_id'],\n", "                            PetrolModel2.data_details[0]['distant'],\n", "                            PetrolModel2.data_details[0]['qty'],\n", "                            )\n", "                            .filter( PetrolModel2.datetime > six_months_ago).statement,\n", "                con = session.bind)\n", "df30 = df30.rename(columns={\n", "    \"anon_1\": 'che_liang_id',\n", "    \"anon_2\": 'distant',\n", "    \"anon_3\": 'qty',\n", "    })\n", "\n", "# display(\"df30\", df30)\n", "\n", "df40 = pd.concat([df20, df30], ignore_index=True)\n", "df40 = df40.sort_values(['datetime'], ascending=[True])\n", "# display(df40)\n", "\n", "Ca_id = merge.to_dict('records')\n", "df50 = df40[df40['che_liang_id'] == Ca_id[0]['che_liang_id']]\n", "df50['previous_distant'] = df50['distant'].shift()\n", "def fx(x):\n", "    if x['previous_distant'] == 0:\n", "        return x['previous_distant']\n", "    elif x['distant'] == 0:\n", "        return x['distant']\n", "    else:\n", "        return x['distant'] - x['previous_distant']\n", "df50['distance_used'] = df50.apply(lambda x : fx(x),axis=1)\n", "df50['previous_qty'] = df50['qty'].shift()\n", "\n", "df50['duplicated'] = df50['distance_used'].duplicated(keep=False)\n", "df50['count'] = df50['distance_used']\n", "\n", "df50['km_lit'] = (df50['distance_used'] / df50['previous_qty'])           \n", "df50['average'] = df50[df50['km_lit'] != 0][\"km_lit\"].mean()\n", "def fx2(x):\n", "    if x['km_lit'] == 0:\n", "        return x['km_lit']\n", "    else:\n", "        return (x.km_lit - x.average) / x.average * 100\n", "df50['average_percent'] = df50.apply(lambda x : fx2(x),axis=1).round(0)\n", "\n", "def remove_outliers(df50, q=0.05):\n", "    upper = df50.quantile(1-q)\n", "    lower = df50.quantile(q)\n", "    mask = (df50 < upper) & (df50 > lower)\n", "    return mask\n", "df50['outliers'] = remove_outliers(df50['km_lit'], 0.1)\n", "\n", "def fx3(x):\n", "    if x['outliers'] == False:\n", "        return None\n", "    else:\n", "        return x['distance_used'] / x['previous_qty']\n", "df50['km_lit_2'] = df50.apply(lambda x : fx3(x),axis=1)\n", "df50['average_2'] = df50[df50['km_lit_2'] != 0][\"km_lit_2\"].mean()\n", "def fx4(x):\n", "    if x['km_lit_2'] == None:\n", "        return x['km_lit_2']\n", "    else:\n", "        return (x.km_lit_2 - x.average_2) / x.average_2 * 100\n", "df50['average_percent_2'] = df50.apply(lambda x : fx4(x),axis=1)\n", "df50['mean'] = ((df50.average_2 - df50.average) / df50.average * 100).round(0)\n", "df50['average_mean'] = df50[\"average_percent\"].astype(str) + ' , ' + df50[\"mean\"].astype(str)\n", "\n", "\n", "df60 = df50.pivot_table(columns=['distance_used'], aggfunc='size')\n", "df60 = pd.DataFrame(df60)\n", "df60 = df60.rename(columns={0: 'countoo'}).reset_index()\n", "\n", "\n", "df50['count'] = df50[['count']].replace(to_replace =df60['distance_used'].to_list(), \n", "                    value =df60['countoo'].to_list())\n", "df50['show'] = df50[\"average_mean\"].astype(str) + ' | ' + df50[\"count\"].astype(str)\n", "df50 = df50.fillna(0)\n", "# df50 = df50.to_dict('records')\n", "    \n", "df70 = merge.merge(df50, on='petrol_id', how='inner')\n", "df70 = df70.rename(columns={\n", "    \"qty_x\": 'qty',\n", "    \"distant_x\": 'distant',\n", "    \"che_liang_id_x\": 'che_liang_id',\n", "    \"datetime_x\": 'datetime',\n", "    \"distant_x\": 'distant',\n", "})\n", "\n", "df70['average_percent'] = 55\n", "\n", "if df70['average_percent'].item() == 0:\n", "    df70[\"decide\"] = False\n", "elif df70['average_percent'].item() <= -20:\n", "    df70[\"decide\"] = False\n", "elif df70['average_percent'].item() >= 40:\n", "    df70[\"decide\"] = False\n", "elif df70['count'].item() >= 5:\n", "    df70[\"decide\"] = False\n", "else:   \n", "    df70[\"decide\"] = True\n", "\n", "# df70 = df70.to_dict('records')\n", "\n", "display(df70)\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import warnings\n", "warnings.simplefilter('ignore')\n", "\n", "import pandas as pd\n", "import sqlalchemy\n", "from sqlalchemy import create_engine, tuple_\n", "from sqlalchemy.pool import NullPool\n", "import numpy as np\n", "import json\n", "from sqlalchemy.ext.declarative import declarative_base\n", "from sqlalchemy import Column, Integer, String, Date, DateTime, ForeignKey, TEXT\n", "from sqlalchemy.sql.expression import table, text\n", "from sqlalchemy.dialects.postgresql.json import JSONB\n", "from sqlalchemy.orm import sessionmaker\n", "from sqlalchemy import and_, or_, not_\n", "from sqlalchemy import extract, cast\n", "from sqlalchemy import literal\n", "from datetime import date, datetime, timedelta\n", "import datetime\n", "import itertools\n", "from sklearn.ensemble import IsolationForest\n", "from sqlalchemy.orm.attributes import flag_modified\n", "import requests\n", "from functools import reduce\n", "\n", "\n", "DATABASE_URL = \"*************************************************/test_nern\"\n", "engine = create_engine(DATABASE_URL, poolclass=NullPool)\n", "# engine = create_engine(DATABASE_URL)\n", "Session = sessionmaker(bind=engine)\n", "\n", "Base = declarative_base()\n", "\n", "class PetrolModel(Base):\n", "    __tablename__ = 'petrol'\n", "    auto_id = Column(Integer, primary_key=True)\n", "    petrol_id = Column(TEXT(convert_unicode=True), server_default=text(\"'petrol'||lpad(NEXTVAL('petrol_sequence'::regclass)::text,8, '0')\"))\n", "    datetime = Column(DateTime)\n", "    data_details = Column(JSONB, nullable=False, server_default='[]')\n", "    status_details = Column(JSONB, nullable=False, server_default='{}')\n", "    update_time = Column(DateTime)\n", "    \n", "    \n", "class PetrolModel2(Base):\n", "    __tablename__ = 'petrol2'\n", "    auto_id = Column(Integer, primary_key=True)\n", "    petrol_id = Column(TEXT(convert_unicode=True), server_default=text(\"'petrol'||lpad(NEXTVAL('petrol_sequence'::regclass)::text,8, '0')\"))\n", "    datetime = Column(DateTime)\n", "    data_details = Column(JSONB, nullable=False, server_default='[]')\n", "    status_details = Column(JSONB, nullable=False, server_default='{}')    \n", "\n", "\n", "session = Session()\n", "\n", "\n", "\n", "get_date = \"2022-05-08\"\n", "try:\n", "    df20 = pd.read_sql(sql = session.query(PetrolModel).order_by(PetrolModel.datetime.desc())\n", "                        .filter(cast(PetrolModel.datetime, Date) == get_date)\n", "                        .with_entities(\n", "                                PetrolModel.data_details[0]['fen_dian_id'],\n", "                                PetrolModel.datetime,\n", "                                PetrolModel.petrol_id,\n", "                                PetrolModel.data_details[0]['che_liang_id'],\n", "                                PetrolModel.data_details[0]['distant'],\n", "                                PetrolModel.data_details[0]['bill'],\n", "                                PetrolModel.status_details['status'],\n", "                                )\n", "                        .statement, con = session.bind)\n", "    df20 = df20.rename(columns=dict(anon_1='fen_dian_id', anon_2='che_liang_id', anon_3='distant', anon_4='bill', anon_5='status'))\n", "    df21 = df20['petrol_id'].tolist()\n", "    display('df20', df20)\n", "    \n", "    df30 = pd.read_sql(sql = session.query(Calculate)\n", "                        .filter(Calculate.data_details[0]['petrol_id'].astext.cast(String).in_(df21))\n", "                        .with_entities(\n", "                                Calculate.data_details[0]['petrol_id'],\n", "                                Calculate.data_details[0]['average_percent'],\n", "                                Calculate.data_details[0]['mean'],\n", "                                Calculate.data_details[0]['count'],\n", "                                )\n", "                        .statement, con = session.bind)\n", "    df30 = df30.rename(columns=dict(anon_1='petrol_id', anon_2='average_percent', anon_3='mean', anon_4='count'))\n", "    display('df30', df30)\n", "\n", "    df40 = df20\n", "    df40 = df40.loc[:, df40.columns.isin(['che_liang_id', 'petrol_id', 'product_id'])]\n", "    df40 = df40.rename(columns={\"che_liang_id\": 'jia_yi_id'})\n", "    # display('df40', df40)\n", "\n", "    url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'\n", "    to_dict = df40.to_dict('records')\n", "    body_raw = {\"data_api\": to_dict}\n", "    df50 = requests.get(url=url, json=body_raw).json()\n", "    df50 = pd.DataFrame(df50)\n", "    display('df50', df50)\n", "    \n", "    merge = df20.merge(df50, left_on='che_liang_id', right_on='jia_yi_id', how='left').merge(df30, on='petrol_id', how='outer')\n", "    display('merge', merge)\n", "except:\n", "    merge = pd.DataFrame()\n", "    # merge = []    \n", "session.close()\n", "\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import warnings\n", "warnings.simplefilter('ignore')\n", "\n", "import pandas as pd\n", "import sqlalchemy\n", "from sqlalchemy import create_engine, tuple_\n", "from sqlalchemy.pool import NullPool\n", "import numpy as np\n", "import json\n", "from sqlalchemy.ext.declarative import declarative_base\n", "from sqlalchemy import Column, Integer, String, Date, DateTime, ForeignKey, TEXT\n", "from sqlalchemy.sql.expression import table, text\n", "from sqlalchemy.dialects.postgresql.json import JSONB\n", "from sqlalchemy.orm import sessionmaker\n", "from sqlalchemy import and_, or_, not_\n", "from sqlalchemy import extract, cast\n", "from sqlalchemy import literal\n", "from datetime import date, datetime, timedelta\n", "import datetime\n", "import itertools\n", "from sklearn.ensemble import IsolationForest\n", "from sqlalchemy.orm.attributes import flag_modified\n", "import requests\n", "from functools import reduce\n", "\n", "\n", "DATABASE_URL = \"*************************************************/n_data\"\n", "engine = create_engine(DATABASE_URL, poolclass=NullPool)\n", "Session = sessionmaker(bind=engine)\n", "\n", "Base = declarative_base()\n", "\n", "class Calculate(Base):\n", "    __tablename__ = 'petrol_calculate'\n", "    auto_id = Column(Integer, primary_key=True)\n", "    head = Column(JSONB, nullable=False, server_default='{}')\n", "    data_details = Column(JSONB, nullable=False, server_default='[]')\n", "    datetime = Column(DateTime)  \n", "\n", "\n", "session = Session()\n", "\n", "    \n", "df10 = pd.read_sql(sql = session.query(Calculate)\n", "                    .with_entities(\n", "                            Calculate.head,\n", "                            Calculate.head['petrol_id'],\n", "                            )\n", "                    .statement, con = session.bind)\n", "df10 = df10.rename(columns=dict(anon_1='petrol_id'))\n", "df10 = df10.to_dict('records')\n", "# display('df10', df10)\n", "\n", "\n", "# DATABASE_URL_2 = \"*************************************************/test_nern\"\n", "# engine2 = create_engine(DATABASE_URL_2, poolclass=NullPool)\n", "# Session2 = sessionmaker(bind=engine2)\n", "# Base = declarative_base()\n", "\n", "# class Calculate(Base):\n", "#     __tablename__ = 'petrol_calculate'\n", "#     auto_id = Column(Integer, primary_key=True)\n", "#     petrol_id = Column(TEXT)\n", "#     calOne = Column(JSONB, nullable=False, server_default='{}')\n", "#     calTwo = Column(JSONB, nullable=False, server_default='{}') \n", "\n", "# session2 = Session2()\n", "    \n", "\n", "# for i in df10:\n", "#     # print(i)\n", "#     session2.add_all([\n", "#         Calculate(\n", "#             petrol_id=i['petrol_id'],\n", "#             calTwo=i['head']\n", "#             )\n", "#         ]\n", "#     )\n", "        \n", "# session2.commit()\n", "# session2.close()\n", "\n", "\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import warnings\n", "warnings.simplefilter('ignore')\n", "\n", "import pandas as pd\n", "import sqlalchemy\n", "from sqlalchemy import create_engine, tuple_\n", "from sqlalchemy.pool import NullPool\n", "import numpy as np\n", "import json\n", "from sqlalchemy.ext.declarative import declarative_base\n", "from sqlalchemy import Column, Integer, String, Date, DateTime, ForeignKey, TEXT\n", "from sqlalchemy.sql.expression import table, text\n", "from sqlalchemy.dialects.postgresql.json import JSONB\n", "from sqlalchemy.orm import sessionmaker\n", "from sqlalchemy import and_, or_, not_\n", "from sqlalchemy import extract, cast\n", "from sqlalchemy import literal\n", "from datetime import date, datetime, timedelta\n", "import datetime\n", "import itertools\n", "from sklearn.ensemble import IsolationForest\n", "from sqlalchemy.orm.attributes import flag_modified\n", "import requests\n", "from functools import reduce\n", "\n", "\n", "DATABASE_URL = \"*************************************************/n_data\"\n", "engine = create_engine(DATABASE_URL, poolclass=NullPool)\n", "Session = sessionmaker(bind=engine)\n", "\n", "Base = declarative_base()\n", "\n", "class Calculate(Base):\n", "    __tablename__ = 'petrol_calculate'\n", "    auto_id = Column(Integer, primary_key=True)\n", "    petrol_id = Column(TEXT)\n", "    calOne = Column(JSONB, nullable=False, server_default='{}')\n", "    calTwo = Column(JSONB, nullable=False, server_default='{}')\n", "\n", "\n", "session = Session()\n", "\n", "    \n", "df10 = pd.read_sql(sql = session.query(Calculate)\n", "                    .with_entities(\n", "                            Calculate.calTwo['che_liang_id'],\n", "                            Calculate.calTwo['datetime'],\n", "                            Calculate.calTwo['oneKmLit'],\n", "                            Calculate.calTwo['minute'],\n", "                            )\n", "                    .filter(Calculate.calTwo['che_liang_id'].astext.cast(Integer) == 25371)\n", "                    .statement, con = session.bind)\n", "df10 = df10.rename(columns=dict(anon_1='che_liang_id', anon_2='datetime', anon_3='oneKmLit', anon_4='minute'))\n", "df10 = df10.to_dict('records')\n", "display('df10', df10)\n", "\n", "\n", "\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import warnings\n", "warnings.simplefilter('ignore')\n", "\n", "import pandas as pd\n", "import sqlalchemy\n", "from sqlalchemy import create_engine, tuple_\n", "from sqlalchemy.pool import NullPool\n", "import numpy as np\n", "import json\n", "from sqlalchemy.ext.declarative import declarative_base\n", "from sqlalchemy import Column, Integer, String, Date, DateTime, ForeignKey, TEXT\n", "from sqlalchemy.sql.expression import table, text\n", "from sqlalchemy.dialects.postgresql.json import JSONB\n", "from sqlalchemy.orm import sessionmaker\n", "from sqlalchemy import and_, or_, not_\n", "from sqlalchemy import literal\n", "import datetime\n", "import itertools\n", "from sklearn.ensemble import IsolationForest\n", "\n", "DATABASE_URL = \"*************************************************/n_data\"\n", "engine = create_engine(DATABASE_URL, poolclass=NullPool)\n", "Session = sessionmaker(bind=engine)\n", "\n", "Base = declarative_base()\n", "\n", "class PetrolModel(Base):\n", "    __tablename__ = 'petrol'\n", "    auto_id = Column(Integer, primary_key=True)\n", "    petrol_id = Column(TEXT(convert_unicode=True), server_default=text(\"'petrol'||lpad(NEXTVAL('petrol_sequence'::regclass)::text,8, '0')\"))\n", "    datetime = Column(DateTime)\n", "    data_details = Column(JSONB, nullable=False, server_default='[]')\n", "    \n", "    \n", "class PetrolModel2(Base):\n", "    __tablename__ = 'petrol2'\n", "    auto_id = Column(Integer, primary_key=True)\n", "    petrol_id = Column(TEXT(convert_unicode=True), server_default=text(\"'petrol'||lpad(NEXTVAL('petrol_sequence'::regclass)::text,8, '0')\"))\n", "    datetime = Column(DateTime)\n", "    data_details = Column(JSONB, nullable=False, server_default='[]')\n", "    status_details = Column(JSONB, nullable=False, server_default='{}')    \n", "\n", "\n", "session = Session()\n", "\n", "\n", "\n", "today = datetime.date.today()\n", "future_day = today.day\n", "future_month = (today.month - 2) % 12\n", "future_year = today.year + ((today.month - 2) // 12)\n", "six_months_ago = datetime.date(future_year, future_month, future_day)\n", "\n", "\n", "df20 = pd.read_sql(sql = session.query(PetrolModel)\\\n", "                        .with_entities(\n", "                            PetrolModel.petrol_id,\n", "                            PetrolModel.datetime,\n", "                            PetrolModel.data_details[0]['che_liang_id'],\n", "                            PetrolModel.data_details[0]['distant'],\n", "                            PetrolModel.data_details[0]['qty'],\n", "                            )\n", "                            .filter(PetrolModel.datetime > six_months_ago).statement,\n", "                con = session.bind)\n", "\n", "\n", "df20 = df20.rename(columns={\n", "    \"anon_1\": 'che_liang_id',\n", "    \"anon_2\": 'distant',\n", "    \"anon_3\": 'qty',\n", "    })\n", "\n", "# display(\"df20\", df20)\n", "\n", "\n", "df30 = pd.read_sql(sql = session.query(PetrolModel2)\\\n", "                        .with_entities(\n", "                            PetrolModel2.petrol_id,\n", "                            PetrolModel2.datetime,\n", "                            PetrolModel2.data_details[0]['che_liang_id'],\n", "                            PetrolModel2.data_details[0]['distant'],\n", "                            PetrolModel2.data_details[0]['qty'],\n", "                            )\n", "                            .filter( PetrolModel2.datetime > six_months_ago).statement,\n", "                con = session.bind)\n", "\n", "\n", "df30 = df30.rename(columns={\n", "    \"anon_1\": 'che_liang_id',\n", "    \"anon_2\": 'distant',\n", "    \"anon_3\": 'qty',\n", "    })\n", "\n", "# display(\"df30\", df30)\n", "\n", "\n", "df40 = pd.concat([df20, df30], ignore_index=True)\n", "df40 = df40.sort_values(['datetime'], ascending=[True])\n", "# display(df40)\n", "\n", "\n", "df50 = df40[df40['che_liang_id'] ==  22046 ]\n", "df50['previous_distant'] = df50['distant'].shift()\n", "def fx(x):\n", "    if x['previous_distant'] == 0:\n", "        return x['previous_distant']\n", "    elif x['distant'] == 0:\n", "        return x['distant']\n", "    else:\n", "        return x['distant'] - x['previous_distant']\n", "df50['distance_used'] = df50.apply(lambda x : fx(x),axis=1)\n", "df50['previous_qty'] = df50['qty'].shift()\n", "df50['duplicated'] = df50['distance_used'].duplicated(keep=False)\n", "df50['count'] = df50['distance_used']\n", "df50['km_lit'] = (df50['distance_used'] / df50['previous_qty'])         \n", "df50['average'] = df50[df50['km_lit'] != 0][\"km_lit\"].mean()\n", "def fx2(x):\n", "    if x['km_lit'] == 0:\n", "        return x['km_lit']\n", "    else:\n", "        return (x.km_lit - x.average) / x.average * 100\n", "df50['average_percent'] = df50.apply(lambda x : fx2(x),axis=1)\n", "def remove_outliers(df50, q=0.05):\n", "    upper = df50.quantile(1-q)\n", "    lower = df50.quantile(q)\n", "    mask = (df50 < upper) & (df50 > lower)\n", "    return mask\n", "df50['outliers'] = remove_outliers(df50['km_lit'], 0.1)\n", "def fx3(x):\n", "    if x['outliers'] == False:\n", "        return None\n", "    else:\n", "        return x['distance_used'] / x['previous_qty']\n", "df50['km_lit_2'] = df50.apply(lambda x : fx3(x),axis=1)\n", "df50['average_2'] = df50[df50['km_lit_2'] != 0][\"km_lit_2\"].mean()\n", "def fx4(x):\n", "    if x['km_lit_2'] == None:\n", "        return x['km_lit_2']\n", "    else:\n", "        return (x.km_lit_2 - x.average_2) / x.average_2 * 100\n", "df50['average_percent_2'] = df50.apply(lambda x : fx4(x),axis=1)\n", "df50['mean'] = ((df50.average_2 - df50.average) / df50.average * 100)\n", "df50['average_mean'] = df50[\"average_percent\"].astype(str) + ' , ' + df50[\"mean\"].astype(str)\n", "df60 = df50.pivot_table(columns=['distance_used'], aggfunc='size')\n", "df60 = pd.DataFrame(df60)\n", "df60 = df60.rename(columns={0: 'countoo'}).reset_index()\n", "df50['count'] = df50[['count']].replace(to_replace =df60['distance_used'].to_list(), \n", "                    value =df60['countoo'].to_list())\n", "df50['show'] = df50[\"average_mean\"].astype(str) + ' | ' + df50[\"count\"].astype(str)\n", "df50['oneKmLit'] = 1 / df50['average']\n", "df50['hundredKmLit'] = 100 / df50['average']\n", "df50['minute'] = 60 * df50['average']\n", "df50['kmLitStd'] = df50['km_lit'].std()\n", "df50['kmLitMin'] = df50['km_lit'].min()\n", "df50['kmLit25Per'] = df50['km_lit'].quantile(0.25)\n", "df50['kmLit50Per'] = df50['km_lit'].quantile(0.5)\n", "df50['kmLit75Per'] = df50['km_lit'].quantile(0.75)\n", "df50['kmLitMax'] = df50['km_lit'].max()\n", "df50['countDuplicate'] = df50['count'].max()\n", "df50['che_liang_id_Count'] = df50['che_liang_id'].count()\n", "df50 = df50.fillna(0).round(decimals = 1)\n", "display(df50, \"Each Car\")\n", "\n", "\n", "\n", "df60 = df50[[\"petrol_id\", \"datetime\", \"che_liang_id\", \"mean\", \"minute\", \"average\",\n", "             \"kmLitMax\", \"kmLitMin\", \"kmLitStd\", \"oneKmLit\", \"average_2\", \"kmLit25Per\",\n", "             \"kmLit50Per\", \"kmLit75Per\", \"hundredKmLit\", \"countDuplicate\", \"che_liang_id_Count\"]]\n", "display(df60, \"Keep in variable\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import warnings\n", "warnings.simplefilter('ignore')\n", "\n", "import pandas as pd\n", "import sqlalchemy\n", "from sqlalchemy import create_engine, tuple_\n", "from sqlalchemy.pool import NullPool\n", "import numpy as np\n", "import json\n", "from sqlalchemy.ext.declarative import declarative_base\n", "from sqlalchemy import Column, Integer, String, Date, DateTime, ForeignKey, TEXT\n", "from sqlalchemy.sql.expression import table, text\n", "from sqlalchemy.dialects.postgresql.json import JSONB\n", "from sqlalchemy.orm import sessionmaker\n", "from sqlalchemy import and_, or_, not_\n", "from sqlalchemy import literal\n", "import datetime\n", "import itertools\n", "from sklearn.ensemble import IsolationForest\n", "\n", "DATABASE_URL = \"*************************************************/n_data\"\n", "engine = create_engine(DATABASE_URL, poolclass=NullPool)\n", "Session = sessionmaker(bind=engine)\n", "\n", "Base = declarative_base()\n", "\n", "class PetrolModel(Base):\n", "    __tablename__ = 'petrol'\n", "    auto_id = Column(Integer, primary_key=True)\n", "    petrol_id = Column(TEXT(convert_unicode=True), server_default=text(\"'petrol'||lpad(NEXTVAL('petrol_sequence'::regclass)::text,8, '0')\"))\n", "    datetime = Column(DateTime)\n", "    data_details = Column(JSONB, nullable=False, server_default='[]')\n", "    \n", "    \n", "class PetrolModel2(Base):\n", "    __tablename__ = 'petrol2'\n", "    auto_id = Column(Integer, primary_key=True)\n", "    petrol_id = Column(TEXT(convert_unicode=True), server_default=text(\"'petrol'||lpad(NEXTVAL('petrol_sequence'::regclass)::text,8, '0')\"))\n", "    datetime = Column(DateTime)\n", "    data_details = Column(JSONB, nullable=False, server_default='[]')\n", "    status_details = Column(JSONB, nullable=False, server_default='{}')    \n", "\n", "\n", "session = Session()\n", "\n", "\n", "\n", "today = datetime.date.today()\n", "future_day = today.day\n", "future_month = (today.month - 1) % 12\n", "future_year = today.year + ((today.month - 1) // 12)\n", "six_months_ago = datetime.date(future_year, future_month, future_day)\n", "\n", "\n", "df20 = pd.read_sql(sql = session.query(PetrolModel)\\\n", "                        .with_entities(\n", "                            PetrolModel.petrol_id,\n", "                            PetrolModel.datetime,\n", "                            PetrolModel.data_details[0]['che_liang_id'],\n", "                            PetrolModel.data_details[0]['distant'],\n", "                            PetrolModel.data_details[0]['qty'],\n", "                            PetrolModel.data_details[0]['bill'],\n", "                            )\n", "                            .filter(PetrolModel.datetime > six_months_ago).statement,\n", "                con = session.bind)\n", "\n", "\n", "df20 = df20.rename(columns={\n", "    \"anon_1\": 'che_liang_id',\n", "    \"anon_2\": 'distant',\n", "    \"anon_3\": 'qty',\n", "    \"anon_4\": 'bill',\n", "    })\n", "\n", "# display(\"df20\", df20)\n", "\n", "\n", "df30 = pd.read_sql(sql = session.query(PetrolModel2)\\\n", "                        .with_entities(\n", "                            PetrolModel2.petrol_id,\n", "                            PetrolModel2.datetime,\n", "                            PetrolModel2.data_details[0]['che_liang_id'],\n", "                            PetrolModel2.data_details[0]['distant'],\n", "                            PetrolModel2.data_details[0]['qty'],\n", "                            )\n", "                            .filter( PetrolModel2.datetime > six_months_ago).statement,\n", "                con = session.bind)\n", "\n", "\n", "df30 = df30.rename(columns={\n", "    \"anon_1\": 'che_liang_id',\n", "    \"anon_2\": 'distant',\n", "    \"anon_3\": 'qty',\n", "    })\n", "\n", "# display(\"df30\", df30)\n", "\n", "df40 = pd.concat([df20, df30], ignore_index=True)\n", "df40 = df40.sort_values(['datetime'], ascending=[True])\n", "display(df40)\n", "\n", "df40 = pd.concat([df20, df30], ignore_index=True)\n", "groups = {key: df40.loc[value] for key, value in df40.groupby(\"che_liang_id\").groups.items()}\n", "for key, group in groups.items():\n", "    group['previous_distant'] = group['distant'].shift()\n", "    group = group.tail(1)\n", "    print(group.to_dict('records'))\n", "    # print(group)\n", "\n", "\n", "# df40 = pd.concat([df20, df30], ignore_index=True)\n", "# df40 = df40.sort_values(['datetime'], ascending=[True])\n", "# dh = group['che_liang_id'].apply(lambda x: pd.Series(seriesFeatures(x)))\n", "# df40\n", "\n", "# def fx(x):\n", "#     if x['previous_distant'] == 0:\n", "#         return x['previous_distant']\n", "#     elif x['distant'] == 0:\n", "#         return x['distant']\n", "#     else:\n", "#         return x['distant'] - x['previous_distant']\n", "# df50['distance_used'] = df50.apply(lambda x : fx(x),axis=1)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Let's say this is my dataframe.\n", "d = {'ids': [100, 200, 100, 200, 200, 100], 'datetime': ['2022-05-11', '2022-05-11', '2022-05-12', '2022-05-12', '2022-05-13', '2022-05-13'], 'col': [1, 2, 3, 4, 5, 6], 'col2': [6, 5, 4, 3, 2, 1]}\n", "df = pd.DataFrame(data=d)\n", "df = df.sort_values(['ids'], ascending=[True])\n", "display(df)\n", "\n", "# I like to \n", "d01 = {'ids': [100, 100, 100, 200, 200, 200], 'datetime': ['2022-05-11', '2022-05-12', '2022-05-13', '2022-05-11', '2022-05-12', '2022-05-13'], 'col': [1, 3, 6, 2, 4, 5], 'previous_col': [0, 1, 3, 0, 2, 4], 'multiply': [0, 3, 18, 0, 8, 20]}\n", "df30 = pd.DataFrame(data=d01)\n", "display(df30)\n", "\n", "\n", "d02 = {'ids': [100, 200], 'datetime': ['2022-05-13', '2022-05-13'], 'col': [6, 5], 'previous_col': [3, 4], 'multiply': [18, 20]}\n", "df40 = pd.DataFrame(data=d02)\n", "display(df40)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import warnings\n", "warnings.simplefilter('ignore')\n", "\n", "import pandas as pd\n", "import sqlalchemy\n", "from sqlalchemy import create_engine, tuple_\n", "from sqlalchemy.pool import NullPool\n", "import numpy as np\n", "import json\n", "from sqlalchemy.ext.declarative import declarative_base\n", "from sqlalchemy import Column, Integer, String, Date, DateTime, ForeignKey, TEXT\n", "from sqlalchemy.sql.expression import table, text\n", "from sqlalchemy.dialects.postgresql.json import JSONB\n", "from sqlalchemy.orm import sessionmaker\n", "from sqlalchemy import and_, or_, not_\n", "from sqlalchemy import literal\n", "import datetime\n", "import itertools\n", "from sklearn.ensemble import IsolationForest\n", "\n", "DATABASE_URL = \"*************************************************/n_data\"\n", "engine = create_engine(DATABASE_URL, poolclass=NullPool)\n", "Session = sessionmaker(bind=engine)\n", "\n", "Base = declarative_base()\n", "\n", "class PetrolModel(Base):\n", "    __tablename__ = 'petrol'\n", "    auto_id = Column(Integer, primary_key=True)\n", "    petrol_id = Column(TEXT(convert_unicode=True), server_default=text(\"'petrol'||lpad(NEXTVAL('petrol_sequence'::regclass)::text,8, '0')\"))\n", "    datetime = Column(DateTime)\n", "    data_details = Column(JSONB, nullable=False, server_default='[]')\n", "    \n", "    \n", "class PetrolModel2(Base):\n", "    __tablename__ = 'petrol2'\n", "    auto_id = Column(Integer, primary_key=True)\n", "    petrol_id = Column(TEXT(convert_unicode=True), server_default=text(\"'petrol'||lpad(NEXTVAL('petrol_sequence'::regclass)::text,8, '0')\"))\n", "    datetime = Column(DateTime)\n", "    data_details = Column(JSONB, nullable=False, server_default='[]')\n", "    status_details = Column(JSONB, nullable=False, server_default='{}')    \n", "\n", "\n", "session = Session()\n", "\n", "\n", "\n", "today = datetime.date.today()\n", "future_day = today.day\n", "future_month = (today.month - 12) % 12\n", "future_year = today.year + ((today.month - 12) // 12)\n", "six_months_ago = datetime.date(future_year, future_month, future_day)\n", "\n", "\n", "df20 = pd.read_sql(sql = session.query(PetrolModel)\\\n", "                        .with_entities(\n", "                            PetrolModel.petrol_id,\n", "                            PetrolModel.datetime,\n", "                            PetrolModel.data_details[0]['che_liang_id'],\n", "                            PetrolModel.data_details[0]['distant'],\n", "                            PetrolModel.data_details[0]['qty'],\n", "                            PetrolModel.data_details[0]['bill'],\n", "                            )\n", "                            .filter(PetrolModel.datetime > six_months_ago).statement,\n", "                con = session.bind)\n", "\n", "\n", "df20 = df20.rename(columns={\n", "    \"anon_1\": 'che_liang_id',\n", "    \"anon_2\": 'distant',\n", "    \"anon_3\": 'qty',\n", "    \"anon_4\": 'bill',\n", "    })\n", "\n", "# display(\"df20\", df20)\n", "\n", "\n", "df30 = pd.read_sql(sql = session.query(PetrolModel2)\\\n", "                        .with_entities(\n", "                            PetrolModel2.petrol_id,\n", "                            PetrolModel2.datetime,\n", "                            PetrolModel2.data_details[0]['che_liang_id'],\n", "                            PetrolModel2.data_details[0]['distant'],\n", "                            PetrolModel2.data_details[0]['qty'],\n", "                            )\n", "                            .filter( PetrolModel2.datetime > six_months_ago).statement,\n", "                con = session.bind)\n", "\n", "\n", "df30 = df30.rename(columns={\n", "    \"anon_1\": 'che_liang_id',\n", "    \"anon_2\": 'distant',\n", "    \"anon_3\": 'qty',\n", "    })\n", "\n", "# display(\"df30\", df30)\n", "\n", "df40 = pd.concat([df20, df30], ignore_index=True)\n", "# df40 = df40.head(60)\n", "df40 = df40.sort_values(['che_liang_id', 'datetime'], ascending=[True, True])\n", "# return che_liang_id values 23021, 194\n", "# df40 = df40[df40['che_liang_id'].isin([23021, 25371])]\n", "# return che_liang_id values == 194\n", "df40 = df40[df40['che_liang_id'] == 25371]\n", "    \n", "# get previous distant into new column call previous_distant and null value set to 0\n", "def fx(x):\n", "    if x['distant'].any() == 0:\n", "        return x['distant']\n", "    else:\n", "        return x['distant'].shift()\n", "df40['previous_distant'] = df40.groupby('che_liang_id').apply(lambda x : fx(x)).reset_index('che_liang_id',drop=True)\n", "\n", "def fx(x):\n", "    if x['distant'] == 0:\n", "        return x['distant']\n", "    elif x['distant'] == 0:\n", "        return x['distant']\n", "    elif x['previous_distant'] == 0:\n", "        return x['previous_distant']\n", "    else:\n", "        return x['distant'] - x['previous_distant']\n", "df40['distance_used'] = df40.apply(lambda x : fx(x),axis=1)\n", "# # get previous qty into new column call previous_qty\n", "# df40['previous_qty'] = df40.groupby('che_liang_id')['qty'].shift(1)\n", "# # calculate km_lit between distant_used and previous_qty\n", "# df40['km_lit'] = df40.apply(lambda x : x['distance_used'] / x['previous_qty'],axis=1)\n", "# # calculate to pandas mean of column km_lit into new column call average and can't be zero\n", "# df40['average'] = df40.groupby('che_liang_id')['km_lit'].transform(lambda x : x.mean())\n", "# # Calculate the percentage difference between the km_lit and average columns\n", "# df40['average_percent'] = df40.apply(lambda x : x['average'] and (x['km_lit'] - x['average']) / x['average'] * 100 ,axis=1)\n", "# def remove_outliers(aaa=df40, q=0.05):\n", "#     upper = aaa.quantile(1-q)\n", "#     lower = aaa.quantile(q)\n", "#     mask = (aaa < upper) & (aaa > lower)\n", "#     return mask\n", "# df40['outliers'] = remove_outliers(df40['km_lit'], 0.1)\n", "# # df40['outliers'] = df40.groupby('che_liang_id').apply(lambda x : remove_outliers(x['km_lit'], 0.1)).reset_index('che_liang_id',drop=True)\n", "# def fx3(x):\n", "#     if x['outliers'] == False:\n", "#         return None\n", "#     else:\n", "#         return x['distance_used'] / x['previous_qty']\n", "# df40['km_lit_2'] = df40.apply(lambda x : fx3(x),axis=1)\n", "# df40['average_2'] = df40.groupby('che_liang_id')['km_lit_2'].transform(lambda x : x.mean())\n", "# def fx4(x):\n", "#     if x['km_lit_2'] == None:\n", "#         return x['km_lit_2']\n", "#     else:\n", "#         return (x.km_lit_2 - x.average_2) / x.average_2 * 100\n", "# df40['average_percent_2'] = df40.apply(lambda x : fx4(x),axis=1)\n", "# df40['mean'] = ((df40.average_2 - df40.average) / df40.average * 100)\n", "# df40['oneKmLit'] = 1 / df40['average']\n", "# df40['hundredKmLit'] = 100 / df40['average']\n", "# df40['minute'] = 60 * df40['average']\n", "# # find km_lit standard deviation\n", "# df40['km_lit_std'] = df40.groupby('che_liang_id')['km_lit'].transform(lambda x : x.std())\n", "# df40['kmLitMin'] = df40.groupby('che_liang_id')['km_lit'].transform(lambda x : x.min())\n", "# # find 25% quantile of km_lit\n", "# df40['km_lit_25'] = df40.groupby('che_liang_id')['km_lit'].transform(lambda x : x.quantile(0.25))\n", "# # find 50% quantile of km_lit\n", "# df40['km_lit_50'] = df40.groupby('che_liang_id')['km_lit'].transform(lambda x : x.quantile(0.5))\n", "# # find 75% quantile of km_lit\n", "# df40['km_lit_75'] = df40.groupby('che_liang_id')['km_lit'].transform(lambda x : x.quantile(0.75))\n", "# # find max km_lit\n", "# df40['km_lit_max'] = df40.groupby('che_liang_id')['km_lit'].transform(lambda x : x.max())\n", "\n", "# df40['count'] = df40['distance_used']\n", "# df60 = df40.pivot_table(columns=['distance_used'], aggfunc='size')\n", "# df60 = pd.DataFrame(df60)\n", "# df60 = df60.rename(columns={0: 'countoo'}).reset_index()\n", "# df40['count'] = df40[['count']].replace(to_replace =df60['distance_used'].to_list(), \n", "#                         value =df60['countoo'].to_list())\n", "\n", "\n", "display(df40)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import warnings\n", "warnings.simplefilter('ignore')\n", "\n", "import pandas as pd\n", "import sqlalchemy\n", "from sqlalchemy import create_engine, tuple_\n", "from sqlalchemy.pool import NullPool\n", "import numpy as np\n", "import json\n", "from sqlalchemy.ext.declarative import declarative_base\n", "from sqlalchemy import Column, Integer, String, Date, DateTime, ForeignKey, TEXT\n", "from sqlalchemy.sql.expression import table, text\n", "from sqlalchemy.dialects.postgresql.json import JSONB\n", "from sqlalchemy.orm import sessionmaker\n", "from sqlalchemy import and_, or_, not_\n", "from sqlalchemy import literal\n", "import datetime\n", "import itertools\n", "from sklearn.ensemble import IsolationForest\n", "\n", "DATABASE_URL = \"*************************************************/n_data\"\n", "engine = create_engine(DATABASE_URL, poolclass=NullPool)\n", "Session = sessionmaker(bind=engine)\n", "\n", "Base = declarative_base()\n", "\n", "class PetrolModel(Base):\n", "    __tablename__ = 'petrol'\n", "    auto_id = Column(Integer, primary_key=True)\n", "    petrol_id = Column(TEXT(convert_unicode=True), server_default=text(\"'petrol'||lpad(NEXTVAL('petrol_sequence'::regclass)::text,8, '0')\"))\n", "    datetime = Column(DateTime)\n", "    data_details = Column(JSONB, nullable=False, server_default='[]')\n", "    \n", "    \n", "class PetrolModel2(Base):\n", "    __tablename__ = 'petrol2'\n", "    auto_id = Column(Integer, primary_key=True)\n", "    petrol_id = Column(TEXT(convert_unicode=True), server_default=text(\"'petrol'||lpad(NEXTVAL('petrol_sequence'::regclass)::text,8, '0')\"))\n", "    datetime = Column(DateTime)\n", "    data_details = Column(JSONB, nullable=False, server_default='[]')\n", "    status_details = Column(JSONB, nullable=False, server_default='{}')    \n", "\n", "\n", "session = Session()\n", "\n", "\n", "\n", "today = datetime.date.today()\n", "future_day = today.day\n", "future_month = (today.month - 1) % 12\n", "future_year = today.year + ((today.month - 1) // 12)\n", "six_months_ago = datetime.date(future_year, future_month, future_day)\n", "\n", "\n", "df20 = pd.read_sql(sql = session.query(PetrolModel)\\\n", "                        .with_entities(\n", "                            PetrolModel.petrol_id,\n", "                            PetrolModel.datetime,\n", "                            PetrolModel.data_details[0]['che_liang_id'],\n", "                            PetrolModel.data_details[0]['distant'],\n", "                            PetrolModel.data_details[0]['qty'],\n", "                            PetrolModel.data_details[0]['bill'],\n", "                            )\n", "                            .filter(PetrolModel.datetime > six_months_ago).statement,\n", "                con = session.bind)\n", "\n", "\n", "df20 = df20.rename(columns={\n", "    \"anon_1\": 'che_liang_id',\n", "    \"anon_2\": 'distant',\n", "    \"anon_3\": 'qty',\n", "    \"anon_4\": 'bill',\n", "    })\n", "\n", "# display(\"df20\", df20)\n", "\n", "\n", "df30 = pd.read_sql(sql = session.query(PetrolModel2)\\\n", "                        .with_entities(\n", "                            PetrolModel2.petrol_id,\n", "                            PetrolModel2.datetime,\n", "                            PetrolModel2.data_details[0]['che_liang_id'],\n", "                            PetrolModel2.data_details[0]['distant'],\n", "                            PetrolModel2.data_details[0]['qty'],\n", "                            )\n", "                            .filter( PetrolModel2.datetime > six_months_ago).statement,\n", "                con = session.bind)\n", "\n", "\n", "df30 = df30.rename(columns={\n", "    \"anon_1\": 'che_liang_id',\n", "    \"anon_2\": 'distant',\n", "    \"anon_3\": 'qty',\n", "    })\n", "\n", "# display(\"df30\", df30)\n", "\n", "# df40 = pd.concat([df20, df30], ignore_index=True)\n", "# df40 = df40.sort_values(['datetime'], ascending=[True])\n", "# display(df40)\n", "\n", "df40 = pd.concat([df20, df30], ignore_index=True)\n", "groups = {key: df40.loc[value] for key, value in df40.groupby(\"che_liang_id\").groups.items()}\n", "for key, group in groups.items():\n", "    group['previous_distant'] = group['distant'].shift()\n", "    group = group.tail(1)\n", "    print(group.to_dict('records'))\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "\n", "d = {'ids': [100, 200, 100, 200, 200, 100, 300, 300], 'col': [1, 2, 3, 4, 5, 6, 7, 8], 'col2': [6, 5, 4, 3, 2, 1, 10, 15]}\n", "df = pd.DataFrame(data=d)\n", "display(df)\n", "\n", "def func(group):\n", "    return group['col'].shift()\n", "df['previous_col'] = df.groupby('ids').apply(func).reset_index('ids',drop=True)\n", "df = df.sort_values(by=['ids'])\n", "df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import warnings\n", "warnings.simplefilter('ignore')\n", "\n", "import pandas as pd\n", "import sqlalchemy\n", "from sqlalchemy import create_engine, tuple_\n", "from sqlalchemy.pool import NullPool\n", "import numpy as np\n", "import json\n", "from sqlalchemy.ext.declarative import declarative_base\n", "from sqlalchemy import Column, Integer, String, Date, DateTime, ForeignKey, TEXT\n", "from sqlalchemy.sql.expression import table, text\n", "from sqlalchemy.dialects.postgresql.json import JSONB\n", "from sqlalchemy.orm import sessionmaker\n", "from sqlalchemy import and_, or_, not_\n", "from sqlalchemy import literal\n", "import datetime\n", "import itertools\n", "from sklearn.ensemble import IsolationForest\n", "\n", "DATABASE_URL = \"*************************************************/n_data\"\n", "engine = create_engine(DATABASE_URL, poolclass=NullPool)\n", "Session = sessionmaker(bind=engine)\n", "\n", "Base = declarative_base()\n", "\n", "class PetrolModel(Base):\n", "    __tablename__ = 'petrol'\n", "    auto_id = Column(Integer, primary_key=True)\n", "    petrol_id = Column(TEXT(convert_unicode=True), server_default=text(\"'petrol'||lpad(NEXTVAL('petrol_sequence'::regclass)::text,8, '0')\"))\n", "    datetime = Column(DateTime)\n", "    data_details = Column(JSONB, nullable=False, server_default='[]')\n", "    \n", "    \n", "class PetrolModel2(Base):\n", "    __tablename__ = 'petrol2'\n", "    auto_id = Column(Integer, primary_key=True)\n", "    petrol_id = Column(TEXT(convert_unicode=True), server_default=text(\"'petrol'||lpad(NEXTVAL('petrol_sequence'::regclass)::text,8, '0')\"))\n", "    datetime = Column(DateTime)\n", "    data_details = Column(JSONB, nullable=False, server_default='[]')\n", "    status_details = Column(JSONB, nullable=False, server_default='{}')    \n", "\n", "\n", "session = Session()\n", "\n", "\n", "\n", "today = datetime.date.today()\n", "future_day = today.day\n", "future_month = (today.month - 12) % 12\n", "future_year = today.year + ((today.month - 12) // 12)\n", "six_months_ago = datetime.date(future_year, future_month, future_day)\n", "\n", "\n", "df20 = pd.read_sql(sql = session.query(PetrolModel)\\\n", "                        .with_entities(\n", "                            PetrolModel.petrol_id,\n", "                            PetrolModel.datetime,\n", "                            PetrolModel.data_details[0]['che_liang_id'],\n", "                            PetrolModel.data_details[0]['distant'],\n", "                            PetrolModel.data_details[0]['qty'],\n", "                            PetrolModel.data_details[0]['bill'],\n", "                            )\n", "                            .filter(PetrolModel.datetime > six_months_ago).statement,\n", "                con = session.bind)\n", "\n", "\n", "df20 = df20.rename(columns={\n", "    \"anon_1\": 'che_liang_id',\n", "    \"anon_2\": 'distant',\n", "    \"anon_3\": 'qty',\n", "    \"anon_4\": 'bill',\n", "    })\n", "\n", "# display(\"df20\", df20)\n", "\n", "\n", "df30 = pd.read_sql(sql = session.query(PetrolModel2)\\\n", "                        .with_entities(\n", "                            PetrolModel2.petrol_id,\n", "                            PetrolModel2.datetime,\n", "                            PetrolModel2.data_details[0]['che_liang_id'],\n", "                            PetrolModel2.data_details[0]['distant'],\n", "                            PetrolModel2.data_details[0]['qty'],\n", "                            )\n", "                            .filter( PetrolModel2.datetime > six_months_ago).statement,\n", "                con = session.bind)\n", "\n", "\n", "df30 = df30.rename(columns={\n", "    \"anon_1\": 'che_liang_id',\n", "    \"anon_2\": 'distant',\n", "    \"anon_3\": 'qty',\n", "    })\n", "\n", "# display(\"df30\", df30)\n", "\n", "d = {'petrol_id': ['petroltest100', 'petroltest200', 'petroltest300'], \n", "     'datetime': ['3000-03-26 13:29:06.333246', '3000-03-26 13:29:06.333246', '3000-03-26 13:29:06.333246'], \n", "     'che_liang_id': [999999, 999999, 999999],\n", "     'distant': [410, 570, 700],\n", "     'qty': [20, 20, 20],\n", "     'bill': ['', '', '']\n", "    }\n", "df31 = pd.DataFrame(data=d)\n", "display(df31)\n", "\n", "df40 = pd.concat([df20, df30, df31], ignore_index=True)\n", "df40 = df40.head(60)\n", "df40 = df40.sort_values(['che_liang_id', 'datetime'], ascending=[True, True])\n", "# add two records to the last of df40\n", "    \n", "# return che_liang_id values 23021, 194\n", "# df40 = df40[df40['che_liang_id'].isin([23021, 25371])]\n", "# return che_liang_id values == 194\n", "# df40 = df40[df40['che_liang_id'] == 25371]\n", "    \n", "# get previous distant into new column call previous_distant and null value set to 0\n", "def fx(x):\n", "    if x['distant'].all() == 0:\n", "        return x['distant']\n", "    else:\n", "        return x['distant'].shift()\n", "df40['previous_distant'] = df40.groupby('che_liang_id').apply(lambda x : fx(x)).reset_index('che_liang_id',drop=True)\n", "def fx(x):\n", "    if x['distant'].all() == 0:\n", "        return x['distant']\n", "    elif x['previous_distant'].all() == 0:\n", "        return x['previous_distant']\n", "    else:\n", "        return x['distant'] - x['previous_distant']\n", "df40['distance_used'] = df40.groupby('che_liang_id').apply(lambda x : fx(x)).reset_index('che_liang_id',drop=True)\n", "# get previous qty into new column call previous_qty\n", "def fx(x):\n", "    if x['previous_distant'].all() == 0:\n", "        return x['previous_distant']\n", "    else:\n", "        return x['qty'].shift()\n", "df40['previous_qty'] = df40.groupby('che_liang_id').apply(lambda x : fx(x)).reset_index('che_liang_id',drop=True)\n", "# calculate km_lit between distant_used and previous_qty\n", "df40['km_lit'] = df40.groupby('che_liang_id').apply(lambda x : x['distance_used'] / x['previous_qty']).reset_index('che_liang_id',drop=True)\n", "# calculate to pandas mean of column km_lit into new column call average and can't be zero\n", "df40['average'] = df40.groupby('che_liang_id')['km_lit'].transform(lambda x : x.mean())\n", "# Calculate the percentage difference between the km_lit and average columns\n", "df40['average_percent'] = df40.apply(lambda x : x['average'] and (x['km_lit'] - x['average']) / x['average'] * 100 ,axis=1)\n", "# def remove_outliers(aaa=df40, q=0.05):\n", "#     upper = aaa.quantile(1-q)\n", "#     lower = aaa.quantile(q)\n", "#     mask = (aaa < upper) & (aaa > lower)\n", "#     return mask\n", "# df40['outliers'] = remove_outliers(df40['km_lit'], 0.1)\n", "# # df40['outliers'] = df40.groupby('che_liang_id').apply(lambda x : remove_outliers(x['km_lit'], 0.1)).reset_index('che_liang_id',drop=True)\n", "# def fx3(x):\n", "#     if x['outliers'] == False:\n", "#         return None\n", "#     else:\n", "#         return x['distance_used'] / x['previous_qty']\n", "# df40['km_lit_2'] = df40.apply(lambda x : fx3(x),axis=1)\n", "# df40['average_2'] = df40.groupby('che_liang_id')['km_lit_2'].transform(lambda x : x.mean())\n", "# def fx4(x):\n", "#     if x['km_lit_2'] == None:\n", "#         return x['km_lit_2']\n", "#     else:\n", "#         return (x.km_lit_2 - x.average_2) / x.average_2 * 100\n", "# df40['average_percent_2'] = df40.apply(lambda x : fx4(x),axis=1)\n", "# df40['mean'] = ((df40.average_2 - df40.average) / df40.average * 100)\n", "# df40['oneKmLit'] = 1 / df40['average']\n", "# df40['hundredKmLit'] = 100 / df40['average']\n", "# df40['minute'] = 60 * df40['average']\n", "# # find km_lit standard deviation\n", "# df40['km_lit_std'] = df40.groupby('che_liang_id')['km_lit'].transform(lambda x : x.std())\n", "# df40['kmLitMin'] = df40.groupby('che_liang_id')['km_lit'].transform(lambda x : x.min())\n", "# # find 25% quantile of km_lit\n", "# df40['km_lit_25'] = df40.groupby('che_liang_id')['km_lit'].transform(lambda x : x.quantile(0.25))\n", "# # find 50% quantile of km_lit\n", "# df40['km_lit_50'] = df40.groupby('che_liang_id')['km_lit'].transform(lambda x : x.quantile(0.5))\n", "# # find 75% quantile of km_lit\n", "# df40['km_lit_75'] = df40.groupby('che_liang_id')['km_lit'].transform(lambda x : x.quantile(0.75))\n", "# # find max km_lit\n", "# df40['km_lit_max'] = df40.groupby('che_liang_id')['km_lit'].transform(lambda x : x.max())\n", "\n", "# df40['count'] = df40['distance_used']\n", "# df60 = df40.pivot_table(columns=['distance_used'], aggfunc='size')\n", "# df60 = pd.DataFrame(df60)\n", "# df60 = df60.rename(columns={0: 'countoo'}).reset_index()\n", "# df40['count'] = df40[['count']].replace(to_replace =df60['distance_used'].to_list(), \n", "#                         value =df60['countoo'].to_list())\n", "\n", "# add new row into df40\n", "\n", "\n", "    \n", "display(df40)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import warnings\n", "warnings.simplefilter('ignore')\n", "\n", "import pandas as pd\n", "import sqlalchemy\n", "from sqlalchemy import create_engine, tuple_\n", "from sqlalchemy.pool import NullPool\n", "import numpy as np\n", "import json\n", "from sqlalchemy.ext.declarative import declarative_base\n", "from sqlalchemy import Column, Integer, String, Date, DateTime, ForeignKey, TEXT\n", "from sqlalchemy.sql.expression import table, text\n", "from sqlalchemy.dialects.postgresql.json import JSONB\n", "from sqlalchemy.orm import sessionmaker\n", "from sqlalchemy import and_, or_, not_\n", "from sqlalchemy import literal\n", "import datetime\n", "import itertools\n", "from sklearn.ensemble import IsolationForest\n", "\n", "DATABASE_URL = \"*************************************************/n_data\"\n", "engine = create_engine(DATABASE_URL, poolclass=NullPool)\n", "Session = sessionmaker(bind=engine)\n", "\n", "Base = declarative_base()\n", "\n", "class PetrolModel(Base):\n", "    __tablename__ = 'petrol'\n", "    auto_id = Column(Integer, primary_key=True)\n", "    petrol_id = Column(TEXT(convert_unicode=True), server_default=text(\"'petrol'||lpad(NEXTVAL('petrol_sequence'::regclass)::text,8, '0')\"))\n", "    datetime = Column(DateTime)\n", "    data_details = Column(JSONB, nullable=False, server_default='[]')\n", "    \n", "    \n", "class PetrolModel2(Base):\n", "    __tablename__ = 'petrol2'\n", "    auto_id = Column(Integer, primary_key=True)\n", "    petrol_id = Column(TEXT(convert_unicode=True), server_default=text(\"'petrol'||lpad(NEXTVAL('petrol_sequence'::regclass)::text,8, '0')\"))\n", "    datetime = Column(DateTime)\n", "    data_details = Column(JSONB, nullable=False, server_default='[]')\n", "    status_details = Column(JSONB, nullable=False, server_default='{}')    \n", "\n", "\n", "session = Session()\n", "\n", "\n", "\n", "today = datetime.date.today()\n", "future_day = today.day\n", "future_month = (today.month - 3) % 12\n", "future_year = today.year + ((today.month - 3) // 12)\n", "six_months_ago = datetime.date(future_year, future_month, future_day)\n", "\n", "\n", "df20 = pd.read_sql(sql = session.query(PetrolModel)\\\n", "                        .with_entities(\n", "                            PetrolModel.petrol_id,\n", "                            PetrolModel.datetime,\n", "                            PetrolModel.data_details[0]['che_liang_id'],\n", "                            PetrolModel.data_details[0]['distant'],\n", "                            PetrolModel.data_details[0]['qty'],\n", "                            PetrolModel.data_details[0]['bill'],\n", "                            )\n", "                            .filter(PetrolModel.datetime > six_months_ago).statement,\n", "                con = session.bind)\n", "\n", "\n", "df20 = df20.rename(columns={\n", "    \"anon_1\": 'che_liang_id',\n", "    \"anon_2\": 'distant',\n", "    \"anon_3\": 'qty',\n", "    \"anon_4\": 'bill',\n", "    })\n", "\n", "# display(\"df20\", df20)\n", "\n", "\n", "df30 = pd.read_sql(sql = session.query(PetrolModel2)\\\n", "                        .with_entities(\n", "                            PetrolModel2.petrol_id,\n", "                            PetrolModel2.datetime,\n", "                            PetrolModel2.data_details[0]['che_liang_id'],\n", "                            PetrolModel2.data_details[0]['distant'],\n", "                            PetrolModel2.data_details[0]['qty'],\n", "                            )\n", "                            .filter( PetrolModel2.datetime > six_months_ago).statement,\n", "                con = session.bind)\n", "\n", "\n", "df30 = df30.rename(columns={\n", "    \"anon_1\": 'che_liang_id',\n", "    \"anon_2\": 'distant',\n", "    \"anon_3\": 'qty',\n", "    })\n", "\n", "# display(\"df30\", df30)\n", "\n", "d = {'petrol_id': ['petroltest100', 'petroltest200', 'petroltest300'], \n", "     'datetime': ['3000-03-26 13:29:06.333246', '3000-03-26 13:29:06.333246', '3000-03-26 13:29:06.333246'], \n", "     'che_liang_id': [999999, 999999, 999999],\n", "     'distant': [410, 570, 700],\n", "     'qty': [20, 20, 20],\n", "     'bill': ['', '', '']\n", "    }\n", "df31 = pd.DataFrame(data=d)\n", "# display(df31)\n", "\n", "df40 = pd.concat([df20, df30], ignore_index=True)\n", "# df40 = df40.head(60)\n", "df40 = df40.sort_values(['che_liang_id', 'datetime'], ascending=[True, True])\n", "# add two records to the last of df40\n", "    \n", "# return che_liang_id values 23021, 194\n", "df40 = df40[df40['che_liang_id'].isin([2343, 26594])]\n", "# return che_liang_id values == 194\n", "# df40 = df40[df40['che_liang_id'] == 25371]\n", "\n", "df40['previous_distant'] = df40.groupby('che_liang_id')['distant'].transform(lambda x : None if x.any() == 0 else x.shift())\n", "df40['distance_used'] = df40.groupby('che_liang_id').apply(lambda x : x['previous_distant'] if x['previous_distant'].any() == 0 else x['distant'] - x['previous_distant']).reset_index('che_liang_id',drop=True)\n", "df40['mean_distant'] = df40.groupby('che_liang_id')['distance_used'].transform(lambda x : x.mean())\n", "df40['previous_qty'] = df40.groupby('che_liang_id').apply(lambda x : x['distance_used'] if x['distant'].any() == 0 else x['qty'].shift()).reset_index('che_liang_id',drop=True)\n", "df40['km_lit'] = df40.groupby('che_liang_id').apply(lambda x : (x['distance_used'] / x['previous_qty'])  ).reset_index('che_liang_id',drop=True)\n", "df40['average'] = df40.groupby('che_liang_id')['km_lit'].transform(lambda x : x.mean())\n", "df40['average_percent'] = df40.groupby('che_liang_id').apply(lambda x : (x.km_lit - x.average) / x.average * 100).reset_index('che_liang_id',drop=True)\n", "def remove_outliers(df40, q=0.05):\n", "    upper = df40.quantile(1-q)\n", "    lower = df40.quantile(q)\n", "    mask = (df40 < upper) & (df40 > lower)\n", "    return mask\n", "df40['outliers'] = df40.groupby('che_liang_id')['km_lit'].transform(lambda x : remove_outliers(x, 0.1))\n", "df40['km_lit_2'] = df40.apply(lambda x : None if x['outliers'] == False else x['km_lit'], axis=1)\n", "df40['average_2'] = df40.groupby('che_liang_id')['km_lit_2'].transform(lambda x : x.mean())\n", "df40['average_percent_2'] = df40.groupby('che_liang_id').apply(lambda x : (x.km_lit_2 - x.average_2) / x.average_2 * 100).reset_index('che_liang_id',drop=True)\n", "df40['mean'] = df40.groupby('che_liang_id').apply(lambda x : ((x.average_2 - x.average) / x.average * 100)).reset_index('che_liang_id',drop=True)\n", "df40['count'] = df40['distance_used']\n", "df60 = df40.pivot_table(columns=['distance_used'], aggfunc='size')\n", "df60 = pd.DataFrame(df60)\n", "df60 = df60.rename(columns={0: 'countoo'}).reset_index()\n", "df40['count'] = df40[['count']].replace(to_replace =df60['distance_used'].to_list(), value =df60['countoo'].to_list())\n", "# df40['oneKmLit'] = 1 / df40['average']\n", "# df40['hundredKmLit'] = 100 / df40['average']\n", "# df40['minute'] = 60 * df40['average']\n", "# df40['km_lit_std'] = df40.groupby('che_liang_id')['km_lit'].transform(lambda x : x.std())\n", "# df40['kmLitMin'] = df40.groupby('che_liang_id')['km_lit'].transform(lambda x : x.min())\n", "# df40['km_lit_25'] = df40.groupby('che_liang_id')['km_lit'].transform(lambda x : x.quantile(0.25))\n", "# df40['km_lit_50'] = df40.groupby('che_liang_id')['km_lit'].transform(lambda x : x.quantile(0.5))\n", "# df40['km_lit_75'] = df40.groupby('che_liang_id')['km_lit'].transform(lambda x : x.quantile(0.75))\n", "# df40['km_lit_max'] = df40.groupby('che_liang_id')['km_lit'].transform(lambda x : x.max())\n", "df40['che_liang_id_Count'] = df40.groupby('che_liang_id')['che_liang_id'].transform(lambda x : x.count())\n", "display(df40)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import warnings\n", "warnings.simplefilter('ignore')\n", "\n", "import pandas as pd\n", "import sqlalchemy\n", "from sqlalchemy import create_engine, tuple_\n", "from sqlalchemy.pool import NullPool\n", "import numpy as np\n", "import json\n", "from sqlalchemy.ext.declarative import declarative_base\n", "from sqlalchemy import Column, Integer, String, Date, DateTime, ForeignKey, TEXT\n", "from sqlalchemy.sql.expression import table, text\n", "from sqlalchemy.dialects.postgresql.json import JSONB\n", "from sqlalchemy.orm import sessionmaker\n", "from sqlalchemy import and_, or_, not_\n", "from sqlalchemy import literal\n", "import datetime\n", "import itertools\n", "from sklearn.ensemble import IsolationForest\n", "\n", "DATABASE_URL = \"*************************************************/n_data\"\n", "engine = create_engine(DATABASE_URL, poolclass=NullPool)\n", "Session = sessionmaker(bind=engine)\n", "\n", "Base = declarative_base()\n", "\n", "class PetrolModel(Base):\n", "    __tablename__ = 'petrol'\n", "    auto_id = Column(Integer, primary_key=True)\n", "    petrol_id = Column(TEXT(convert_unicode=True), server_default=text(\"'petrol'||lpad(NEXTVAL('petrol_sequence'::regclass)::text,8, '0')\"))\n", "    datetime = Column(DateTime)\n", "    data_details = Column(JSONB, nullable=False, server_default='[]')\n", "    \n", "    \n", "class PetrolModel2(Base):\n", "    __tablename__ = 'petrol2'\n", "    auto_id = Column(Integer, primary_key=True)\n", "    petrol_id = Column(TEXT(convert_unicode=True), server_default=text(\"'petrol'||lpad(NEXTVAL('petrol_sequence'::regclass)::text,8, '0')\"))\n", "    datetime = Column(DateTime)\n", "    data_details = Column(JSONB, nullable=False, server_default='[]')\n", "    status_details = Column(JSONB, nullable=False, server_default='{}')    \n", "\n", "\n", "session = Session()\n", "\n", "\n", "\n", "today = datetime.date.today()\n", "future_day = today.day\n", "future_month = (today.month - 2) % 12\n", "future_year = today.year + ((today.month - 2) // 12)\n", "six_months_ago = datetime.date(future_year, future_month, future_day)\n", "\n", "\n", "df20 = pd.read_sql(sql = session.query(PetrolModel)\\\n", "                        .with_entities(\n", "                            PetrolModel.petrol_id,\n", "                            PetrolModel.datetime,\n", "                            PetrolModel.data_details[0]['che_liang_id'],\n", "                            PetrolModel.data_details[0]['distant'],\n", "                            PetrolModel.data_details[0]['qty'],\n", "                            PetrolModel.data_details[0]['bill'],\n", "                            )\n", "                            .filter(PetrolModel.datetime > six_months_ago).statement,\n", "                con = session.bind)\n", "\n", "\n", "df20 = df20.rename(columns={\n", "    \"anon_1\": 'che_liang_id',\n", "    \"anon_2\": 'distant',\n", "    \"anon_3\": 'qty',\n", "    \"anon_4\": 'bill',\n", "    })\n", "\n", "# display(\"df20\", df20)\n", "\n", "\n", "df30 = pd.read_sql(sql = session.query(PetrolModel2)\\\n", "                        .with_entities(\n", "                            PetrolModel2.petrol_id,\n", "                            PetrolModel2.datetime,\n", "                            PetrolModel2.data_details[0]['che_liang_id'],\n", "                            PetrolModel2.data_details[0]['distant'],\n", "                            PetrolModel2.data_details[0]['qty'],\n", "                            )\n", "                            .filter( PetrolModel2.datetime > six_months_ago).statement,\n", "                con = session.bind)\n", "\n", "\n", "df30 = df30.rename(columns={\n", "    \"anon_1\": 'che_liang_id',\n", "    \"anon_2\": 'distant',\n", "    \"anon_3\": 'qty',\n", "    })\n", "\n", "# display(\"df30\", df30)\n", "\n", "df40 = pd.concat([df20, df30], ignore_index=True)\n", "# df40 = df40.head(60)\n", "df40 = df40.sort_values(['che_liang_id', 'datetime'], ascending=[True, True])\n", "# add two records to the last of df40\n", "    \n", "# return che_liang_id values 23021, 194\n", "df40 = df40[df40['che_liang_id'].isin([2343, 26594, 36768])]\n", "# return che_liang_id values == 194\n", "# df40 = df40[df40['che_liang_id'] == 25371]\n", "\n", "df40['previous_distant'] = df40.groupby('che_liang_id')['distant'].transform(lambda x : None if x.any() == 0 else x.shift())\n", "df40['distance_used'] = df40.groupby('che_liang_id').apply(lambda x : x['previous_distant'] if x['previous_distant'].any() == 0 else x['distant'] - x['previous_distant']).reset_index('che_liang_id',drop=True)\n", "df40['mean_distant'] = df40.groupby('che_liang_id')['distance_used'].transform(lambda x : x.mean())\n", "df40['previous_qty'] = df40.groupby('che_liang_id').apply(lambda x : x['distance_used'] if x['distant'].any() == 0 else x['qty'].shift()).reset_index('che_liang_id',drop=True)\n", "df40['km_lit'] = df40.groupby('che_liang_id').apply(lambda x : (x['distance_used'] / x['previous_qty'])  ).reset_index('che_liang_id',drop=True)\n", "df40['average'] = df40.groupby('che_liang_id')['km_lit'].transform(lambda x : x.mean())\n", "df40['average_percent'] = df40.groupby('che_liang_id').apply(lambda x : (x.km_lit - x.average) / x.average * 100).reset_index('che_liang_id',drop=True)\n", "def remove_outliers(df40, q=0.05):\n", "    upper = df40.quantile(1-q)\n", "    lower = df40.quantile(q)\n", "    mask = (df40 < upper) & (df40 > lower)\n", "    return mask\n", "df40['outliers'] = df40.groupby('che_liang_id')['km_lit'].transform(lambda x : remove_outliers(x, 0.1))\n", "df40['distance_used_2'] = df40.groupby('che_liang_id').apply(lambda x : 0 if x['outliers'].any() == False else x['distance_used']).reset_index('che_liang_id',drop=True)\n", "df40['distance_used_2'] = df40.apply(lambda x : None if x['outliers'] == False else x['km_lit'], axis=1)\n", "df40['km_lit_2'] = df40.apply(lambda x : None if x['outliers'] == False else x['km_lit'], axis=1)\n", "df40['average_2'] = df40.groupby('che_liang_id')['km_lit_2'].transform(lambda x : x.mean())\n", "df40['average_percent_2'] = df40.groupby('che_liang_id').apply(lambda x : (x.km_lit_2 - x.average_2) / x.average_2 * 100).reset_index('che_liang_id',drop=True)\n", "df40['mean'] = df40.groupby('che_liang_id').apply(lambda x : ((x.average_2 - x.average) / x.average * 100)).reset_index('che_liang_id',drop=True)\n", "display(df40)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%%time\n", "import warnings\n", "warnings.simplefilter('ignore')\n", "\n", "import pandas as pd\n", "import sqlalchemy\n", "from sqlalchemy import create_engine, tuple_\n", "from sqlalchemy.pool import NullPool\n", "import numpy as np\n", "import json\n", "from sqlalchemy.ext.declarative import declarative_base\n", "from sqlalchemy import Column, Integer, String, Date, DateTime, ForeignKey, TEXT\n", "from sqlalchemy.sql.expression import table, text\n", "from sqlalchemy.dialects.postgresql.json import JSONB\n", "from sqlalchemy.orm import sessionmaker\n", "from sqlalchemy import and_, or_, not_\n", "from sqlalchemy import literal\n", "import datetime\n", "import itertools\n", "from sklearn.ensemble import IsolationForest\n", "\n", "DATABASE_URL = \"*************************************************/test_nern\"\n", "engine = create_engine(DATABASE_URL, poolclass=NullPool)\n", "Session = sessionmaker(bind=engine)\n", "\n", "Base = declarative_base()\n", "\n", "class PetrolModel(Base):\n", "    __tablename__ = 'petrol'\n", "    auto_id = Column(Integer, primary_key=True)\n", "    petrol_id = Column(TEXT(convert_unicode=True), server_default=text(\"'petrol'||lpad(NEXTVAL('petrol_sequence'::regclass)::text,8, '0')\"))\n", "    datetime = Column(DateTime)\n", "    data_details = Column(JSONB, nullable=False, server_default='[]')\n", "    \n", "    \n", "class PetrolModel2(Base):\n", "    __tablename__ = 'petrol2'\n", "    auto_id = Column(Integer, primary_key=True)\n", "    petrol_id = Column(TEXT(convert_unicode=True), server_default=text(\"'petrol'||lpad(NEXTVAL('petrol_sequence'::regclass)::text,8, '0')\"))\n", "    datetime = Column(DateTime)\n", "    data_details = Column(JSONB, nullable=False, server_default='[]')\n", "    status_details = Column(JSONB, nullable=False, server_default='{}')    \n", "\n", "\n", "session = Session()\n", "\n", "\n", "\n", "today = datetime.date.today()\n", "future_day = today.day\n", "future_month = (today.month - 3) % 12\n", "future_year = today.year + ((today.month - 3) // 12)\n", "six_months_ago = datetime.date(future_year, future_month, future_day)\n", "\n", "\n", "df20 = pd.read_sql(sql = session.query(PetrolModel)\\\n", "                        .with_entities(\n", "                            PetrolModel.petrol_id,\n", "                            PetrolModel.datetime,\n", "                            PetrolModel.data_details[0]['che_liang_id'],\n", "                            PetrolModel.data_details[0]['distant'],\n", "                            PetrolModel.data_details[0]['qty'],\n", "                            )\n", "                            .filter(PetrolModel.datetime > six_months_ago).statement,\n", "                con = session.bind)\n", "\n", "\n", "df20 = df20.rename(columns={\n", "    \"anon_1\": 'che_liang_id',\n", "    \"anon_2\": 'distant',\n", "    \"anon_3\": 'qty',\n", "    })\n", "\n", "# display(\"df20\", df20)\n", "\n", "\n", "df30 = pd.read_sql(sql = session.query(PetrolModel2)\\\n", "                        .with_entities(\n", "                            PetrolModel2.petrol_id,\n", "                            PetrolModel2.datetime,\n", "                            PetrolModel2.data_details[0]['che_liang_id'],\n", "                            PetrolModel2.data_details[0]['distant'],\n", "                            PetrolModel2.data_details[0]['qty'],\n", "                            )\n", "                            .filter( PetrolModel2.datetime > six_months_ago).statement,\n", "                con = session.bind)\n", "\n", "\n", "df30 = df30.rename(columns={\n", "    \"anon_1\": 'che_liang_id',\n", "    \"anon_2\": 'distant',\n", "    \"anon_3\": 'qty',\n", "    })\n", "\n", "# display(\"df30\", df30)\n", "\n", "\n", "df40 = pd.concat([df20, df30], ignore_index=True)\n", "df40 = df40.sort_values(['datetime'], ascending=[True])\n", "# display(df40)\n", "\n", "\n", "df50 = df40[df40['che_liang_id'] ==  18576 ]\n", "df50['previous_distant'] = df50['distant'].shift()\n", "def fx(x):\n", "    if x['previous_distant'] == 0:\n", "        return x['previous_distant']\n", "    elif x['distant'] == 0:\n", "        return x['distant']\n", "    else:\n", "        return x['distant'] - x['previous_distant']\n", "df50['distance_used'] = df50.apply(lambda x : fx(x),axis=1).fillna(0)\n", "df50['mean_distance_used'] = df50['distance_used'].mean()\n", "df50['previous_qty'] = df50['qty'].shift()\n", "\n", "df50['km_lit'] = (df50['distance_used'] / df50['previous_qty'])         \n", "df50['average'] = df50[df50['km_lit'] != 0][\"km_lit\"].mean()\n", "def fx2(x):\n", "    if x['km_lit'] == 0:\n", "        return x['km_lit']\n", "    else:\n", "        return ((x.distance_used - x.mean_distance_used) / x.mean_distance_used) * 100\n", "df50['average_percent'] = df50.apply(lambda x : fx2(x),axis=1)\n", "\n", "\n", "outlier_detector = IsolationForest(random_state=42)\n", "outlier_detector.fit(df50.loc[:,['distance_used']])\n", "prediction = outlier_detector.predict(df50.loc[:,['distance_used']])\n", "prediction_strings = ['Outlier' if x < 0 else 'Standard' for x in prediction]\n", "df50['outlier_flag'] = prediction_strings\n", "\n", "def fx(x):\n", "        if x['outlier_flag'] == 'Outlier':\n", "            return None\n", "        else:\n", "            return x['distance_used']\n", "df50['distance_used_2'] = df50.apply(lambda x : fx(x),axis=1)\n", "df50['mean_distance_used_2'] = df50['distance_used_2'].mean()\n", "def fx3(x):\n", "    if x['outlier_flag'] == 'Outlier':\n", "        return None\n", "    else:\n", "        return x['distance_used'] / x['previous_qty']\n", "df50['km_lit_2'] = df50.apply(lambda x : fx3(x),axis=1)\n", "df50['average_2'] = df50[df50['km_lit_2'] != 0][\"km_lit_2\"].mean()\n", "def fx4(x):\n", "    if x['km_lit_2'] == None:\n", "        return x['km_lit_2']\n", "    else:\n", "        return ((x.distance_used - x.mean_distance_used_2) / x.mean_distance_used_2) * 100\n", "df50['average_percent_2'] = df50.apply(lambda x : fx4(x),axis=1)\n", "df50['mean'] = ((df50.mean_distance_used - df50.mean_distance_used_2) / df50.mean_distance_used_2) * 100\n", "display(df50)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%%time\n", "import warnings\n", "warnings.simplefilter('ignore')\n", "\n", "import pandas as pd\n", "import sqlalchemy\n", "from sqlalchemy import create_engine, tuple_\n", "from sqlalchemy.pool import NullPool\n", "import numpy as np\n", "import json\n", "from sqlalchemy.ext.declarative import declarative_base\n", "from sqlalchemy import Column, Integer, String, Date, DateTime, ForeignKey, TEXT\n", "from sqlalchemy.sql.expression import table, text\n", "from sqlalchemy.dialects.postgresql.json import JSONB\n", "from sqlalchemy.orm import sessionmaker\n", "from sqlalchemy import and_, or_, not_\n", "from sqlalchemy import literal\n", "import datetime\n", "import itertools\n", "from sklearn.ensemble import IsolationForest\n", "\n", "DATABASE_URL = \"*************************************************/test_nern\"\n", "engine = create_engine(DATABASE_URL, poolclass=NullPool)\n", "Session = sessionmaker(bind=engine)\n", "\n", "Base = declarative_base()\n", "\n", "class PetrolModel(Base):\n", "    __tablename__ = 'petrol'\n", "    auto_id = Column(Integer, primary_key=True)\n", "    petrol_id = Column(TEXT(convert_unicode=True), server_default=text(\"'petrol'||lpad(NEXTVAL('petrol_sequence'::regclass)::text,8, '0')\"))\n", "    datetime = Column(DateTime)\n", "    data_details = Column(JSONB, nullable=False, server_default='[]')\n", "    \n", "    \n", "class PetrolModel2(Base):\n", "    __tablename__ = 'petrol2'\n", "    auto_id = Column(Integer, primary_key=True)\n", "    petrol_id = Column(TEXT(convert_unicode=True), server_default=text(\"'petrol'||lpad(NEXTVAL('petrol_sequence'::regclass)::text,8, '0')\"))\n", "    datetime = Column(DateTime)\n", "    data_details = Column(JSONB, nullable=False, server_default='[]')\n", "    status_details = Column(JSONB, nullable=False, server_default='{}')    \n", "\n", "\n", "session = Session()\n", "\n", "\n", "\n", "today = datetime.date.today()\n", "future_day = today.day\n", "future_month = (today.month - 3) % 12\n", "future_year = today.year + ((today.month - 3) // 12)\n", "six_months_ago = datetime.date(future_year, future_month, future_day)\n", "\n", "\n", "df20 = pd.read_sql(sql = session.query(PetrolModel)\\\n", "                        .with_entities(\n", "                            PetrolModel.petrol_id,\n", "                            PetrolModel.datetime,\n", "                            PetrolModel.data_details[0]['che_liang_id'],\n", "                            PetrolModel.data_details[0]['distant'],\n", "                            PetrolModel.data_details[0]['qty'],\n", "                            )\n", "                            .filter(PetrolModel.datetime > six_months_ago).statement,\n", "                con = session.bind)\n", "\n", "\n", "df20 = df20.rename(columns={\n", "    \"anon_1\": 'che_liang_id',\n", "    \"anon_2\": 'distant',\n", "    \"anon_3\": 'qty',\n", "    })\n", "\n", "# display(\"df20\", df20)\n", "\n", "\n", "df30 = pd.read_sql(sql = session.query(PetrolModel2)\\\n", "                        .with_entities(\n", "                            PetrolModel2.petrol_id,\n", "                            PetrolModel2.datetime,\n", "                            PetrolModel2.data_details[0]['che_liang_id'],\n", "                            PetrolModel2.data_details[0]['distant'],\n", "                            PetrolModel2.data_details[0]['qty'],\n", "                            )\n", "                            .filter( PetrolModel2.datetime > six_months_ago).statement,\n", "                con = session.bind)\n", "\n", "\n", "df30 = df30.rename(columns={\n", "    \"anon_1\": 'che_liang_id',\n", "    \"anon_2\": 'distant',\n", "    \"anon_3\": 'qty',\n", "    })\n", "\n", "# display(\"df30\", df30)\n", "\n", "\n", "df40 = pd.concat([df20, df30], ignore_index=True)\n", "df40 = df40.sort_values(['datetime'], ascending=[True])\n", "# display(df40)\n", "\n", "\n", "df50 = df40[df40['che_liang_id'] ==  20929 ]\n", "df50['previous_distant'] = df50['distant'].shift()\n", "df50['distance_used'] = df50.apply(lambda x: x['previous_distant'] if x['previous_distant'] == 0 else (x['distant'] if x['distant'] == 0 else x['distant'] - x['previous_distant']), axis=1)\n", "df50['mean_distance_used'] = df50['distance_used'].mean()\n", "df50['previous_qty'] = df50['qty'].shift()\n", "df50['count'] = df50['distance_used']\n", "df50['km_lit'] = (df50['distance_used'] / df50['previous_qty'])         \n", "df50['average'] = df50[df50['km_lit'] != 0][\"km_lit\"].mean()\n", "df50['average_percent'] = df50.apply(lambda x: x['km_lit'] if x['km_lit'] == 0 else x.mean_distance_used and ((x.distance_used - x.mean_distance_used) / x.mean_distance_used) * 100, axis=1)\n", "\n", "outlier_detector = IsolationForest(random_state=42)\n", "distance_used_fillna = df50[['distance_used']].fillna(0)\n", "outlier_detector.fit(distance_used_fillna)\n", "prediction = outlier_detector.predict(distance_used_fillna)\n", "prediction_strings = ['Outlier' if x < 0 else 'Standard' for x in prediction]\n", "df50['outlier_flag'] = prediction_strings\n", "\n", "df50['distance_used_2'] = df50.apply(lambda x: None if x['outlier_flag'] == 'Outlier' else x['distance_used'], axis=1)\n", "df50['mean_distance_used_2'] = df50['distance_used_2'].mean()\n", "df50['km_lit_2'] = df50.apply(lambda x: None if x['outlier_flag'] == 'Outlier' else x['distance_used'] / x['previous_qty'], axis=1)\n", "df50['average_2'] = df50[df50['km_lit_2'] != 0][\"km_lit_2\"].mean()\n", "df50['average_percent_2'] = df50.apply(lambda x: x['km_lit_2'] if x['km_lit_2'] == None else x.mean_distance_used and ((x.distance_used - x.mean_distance_used_2) / x.mean_distance_used_2) * 100, axis=1)\n", "df50['mean'] = ((df50.mean_distance_used - df50.mean_distance_used_2) / df50.mean_distance_used_2) * 100\n", "\n", "df60 = df50.pivot_table(columns=['distance_used'], aggfunc='size')\n", "df60 = pd.DataFrame(df60)\n", "df60 = df60.rename(columns={0: 'countoo'}).reset_index()\n", "df50['count'] = df50[['count']].replace(to_replace =df60['distance_used'].to_list(), value =df60['countoo'].to_list())\n", "display(df50)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%%time\n", "import warnings\n", "warnings.simplefilter('ignore')\n", "\n", "import pandas as pd\n", "import sqlalchemy\n", "from sqlalchemy import create_engine, tuple_\n", "from sqlalchemy.pool import NullPool\n", "import numpy as np\n", "import json\n", "from sqlalchemy.ext.declarative import declarative_base\n", "from sqlalchemy import Column, Integer, String, Date, DateTime, ForeignKey, TEXT\n", "from sqlalchemy.sql.expression import table, text\n", "from sqlalchemy.dialects.postgresql.json import JSONB\n", "from sqlalchemy.orm import sessionmaker\n", "from sqlalchemy import and_, or_, not_\n", "from sqlalchemy import literal\n", "import datetime\n", "import itertools\n", "from sklearn.ensemble import IsolationForest\n", "\n", "DATABASE_URL = \"*************************************************/test_nern\"\n", "engine = create_engine(DATABASE_URL, poolclass=NullPool)\n", "Session = sessionmaker(bind=engine)\n", "\n", "Base = declarative_base()\n", "\n", "class PetrolModel(Base):\n", "    __tablename__ = 'petrol'\n", "    auto_id = Column(Integer, primary_key=True)\n", "    petrol_id = Column(TEXT(convert_unicode=True), server_default=text(\"'petrol'||lpad(NEXTVAL('petrol_sequence'::regclass)::text,8, '0')\"))\n", "    datetime = Column(DateTime)\n", "    data_details = Column(JSONB, nullable=False, server_default='[]')\n", "    \n", "    \n", "class PetrolModel2(Base):\n", "    __tablename__ = 'petrol2'\n", "    auto_id = Column(Integer, primary_key=True)\n", "    petrol_id = Column(TEXT(convert_unicode=True), server_default=text(\"'petrol'||lpad(NEXTVAL('petrol_sequence'::regclass)::text,8, '0')\"))\n", "    datetime = Column(DateTime)\n", "    data_details = Column(JSONB, nullable=False, server_default='[]')\n", "    status_details = Column(JSONB, nullable=False, server_default='{}')    \n", "\n", "\n", "session = Session()\n", "\n", "\n", "\n", "today = datetime.date.today()\n", "future_day = today.day\n", "future_month = (today.month - 3) % 12\n", "future_year = today.year + ((today.month - 3) // 12)\n", "six_months_ago = datetime.date(future_year, future_month, future_day)\n", "\n", "\n", "df20 = pd.read_sql(sql = session.query(PetrolModel)\\\n", "                        .with_entities(\n", "                            PetrolModel.petrol_id,\n", "                            PetrolModel.datetime,\n", "                            PetrolModel.data_details[0]['che_liang_id'],\n", "                            PetrolModel.data_details[0]['distant'],\n", "                            PetrolModel.data_details[0]['qty'],\n", "                            PetrolModel.data_details[0]['bill'],\n", "                            )\n", "                            .filter(PetrolModel.datetime > six_months_ago).statement,\n", "                con = session.bind)\n", "\n", "\n", "df20 = df20.rename(columns={\n", "    \"anon_1\": 'che_liang_id',\n", "    \"anon_2\": 'distant',\n", "    \"anon_3\": 'qty',\n", "    \"anon_4\": 'bill',\n", "    })\n", "\n", "# display(\"df20\", df20)\n", "\n", "\n", "df30 = pd.read_sql(sql = session.query(PetrolModel2)\\\n", "                        .with_entities(\n", "                            PetrolModel2.petrol_id,\n", "                            PetrolModel2.datetime,\n", "                            PetrolModel2.data_details[0]['che_liang_id'],\n", "                            PetrolModel2.data_details[0]['distant'],\n", "                            PetrolModel2.data_details[0]['qty'],\n", "                            )\n", "                            .filter( PetrolModel2.datetime > six_months_ago).statement,\n", "                con = session.bind)\n", "\n", "\n", "df30 = df30.rename(columns={\n", "    \"anon_1\": 'che_liang_id',\n", "    \"anon_2\": 'distant',\n", "    \"anon_3\": 'qty',\n", "    })\n", "\n", "# display(\"df30\", df30)\n", "\n", "d = {'petrol_id': ['petroltest100', 'petroltest200', 'petroltest300'], \n", "     'datetime': ['3000-03-26 13:29:06.333246', '3000-03-26 13:29:06.333246', '3000-03-26 13:29:06.333246'], \n", "     'che_liang_id': [999999, 999999, 999999],\n", "     'distant': [410, 570, 700],\n", "     'qty': [20, 20, 20],\n", "     'bill': ['', '', '']\n", "    }\n", "df31 = pd.DataFrame(data=d)\n", "# display(df31)\n", "\n", "df40 = pd.concat([df20, df30], ignore_index=True)\n", "# df40 = df40.head(60)\n", "df40 = df40.sort_values(['che_liang_id', 'datetime'], ascending=[True, True])\n", "# add two records to the last of df40\n", "    \n", "# return che_liang_id values 23021, 194\n", "df40 = df40[df40['che_liang_id'].isin([2343, 20929])]\n", "# return che_liang_id values == 194\n", "# df40 = df40[df40['che_liang_id'] == 25371]\n", "\n", "df40['previous_distant'] = df40.groupby('che_liang_id')['distant'].transform(lambda x : None if x.any() == 0 else x.shift())\n", "df40['distance_used'] = df40.groupby('che_liang_id').apply(lambda x : x['previous_distant'] if x['previous_distant'].any() == 0 else (x['distant'] if x['distant'].any() == 0 else x['distant'] - x['previous_distant'])).reset_index('che_liang_id',drop=True)\n", "df40['mean_distance_used'] = df40.groupby('che_liang_id')['distance_used'].transform(lambda x : x.mean())\n", "df40['previous_qty'] = df40.groupby('che_liang_id').apply(lambda x : x['distance_used'] if x['distant'].any() == 0 else x['qty'].shift()).reset_index('che_liang_id',drop=True)\n", "df40['km_lit'] = df40.groupby('che_liang_id').apply(lambda x : (x['distance_used'] / x['previous_qty'])  ).reset_index('che_liang_id',drop=True)\n", "df40['average'] = df40.groupby('che_liang_id')['km_lit'].transform(lambda x : x.mean())\n", "df40['average_percent'] = df40.groupby('che_liang_id').apply(lambda x: x['km_lit'] if x['km_lit'].any() == 0 else ((x.distance_used - x.mean_distance_used) / x.mean_distance_used) * 100).reset_index('che_liang_id',drop=True)\n", "# df50['average_percent'] = df50.apply(lambda x: x['km_lit'] if x['km_lit'] == 0 else x.mean_distance_used and ((x.distance_used - x.mean_distance_used) / x.mean_distance_used) * 100, axis=1)\n", "\n", "# def remove_outliers(df40, q=0.05):\n", "#     upper = df40.quantile(1-q)\n", "#     lower = df40.quantile(q)\n", "#     mask = (df40 < upper) & (df40 > lower)\n", "#     return mask\n", "# df40['outliers'] = df40.groupby('che_liang_id')['km_lit'].transform(lambda x : remove_outliers(x, 0.1))\n", "outlier_detector = IsolationForest(random_state=42)\n", "distance_used_fillna = df40[['distance_used']].fillna(0)\n", "outlier_detector.fit(distance_used_fillna)\n", "prediction = outlier_detector.predict(distance_used_fillna)\n", "prediction_strings = ['Outlier' if x < 0 else 'Standard' for x in prediction]\n", "df40['outlier_flag'] = prediction_strings\n", "\n", "df40['km_lit_2'] = df40.apply(lambda x : None if x['outlier_flag'] == 'Outlier' else x['km_lit'], axis=1)\n", "df40['average_2'] = df40.groupby('che_liang_id')['km_lit_2'].transform(lambda x : x.mean())\n", "df40['average_percent_2'] = df40.groupby('che_liang_id').apply(lambda x : (x.km_lit_2 - x.average_2) / x.average_2 * 100).reset_index('che_liang_id',drop=True)\n", "df40['mean'] = df40.groupby('che_liang_id').apply(lambda x : ((x.average_2 - x.average) / x.average * 100)).reset_index('che_liang_id',drop=True)\n", "# df40['oneKmLit'] = 1 / df40['average']\n", "# df40['hundredKmLit'] = 100 / df40['average']\n", "# df40['minute'] = 60 * df40['average']\n", "# df40['che_liang_id_Count'] = df40.groupby('che_liang_id')['che_liang_id'].transform(lambda x : x.count())\n", "display(df40)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%%time\n", "import warnings\n", "warnings.simplefilter('ignore')\n", "\n", "import pandas as pd\n", "import sqlalchemy\n", "from sqlalchemy import create_engine, tuple_\n", "from sqlalchemy.pool import NullPool\n", "import numpy as np\n", "import json\n", "from sqlalchemy.ext.declarative import declarative_base\n", "from sqlalchemy import Column, Integer, String, Date, DateTime, ForeignKey, TEXT\n", "from sqlalchemy.sql.expression import table, text\n", "from sqlalchemy.dialects.postgresql.json import JSONB\n", "from sqlalchemy.orm import sessionmaker\n", "from sqlalchemy import and_, or_, not_\n", "from sqlalchemy import literal\n", "import datetime\n", "import itertools\n", "from sklearn.ensemble import IsolationForest\n", "\n", "DATABASE_URL = \"*************************************************/test_nern\"\n", "engine = create_engine(DATABASE_URL, poolclass=NullPool)\n", "Session = sessionmaker(bind=engine)\n", "\n", "Base = declarative_base()\n", "\n", "class PetrolModel(Base):\n", "    __tablename__ = 'petrol'\n", "    auto_id = Column(Integer, primary_key=True)\n", "    petrol_id = Column(TEXT(convert_unicode=True), server_default=text(\"'petrol'||lpad(NEXTVAL('petrol_sequence'::regclass)::text,8, '0')\"))\n", "    datetime = Column(DateTime)\n", "    data_details = Column(JSONB, nullable=False, server_default='[]')\n", "    \n", "    \n", "class PetrolModel2(Base):\n", "    __tablename__ = 'petrol2'\n", "    auto_id = Column(Integer, primary_key=True)\n", "    petrol_id = Column(TEXT(convert_unicode=True), server_default=text(\"'petrol'||lpad(NEXTVAL('petrol_sequence'::regclass)::text,8, '0')\"))\n", "    datetime = Column(DateTime)\n", "    data_details = Column(JSONB, nullable=False, server_default='[]')\n", "    status_details = Column(JSONB, nullable=False, server_default='{}')    \n", "\n", "\n", "session = Session()\n", "\n", "today = datetime.date.today()\n", "future_day = today.day\n", "future_month = (today.month - 12) % 12\n", "future_year = today.year + ((today.month - 12) // 12)\n", "six_months_ago = datetime.date(future_year, future_month, future_day)\n", "\n", "df20 = pd.read_sql(sql = session.query(PetrolModel)\\\n", "                        .with_entities(\n", "                            PetrolModel.petrol_id,\n", "                            PetrolModel.datetime,\n", "                            PetrolModel.data_details[0]['che_liang_id'],\n", "                            PetrolModel.data_details[0]['distant'],\n", "                            PetrolModel.data_details[0]['qty'],\n", "                            PetrolModel.data_details[0]['price'],\n", "                            PetrolModel.data_details[0]['product_id'],\n", "                            )\n", "                            .filter(PetrolModel.datetime > six_months_ago).statement,\n", "                con = session.bind)\n", "df20 = df20.rename(columns={\n", "    \"anon_1\": 'che_liang_id',\n", "    \"anon_2\": 'distant',\n", "    \"anon_3\": 'qty',\n", "    \"anon_4\": 'price',\n", "    \"anon_5\": 'product_id',\n", "    })\n", "# display(\"df20\", df20)\n", "\n", "df30 = pd.read_sql(sql = session.query(PetrolModel2)\\\n", "                        .with_entities(\n", "                            PetrolModel2.petrol_id,\n", "                            PetrolModel2.datetime,\n", "                            PetrolModel2.data_details[0]['che_liang_id'],\n", "                            PetrolModel2.data_details[0]['distant'],\n", "                            PetrolModel2.data_details[0]['qty'],\n", "                            PetrolModel2.data_details[0]['price'],\n", "                            PetrolModel2.data_details[0]['product_id'],\n", "                            )\n", "                            .filter( PetrolModel2.datetime > six_months_ago).statement,\n", "                con = session.bind)\n", "df30 = df30.rename(columns={\n", "    \"anon_1\": 'che_liang_id',\n", "    \"anon_2\": 'distant',\n", "    \"anon_3\": 'qty',\n", "    \"anon_4\": 'price',\n", "    \"anon_5\": 'product_id',\n", "    })\n", "\n", "# display(\"df30\", df30)\n", "\n", "\n", "df40 = pd.concat([df20, df30], ignore_index=True)\n", "df40 = df40.sort_values(['datetime'], ascending=[True])\n", "# display(df40)\n", "\n", "# df50 = df40[df40['che_liang_id'] ==  104 ]\n", "# return che_liang_id 104 and 25371\n", "df50 = df40[(df40['che_liang_id'] ==  104) | (df40['che_liang_id'] ==  25371)]\n", "\n", "df50['amount'] = df50.groupby('che_liang_id').apply(lambda x : x['qty'] * x['price']).reset_index('che_liang_id',drop=True)\n", "df50['count'] = 'calculate'\n", "df50['previous_distant'] = df50.groupby('che_liang_id')['distant'].transform(lambda x : x.shift())\n", "df50['distance_used'] = df50.groupby('che_liang_id').apply(lambda x : x['previous_distant'] if x['previous_distant'].any() == 0 else (x['distant'] if x['distant'].any() == 0 else x['distant'] - x['previous_distant'])).reset_index('che_liang_id',drop=True)\n", "df50['previous_qty'] = df50.groupby('che_liang_id').apply(lambda x : x['qty'].shift()).reset_index('che_liang_id',drop=True)\n", "df50['km_lit'] = df50.groupby('che_liang_id').apply(lambda x : (x['distance_used'] / x['previous_qty'])  ).reset_index('che_liang_id',drop=True)\n", "\n", "\n", "# df60 = df50.set_index('datetime', inplace=True)\n", "# df60 = df50.groupby('che_liang_id').resample('MS').agg({'che_liang_id': np.max, 'qty': np.sum, 'price': np.mean, 'amount': np.sum, 'count': np.size, 'km_lit': np.mean, 'distance_used': np.mean}).reset_index().round(decimals = 1).fillna(\"\")\n", "\n", "# df50['amount'] = df50['qty'] * df50['price']\n", "# df50['count'] = 'calculate'\n", "# df50['previous_distant'] = df50['distant'].shift()\n", "# def fx(x):\n", "#         if x['previous_distant'] == 0:\n", "#             return x['previous_distant']\n", "#         elif x['distant'] == 0:\n", "#             return x['distant']\n", "#         else:\n", "#             return x['distant'] - x['previous_distant']\n", "# df50['distance_used'] = df50.apply(lambda x : fx(x),axis=1)\n", "# df50['previous_qty'] = df50['qty'].shift()\n", "# df50['km_lit'] = (df50['distance_used'] / df50['previous_qty']) \n", "display(df50)\n", "\n", "\n", "# df60 = df50.set_index('datetime', inplace=True)\n", "# df60 = df50.resample('MS').agg({'che_liang_id': np.max, 'qty': np.sum, 'price': np.mean, 'amount': np.sum, 'count': np.size, 'km_lit': np.mean, 'distance_used': np.mean}).reset_index().round(decimals = 1).fillna(\"\")\n", "display(df60)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%%time\n", "import warnings\n", "warnings.simplefilter('ignore')\n", "\n", "import pandas as pd\n", "import sqlalchemy\n", "from sqlalchemy import create_engine, tuple_\n", "from sqlalchemy.pool import NullPool\n", "import numpy as np\n", "import json\n", "from sqlalchemy.ext.declarative import declarative_base\n", "from sqlalchemy import Column, Integer, String, Date, DateTime, ForeignKey, TEXT\n", "from sqlalchemy.sql.expression import table, text\n", "from sqlalchemy.dialects.postgresql.json import JSONB\n", "from sqlalchemy.orm import sessionmaker\n", "from sqlalchemy import and_, or_, not_\n", "from sqlalchemy import literal\n", "import datetime\n", "import itertools\n", "from sklearn.ensemble import IsolationForest\n", "\n", "DATABASE_URL = \"*************************************************/test_nern\"\n", "engine = create_engine(DATABASE_URL, poolclass=NullPool)\n", "Session = sessionmaker(bind=engine)\n", "\n", "Base = declarative_base()\n", "\n", "class PetrolModel(Base):\n", "    __tablename__ = 'petrol'\n", "    auto_id = Column(Integer, primary_key=True)\n", "    petrol_id = Column(TEXT(convert_unicode=True), server_default=text(\"'petrol'||lpad(NEXTVAL('petrol_sequence'::regclass)::text,8, '0')\"))\n", "    datetime = Column(DateTime)\n", "    data_details = Column(JSONB, nullable=False, server_default='[]')\n", "    \n", "    \n", "class PetrolModel2(Base):\n", "    __tablename__ = 'petrol2'\n", "    auto_id = Column(Integer, primary_key=True)\n", "    petrol_id = Column(TEXT(convert_unicode=True), server_default=text(\"'petrol'||lpad(NEXTVAL('petrol_sequence'::regclass)::text,8, '0')\"))\n", "    datetime = Column(DateTime)\n", "    data_details = Column(JSONB, nullable=False, server_default='[]')\n", "    status_details = Column(JSONB, nullable=False, server_default='{}')    \n", "\n", "\n", "session = Session()\n", "\n", "today = datetime.date.today()\n", "future_day = today.day\n", "future_month = (today.month - 12) % 12\n", "future_year = today.year + ((today.month - 12) // 12)\n", "six_months_ago = datetime.date(future_year, future_month, future_day)\n", "\n", "df20 = pd.read_sql(sql = session.query(PetrolModel)\\\n", "                        .with_entities(\n", "                            PetrolModel.petrol_id,\n", "                            PetrolModel.datetime,\n", "                            PetrolModel.data_details[0]['che_liang_id'],\n", "                            PetrolModel.data_details[0]['distant'],\n", "                            PetrolModel.data_details[0]['qty'],\n", "                            PetrolModel.data_details[0]['price'],\n", "                            PetrolModel.data_details[0]['product_id'],\n", "                            )\n", "                            # .filter(PetrolModel.datetime > six_months_ago)\n", "                            .statement,\n", "                con = session.bind)\n", "df20 = df20.rename(columns={\n", "    \"anon_1\": 'che_liang_id',\n", "    \"anon_2\": 'distant',\n", "    \"anon_3\": 'qty',\n", "    \"anon_4\": 'price',\n", "    \"anon_5\": 'product_id',\n", "    })\n", "# display(\"df20\", df20)\n", "\n", "df30 = pd.read_sql(sql = session.query(PetrolModel2)\\\n", "                        .with_entities(\n", "                            PetrolModel2.petrol_id,\n", "                            PetrolModel2.datetime,\n", "                            PetrolModel2.data_details[0]['che_liang_id'],\n", "                            PetrolModel2.data_details[0]['distant'],\n", "                            PetrolModel2.data_details[0]['qty'],\n", "                            PetrolModel2.data_details[0]['price'],\n", "                            PetrolModel2.data_details[0]['product_id'],\n", "                            )\n", "                            .filter( PetrolModel2.datetime > six_months_ago).statement,\n", "                con = session.bind)\n", "df30 = df30.rename(columns={\n", "    \"anon_1\": 'che_liang_id',\n", "    \"anon_2\": 'distant',\n", "    \"anon_3\": 'qty',\n", "    \"anon_4\": 'price',\n", "    \"anon_5\": 'product_id',\n", "    })\n", "\n", "# display(\"df30\", df30)\n", "\n", "\n", "df40 = pd.concat([df20, df30], ignore_index=True)\n", "df40 = df40.sort_values(['che_liang_id', 'datetime'], ascending=[True, True])\n", "# display(df40)\n", "\n", "# df50 = df40[df40['che_liang_id'] ==  104 ]\n", "# return che_liang_id 104 and 25371\n", "df50 = df40[(df40['che_liang_id'] ==  104) | (df40['che_liang_id'] ==  2290)]\n", "\n", "df50['amount'] = df50.groupby('che_liang_id').apply(lambda x : x['qty'] * x['price']).reset_index('che_liang_id',drop=True)\n", "df50['count'] = 'calculate'\n", "df50['previous_distant'] = df50.groupby('che_liang_id')['distant'].transform(lambda x : x.shift())\n", "df50['distance_used'] = df50.groupby('che_liang_id').apply(lambda x : x['previous_distant'] if x['previous_distant'].any() == 0 else (x['distant'] if x['distant'].any() == 0 else x['distant'] - x['previous_distant'])).reset_index('che_liang_id',drop=True)\n", "df50['previous_qty'] = df50.groupby('che_liang_id')['qty'].transform(lambda x : x.shift())\n", "df50['km_lit'] = df50.groupby('che_liang_id').apply(lambda x : (x['distance_used'] / x['previous_qty'])  ).reset_index('che_liang_id',drop=True)\n", "\n", "# df50['amount'] = df50['qty'] * df50['price']\n", "# df50['count'] = 'calculate'\n", "# df50['previous_distant'] = df50['distant'].shift()\n", "# def fx(x):\n", "#         if x['previous_distant'] == 0:\n", "#             return x['previous_distant']\n", "#         elif x['distant'] == 0:\n", "#             return x['distant']\n", "#         else:\n", "#             return x['distant'] - x['previous_distant']\n", "# df50['distance_used'] = df50.apply(lambda x : fx(x),axis=1)\n", "# df50['previous_qty'] = df50['qty'].shift()\n", "# df50['km_lit'] = (df50['distance_used'] / df50['previous_qty']) \n", "\n", "display(\"df50\", df50)\n", "\n", "# Sum the value of amount based on che_liang_id every month.\n", "# df60 = df50.groupby(['che_liang_id', pd.Grouper(key='datetime', freq='1M')]).sum().reset_index()\n", "\n", "df60 = df50.set_index('datetime', inplace=True)\n", "df60 = df50.groupby('che_liang_id').resample(\"MS\").agg({'qty': np.sum, 'price': np.mean, 'amount': np.sum, 'count': np.size, 'km_lit': np.mean, 'distance_used': np.mean}).round(decimals = 1).fillna(\"\")\n", "\n", "display(\"df60\", df60)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%%time\n", "import warnings\n", "warnings.simplefilter('ignore')\n", "\n", "import pandas as pd\n", "import sqlalchemy\n", "from sqlalchemy import create_engine, tuple_\n", "from sqlalchemy.pool import NullPool\n", "import numpy as np\n", "import json\n", "from sqlalchemy.ext.declarative import declarative_base\n", "from sqlalchemy import Column, Integer, String, Date, DateTime, ForeignKey, TEXT\n", "from sqlalchemy.sql.expression import table, text\n", "from sqlalchemy.dialects.postgresql.json import JSONB\n", "from sqlalchemy.orm import sessionmaker\n", "from sqlalchemy import and_, or_, not_\n", "from sqlalchemy import literal\n", "import datetime\n", "import itertools\n", "from sklearn.ensemble import IsolationForest\n", "\n", "DATABASE_URL = \"*************************************************/test_nern\"\n", "engine = create_engine(DATABASE_URL, poolclass=NullPool)\n", "Session = sessionmaker(bind=engine)\n", "\n", "Base = declarative_base()\n", "\n", "class PetrolModel(Base):\n", "    __tablename__ = 'petrol'\n", "    auto_id = Column(Integer, primary_key=True)\n", "    petrol_id = Column(TEXT(convert_unicode=True), server_default=text(\"'petrol'||lpad(NEXTVAL('petrol_sequence'::regclass)::text,8, '0')\"))\n", "    datetime = Column(DateTime)\n", "    data_details = Column(JSONB, nullable=False, server_default='[]')\n", "    \n", "    \n", "class PetrolModel2(Base):\n", "    __tablename__ = 'petrol2'\n", "    auto_id = Column(Integer, primary_key=True)\n", "    petrol_id = Column(TEXT(convert_unicode=True), server_default=text(\"'petrol'||lpad(NEXTVAL('petrol_sequence'::regclass)::text,8, '0')\"))\n", "    datetime = Column(DateTime)\n", "    data_details = Column(JSONB, nullable=False, server_default='[]')\n", "    status_details = Column(JSONB, nullable=False, server_default='{}')    \n", "\n", "\n", "session = Session()\n", "\n", "today = datetime.date.today()\n", "future_day = today.day\n", "future_month = (today.month - 12) % 12\n", "future_year = today.year + ((today.month - 12) // 12)\n", "six_months_ago = datetime.date(future_year, future_month, future_day)\n", "\n", "df20 = pd.read_sql(sql = session.query(PetrolModel)\\\n", "                        .with_entities(\n", "                            PetrolModel.petrol_id,\n", "                            PetrolModel.datetime,\n", "                            PetrolModel.data_details[0]['che_liang_id'],\n", "                            PetrolModel.data_details[0]['distant'],\n", "                            PetrolModel.data_details[0]['qty'],\n", "                            PetrolModel.data_details[0]['price'],\n", "                            PetrolModel.data_details[0]['product_id'],\n", "                            )\n", "                            # .filter(PetrolModel.datetime > six_months_ago)\n", "                            .statement,\n", "                con = session.bind)\n", "df20 = df20.rename(columns={\n", "    \"anon_1\": 'che_liang_id',\n", "    \"anon_2\": 'distant',\n", "    \"anon_3\": 'qty',\n", "    \"anon_4\": 'price',\n", "    \"anon_5\": 'product_id',\n", "    })\n", "# display(\"df20\", df20)\n", "\n", "df30 = pd.read_sql(sql = session.query(PetrolModel2)\\\n", "                        .with_entities(\n", "                            PetrolModel2.petrol_id,\n", "                            PetrolModel2.datetime,\n", "                            PetrolModel2.data_details[0]['che_liang_id'],\n", "                            PetrolModel2.data_details[0]['distant'],\n", "                            PetrolModel2.data_details[0]['qty'],\n", "                            PetrolModel2.data_details[0]['price'],\n", "                            PetrolModel2.data_details[0]['product_id'],\n", "                            )\n", "                            .filter( PetrolModel2.datetime > six_months_ago).statement,\n", "                con = session.bind)\n", "df30 = df30.rename(columns={\n", "    \"anon_1\": 'che_liang_id',\n", "    \"anon_2\": 'distant',\n", "    \"anon_3\": 'qty',\n", "    \"anon_4\": 'price',\n", "    \"anon_5\": 'product_id',\n", "    })\n", "\n", "# display(\"df30\", df30)\n", "\n", "\n", "df40 = pd.concat([df20, df30], ignore_index=True)\n", "df40 = df40.sort_values(['che_liang_id', 'datetime'], ascending=[True, True])\n", "# display(df40)\n", "\n", "# df50 = df40[df40['che_liang_id'] ==  104 ]\n", "# return che_liang_id 104 and 25371\n", "df50 = df40[(df40['che_liang_id'] ==  22085) | (df40['che_liang_id'] ==  22046)]\n", "\n", "\n", "# df50['previous_distant'] = df50.groupby('che_liang_id')['distant'].transform(lambda x : x.shift())\n", "# df50['distance_used'] = df50.groupby('che_liang_id').apply(lambda x : x['previous_distant'] if x['previous_distant'].any() == 0 else (x['distant'] if x['distant'].any() == 0 else x['distant'] - x['previous_distant'])).reset_index('che_liang_id',drop=True)\n", "# df50['previous_qty'] = df50.groupby('che_liang_id')['qty'].transform(lambda x : x.shift())\n", "# df50['km_lit'] = df50.groupby('che_liang_id').apply(lambda x : (x['distance_used'] / x['previous_qty'])  ).reset_index('che_liang_id',drop=True)\n", "# df50['count'] = 'calculate'\n", "\n", "df50['previous_distant'] = df50.groupby('che_liang_id')['distant'].transform(lambda x : None if x.any() == 0 else x.shift())\n", "df50['distance_used'] = df50.groupby('che_liang_id').apply(lambda x : x['previous_distant'] if x['previous_distant'].any() == 0 else (x['distant'] if x['distant'].any() == 0 else x['distant'] - x['previous_distant'])).reset_index('che_liang_id',drop=True)\n", "df50['mean_distance_used'] = df50.groupby('che_liang_id')['distance_used'].transform(lambda x : x.mean())\n", "df50['previous_qty'] = df50.groupby('che_liang_id').apply(lambda x : x['distance_used'] if x['distant'].any() == 0 else x['qty'].shift()).reset_index('che_liang_id',drop=True)\n", "df50['km_lit'] = df50.groupby('che_liang_id').apply(lambda x : (x['distance_used'] / x['previous_qty'])  ).reset_index('che_liang_id',drop=True)\n", "# df50['average'] = df50.groupby('che_liang_id')['km_lit'].transform(lambda x : x.mean())\n", "# df50['average_percent'] = df50.groupby('che_liang_id').apply(lambda x: x['km_lit'] if x['km_lit'].any() == 0 else ((x.distance_used - x.mean_distance_used) / x.mean_distance_used) * 100).reset_index('che_liang_id',drop=True)\n", "\n", "outlier_detector = IsolationForest(random_state=42)\n", "distance_used_fillna = df50[['distance_used']].fillna(0)\n", "outlier_detector.fit(distance_used_fillna)\n", "prediction = outlier_detector.predict(distance_used_fillna)\n", "prediction_strings = ['Outlier' if x < 0 else 'Standard' for x in prediction]\n", "df50['outlier_flag'] = prediction_strings\n", "\n", "df50['distance_used_2'] = df50.groupby('che_liang_id').apply(lambda x: None if x['outlier_flag'].empty == 'Outlier' else x['distance_used']).reset_index('che_liang_id',drop=True)\n", "df50['mean_distance_used_2'] = df50.groupby('che_liang_id')['distance_used_2'].transform(lambda x : x.mean())\n", "df50['km_lit_2'] = df50.groupby('che_liang_id').apply(lambda x: None if x['outlier_flag'].empty == 'Outlier' else x['distance_used'] / x['previous_qty']).reset_index('che_liang_id',drop=True)\n", "# df50['average_2'] = df50.groupby('che_liang_id')['km_lit_2'].transform(lambda x : x.mean())\n", "# df50['average_percent_2'] = df50.groupby('che_liang_id').apply(lambda x : (x.km_lit_2 - x.average_2) / x.average_2 * 100).reset_index('che_liang_id',drop=True)\n", "# df50['mean'] = df50.groupby('che_liang_id').apply(lambda x : ((x.average_2 - x.average) / x.average * 100)).reset_index('che_liang_id',drop=True)\n", "# df50['oneKmLit'] = 1 / df50['average']\n", "# df50['hundredKmLit'] = 100 / df50['average']\n", "# df50['minute'] = 60 * df50['average']\n", "# df50['che_liang_id_Count'] = df50.groupby('che_liang_id')['che_liang_id'].transform(lambda x : x.count())\n", "display(\"df50\", df50)\n", "\n", "\n", "# df60 = df50.set_index('datetime', inplace=True)\n", "# df60 = df50.groupby('che_liang_id').resample(\"MS\").agg({'qty': np.sum, 'price': np.mean, 'km_lit': np.mean, 'mean_distance_used': np.mean, 'count': np.size}).round(decimals = 1).fillna(\"\")\n", "# df60 = df50.groupby('che_liang_id').resample(\"MS\").agg({'distance_used': np.mean, \n", "#                                                         'km_lit': np.mean, \n", "#                                                         'average': lambda x: 1 / x.std()\n", "#                                                         }).round(decimals = 1).fillna(\"\")\n", "# display(\"df60\", df60)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%%time\n", "import warnings\n", "warnings.simplefilter('ignore')\n", "\n", "import pandas as pd\n", "import sqlalchemy\n", "from sqlalchemy import create_engine, tuple_\n", "from sqlalchemy.pool import NullPool\n", "import numpy as np\n", "import json\n", "from sqlalchemy.ext.declarative import declarative_base\n", "from sqlalchemy import Column, Integer, String, Date, DateTime, ForeignKey, TEXT\n", "from sqlalchemy.sql.expression import table, text\n", "from sqlalchemy.dialects.postgresql.json import JSONB\n", "from sqlalchemy.orm import sessionmaker\n", "from sqlalchemy import and_, or_, not_\n", "from sqlalchemy import literal\n", "import datetime\n", "import itertools\n", "from sklearn.ensemble import IsolationForest\n", "\n", "DATABASE_URL = \"*************************************************/test_nern\"\n", "engine = create_engine(DATABASE_URL, poolclass=NullPool)\n", "Session = sessionmaker(bind=engine)\n", "\n", "Base = declarative_base()\n", "\n", "class PetrolModel(Base):\n", "    __tablename__ = 'petrol'\n", "    auto_id = Column(Integer, primary_key=True)\n", "    petrol_id = Column(TEXT(convert_unicode=True), server_default=text(\"'petrol'||lpad(NEXTVAL('petrol_sequence'::regclass)::text,8, '0')\"))\n", "    datetime = Column(DateTime)\n", "    data_details = Column(JSONB, nullable=False, server_default='[]')\n", "    \n", "    \n", "class PetrolModel2(Base):\n", "    __tablename__ = 'petrol2'\n", "    auto_id = Column(Integer, primary_key=True)\n", "    petrol_id = Column(TEXT(convert_unicode=True), server_default=text(\"'petrol'||lpad(NEXTVAL('petrol_sequence'::regclass)::text,8, '0')\"))\n", "    datetime = Column(DateTime)\n", "    data_details = Column(JSONB, nullable=False, server_default='[]')\n", "    status_details = Column(JSONB, nullable=False, server_default='{}')    \n", "\n", "\n", "session = Session()\n", "\n", "today = datetime.date.today()\n", "future_day = today.day\n", "future_month = (today.month - 12) % 12\n", "future_year = today.year + ((today.month - 12) // 12)\n", "six_months_ago = datetime.date(future_year, future_month, future_day)\n", "\n", "df20 = pd.read_sql(sql = session.query(PetrolModel)\\\n", "                        .with_entities(\n", "                            PetrolModel.datetime,\n", "                            PetrolModel.data_details[0]['che_liang_id'],\n", "                            PetrolModel.data_details[0]['distant'],\n", "                            PetrolModel.data_details[0]['qty'],\n", "                            PetrolModel.data_details[0]['bill'],\n", "                            )\n", "                            # .filter(PetrolModel.datetime > six_months_ago)\n", "                            .statement,\n", "                con = session.bind)\n", "df20 = df20.rename(columns={\n", "    \"anon_1\": 'che_liang_id',\n", "    \"anon_2\": 'distant',\n", "    \"anon_3\": 'qty',\n", "    \"anon_4\": 'bill',\n", "    })\n", "# display(\"df20\", df20)\n", "\n", "df30 = pd.read_sql(sql = session.query(PetrolModel2)\\\n", "                        .with_entities(\n", "                            PetrolModel2.datetime,\n", "                            PetrolModel2.data_details[0]['che_liang_id'],\n", "                            PetrolModel2.data_details[0]['distant'],\n", "                            PetrolModel2.data_details[0]['qty'],\n", "                            )\n", "                            # .filter( PetrolModel2.datetime > six_months_ago)\n", "                            .statement,\n", "                con = session.bind)\n", "df30 = df30.rename(columns={\n", "    \"anon_1\": 'che_liang_id',\n", "    \"anon_2\": 'distant',\n", "    \"anon_3\": 'qty',\n", "    })\n", "\n", "# display(\"df30\", df30)\n", "\n", "\n", "df40 = pd.concat([df20, df30], ignore_index=True)\n", "df40 = df40.sort_values(['che_liang_id', 'datetime'], ascending=[True, True])\n", "# display(df40)\n", "\n", "# df50 = df40[df40['che_liang_id'] ==  20929 ]\n", "# return che_liang_id 104 and 25371\n", "# df50 = df40[(df40['che_liang_id'] ==  38755) | (df40['che_liang_id'] ==  20929)]\n", "df50 = df40[df40['che_liang_id'].isin([13417, 2343, 26594])]\n", "# df50 = df40\n", "\n", "df50['previous_distant'] = df50.groupby('che_liang_id')['distant'].transform(lambda x : x.shift())\n", "df50['distance_used'] = df50.groupby('che_liang_id').apply(lambda x: x['previous_distant'] if x['previous_distant'].any() == 0 else (x['distant'] if x['distant'].any() == 0 else x['distant'] - x['previous_distant'])).reset_index('che_liang_id',drop=True)\n", "df50['mean_distance_used'] = df50.groupby('che_liang_id')['distance_used'].transform(lambda x : x.mean())\n", "df50['previous_qty'] = df50.groupby('che_liang_id')['qty'].transform(lambda x : x.shift())    \n", "df50['km_lit'] = df50.groupby('che_liang_id').apply(lambda x : x['distance_used'] / x['previous_qty']  ).reset_index('che_liang_id',drop=True)  \n", "df50['average'] = df50.groupby('che_liang_id')['km_lit'].transform(lambda x : x.mean()) \n", "df50['average_percent'] = df50.groupby('che_liang_id').apply(lambda x: x['km_lit'] if x['km_lit'].any() == 0 else ((x.distance_used - x.mean_distance_used) / x.mean_distance_used) * 100).reset_index('che_liang_id',drop=True) \n", "\n", "def train_isolation_group(pass_distance_used):\n", "    outlier_detector = IsolationForest(random_state=42)\n", "    np_scaled = pass_distance_used.values.reshape(-1, 1)\n", "    distance_used_fillna = pd.DataFrame(np_scaled).fillna(0)\n", "    outlier_detector.fit(distance_used_fillna)\n", "    prediction = outlier_detector.predict(distance_used_fillna)\n", "    prediction_strings = ['Outlier' if x < 0 else 'Standard' for x in prediction]\n", "    return prediction_strings\n", "df50['outlier_flag'] = df50.groupby('che_liang_id')['distance_used'].transform(train_isolation_group)\n", "\n", "df50['distance_used_2'] = df50.apply(lambda x: None if x['outlier_flag'] == 'Outlier' else x['distance_used'], axis=1)\n", "df50['mean_distance_used_2'] = df50.groupby('che_liang_id')['distance_used_2'].transform(lambda x : x.mean())\n", "df50['km_lit_2'] = df50.apply(lambda x: None if x['outlier_flag'] == 'Outlier' else x['distance_used'] / x['previous_qty'], axis=1)\n", "df50['average_2'] = df50.groupby('che_liang_id')['km_lit_2'].transform(lambda x : x.mean())\n", "df50['average_percent_2'] = df50.groupby('che_liang_id').apply(lambda x: x['km_lit_2'] if x['km_lit_2'].any() == None else ((x.distance_used - x.mean_distance_used_2) / x.mean_distance_used_2) * 100).reset_index('che_liang_id',drop=True) \n", "df50['mean'] = df50.groupby('che_liang_id').apply(lambda x: (x.mean_distance_used - x.mean_distance_used_2) / x.mean_distance_used_2 * 100).reset_index('che_liang_id',drop=True) \n", "\n", "\n", "\n", "# pd.set_option('display.max_rows', 500)\n", "# df50.head(500)\n", "# display(\"df50\", df50)\n", "\n", "\n", "def sumfunc_value(x):\n", "    qty = x['qty'].mean()\n", "    distance_used = x['distance_used'].mean()\n", "    km_lit = x['km_lit'].mean()\n", "    distance_used_2 = x['distance_used_2'].mean()\n", "    km_lit_2 = x['km_lit_2'].mean()\n", "    mean = (x['distance_used_2'].mean() - x['distance_used'].mean()) / x['distance_used'].mean() * 100\n", "    minute = 60 * x['km_lit_2'].mean()  \n", "    oneKmLit =  x['distance_used_2'].mean() if x['distance_used_2'].any() == 0  else x['qty'].mean() / x['distance_used_2'].mean()\n", "    hundredKmLit = x['km_lit_2'].mean() if x['km_lit_2'].any() == 0  else 100 / x['km_lit_2'].mean()\n", "    count = x['che_liang_id'].count()\n", "    return pd.Series([qty, distance_used, km_lit, distance_used_2, km_lit_2, mean, minute, oneKmLit, hundredKmLit, count], \n", "                        index=['qty', 'distance_used', 'km_lit', 'distance_used_2', 'km_lit_2', 'mean', 'minute', 'oneKmLit', 'hundredKmLit', 'count'])\n", "df60 = df50.groupby(['che_liang_id', 'bill', pd.Grouper(key='datetime', freq='MS')]).apply(sumfunc_value).reset_index().round(decimals = 1).fillna(0)\n", "\n", "# display(\"df60\", df60)\n", "\n", "def train_isolation_group(pass_distance_used):\n", "    outlier_detector = IsolationForest(random_state=42)\n", "    np_scaled = pass_distance_used.values.reshape(-1, 1)\n", "    distance_used_fillna = pd.DataFrame(np_scaled).fillna(0)\n", "    outlier_detector.fit(distance_used_fillna)\n", "    prediction = outlier_detector.predict(distance_used_fillna)\n", "    prediction_strings = ['Outlier' if x < 0 else 'Standard' for x in prediction]\n", "    return prediction_strings\n", "df60['outlier_flag'] = df60.groupby('che_liang_id')['count'].transform(train_isolation_group)\n", "df60['outlier_flag_dis2'] = df60.apply(lambda x: None if x['outlier_flag'] == 'Outlier' else x['distance_used_2'], axis=1)\n", "df60['mean_distance_used_2'] = df60.groupby('che_liang_id')['outlier_flag_dis2'].transform(lambda x : x.mean())\n", "df60['average_2'] = df60.groupby('che_liang_id').apply(lambda x: (x.distance_used_2 - x.mean_distance_used_2) / x.mean_distance_used_2 * 100).reset_index('che_liang_id',drop=True) \n", "# aaa = df60.groupby('che_liang_id')['average_2'].transform('last') >= 1\n", "df60 = df60.groupby('che_liang_id').nth(-2).reset_index() \n", "display(\"df60\", df60)\n", "\n", "# display(\"df60\", df60)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "d = {'petrol_id': ['petroltest100', '', '', 'petroltest2000', ''], \n", "     'datetime': ['3000-03-26 13:29:06.333246', '3000-03-26 13:29:06.333246', '3000-03-26 13:29:06.333246', '3000-03-26 13:29:06.333246'], \n", "     'che_liang_id': [999999, 999999, 999999, 888888],\n", "     'distant': [410, 570, 700, 5000],\n", "     'qty': [20, 20, 20, 556],\n", "     'bill': ['', '', '', '']\n", "    }\n", "df31 = pd.DataFrame(data=d)\n", "df31"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "d = {'id': ['101', '101', '101', '105', '105', '105', '106', '106', '106'], \n", "     'qty': [10, -2, 20, 23 ,10, 50, -3, 50, -12],\n", "    }\n", "df31 = pd.DataFrame(data=d)\n", "aaa = df31.groupby('id')['qty'].transform('shift') >= 1\n", "display(df31[~aaa])\n", "df32 = df31.to_dict('records')\n", "df32"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "d = {'id': ['101', '101', '101'], \n", "     'qty': [10, -2, 20],\n", "    }\n", "df31 = pd.DataFrame(data=d)\n", "df31\n", "display(df31)\n", "df32 = df31.to_dict('records')\n", "df32"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%%time\n", "import warnings\n", "warnings.simplefilter('ignore')\n", "\n", "import pandas as pd\n", "import sqlalchemy\n", "from sqlalchemy import create_engine, tuple_\n", "from sqlalchemy.pool import NullPool\n", "import numpy as np\n", "import json\n", "from sqlalchemy.ext.declarative import declarative_base\n", "from sqlalchemy import Column, Integer, String, Date, DateTime, ForeignKey, TEXT\n", "from sqlalchemy.sql.expression import table, text\n", "from sqlalchemy.dialects.postgresql.json import JSONB\n", "from sqlalchemy.orm import sessionmaker\n", "from sqlalchemy import and_, or_, not_\n", "from sqlalchemy import literal\n", "import datetime\n", "import itertools\n", "from sklearn.ensemble import IsolationForest\n", "\n", "DATABASE_URL = \"*************************************************/n_data\"\n", "engine = create_engine(DATABASE_URL, poolclass=NullPool)\n", "Session = sessionmaker(bind=engine)\n", "\n", "Base = declarative_base()\n", "\n", "class PetrolModel(Base):\n", "    __tablename__ = 'petrol'\n", "    auto_id = Column(Integer, primary_key=True)\n", "    petrol_id = Column(TEXT(convert_unicode=True), server_default=text(\"'petrol'||lpad(NEXTVAL('petrol_sequence'::regclass)::text,8, '0')\"))\n", "    datetime = Column(DateTime)\n", "    data_details = Column(JSONB, nullable=False, server_default='[]')\n", "    \n", "    \n", "class PetrolModel2(Base):\n", "    __tablename__ = 'petrol2'\n", "    auto_id = Column(Integer, primary_key=True)\n", "    petrol_id = Column(TEXT(convert_unicode=True), server_default=text(\"'petrol'||lpad(NEXTVAL('petrol_sequence'::regclass)::text,8, '0')\"))\n", "    datetime = Column(DateTime)\n", "    data_details = Column(JSONB, nullable=False, server_default='[]')\n", "    status_details = Column(JSONB, nullable=False, server_default='{}')    \n", "\n", "\n", "session = Session()\n", "\n", "\n", "\n", "today = datetime.date.today()\n", "future_day = today.day\n", "future_month = (today.month - 3) % 12\n", "future_year = today.year + ((today.month - 3) // 12)\n", "six_months_ago = datetime.date(future_year, future_month, future_day)\n", "\n", "\n", "df20 = pd.read_sql(sql = session.query(PetrolModel)\\\n", "                        .with_entities(\n", "                            PetrolModel.petrol_id,\n", "                            PetrolModel.datetime,\n", "                            PetrolModel.data_details[0]['che_liang_id'],\n", "                            PetrolModel.data_details[0]['distant'],\n", "                            PetrolModel.data_details[0]['qty'],\n", "                            )\n", "                            .filter(PetrolModel.datetime > six_months_ago).statement,\n", "                con = session.bind)\n", "\n", "\n", "df20 = df20.rename(columns={\n", "    \"anon_1\": 'che_liang_id',\n", "    \"anon_2\": 'distant',\n", "    \"anon_3\": 'qty',\n", "    })\n", "\n", "# display(\"df20\", df20)\n", "\n", "\n", "df30 = pd.read_sql(sql = session.query(PetrolModel2)\\\n", "                        .with_entities(\n", "                            PetrolModel2.petrol_id,\n", "                            PetrolModel2.datetime,\n", "                            PetrolModel2.data_details[0]['che_liang_id'],\n", "                            PetrolModel2.data_details[0]['distant'],\n", "                            PetrolModel2.data_details[0]['qty'],\n", "                            )\n", "                            .filter( PetrolModel2.datetime > six_months_ago).statement,\n", "                con = session.bind)\n", "\n", "\n", "df30 = df30.rename(columns={\n", "    \"anon_1\": 'che_liang_id',\n", "    \"anon_2\": 'distant',\n", "    \"anon_3\": 'qty',\n", "    })\n", "\n", "# display(\"df30\", df30)\n", "\n", "\n", "df40 = pd.concat([df20, df30], ignore_index=True)\n", "df40 = df40.sort_values(['datetime'], ascending=[True])\n", "# display(df40)\n", "\n", "\n", "df50 = df40[df40['che_liang_id'] ==  \t37710 ]\n", "df50['previous_distant'] = df50['distant'].shift()\n", "df50['distance_used'] = df50.apply(lambda x: x['previous_distant'] if x['previous_distant'] == 0 else (x['distant'] if x['distant'] == 0 else x['distant'] - x['previous_distant']), axis=1)\n", "df50['mean_distance_used'] = df50['distance_used'].mean()\n", "df50['previous_qty'] = df50['qty'].shift()\n", "df50['count'] = df50['distance_used']\n", "df50['km_lit'] = (df50['distance_used'] / df50['previous_qty'])         \n", "df50['average'] = df50[df50['km_lit'] != 0][\"km_lit\"].mean()\n", "df50['average_percent'] = df50.apply(lambda x: x['km_lit'] if x['km_lit'] == 0 else x.mean_distance_used and ((x.distance_used - x.mean_distance_used) / x.mean_distance_used) * 100, axis=1)\n", "\n", "outlier_detector = IsolationForest(random_state=42)\n", "distance_used_fillna = df50[['distance_used']].fillna(0)\n", "outlier_detector.fit(distance_used_fillna)\n", "prediction = outlier_detector.predict(distance_used_fillna)\n", "prediction_strings = ['Outlier' if x < 0 else 'Standard' for x in prediction]\n", "df50['outlier_flag'] = prediction_strings\n", "\n", "df50['distance_used_2'] = df50.apply(lambda x: None if x['outlier_flag'] == 'Outlier' else x['distance_used'], axis=1)\n", "df50['mean_distance_used_2'] = df50['distance_used_2'].mean()\n", "df50['km_lit_2'] = df50.apply(lambda x: None if x['outlier_flag'] == 'Outlier' else x['distance_used'] / x['previous_qty'], axis=1)\n", "df50['average_2'] = df50[df50['km_lit_2'] != 0][\"km_lit_2\"].mean()\n", "df50['average_percent_2'] = df50.apply(lambda x: x['km_lit_2'] if x['km_lit_2'] == None else x.mean_distance_used and ((x.distance_used - x.mean_distance_used_2) / x.mean_distance_used_2) * 100, axis=1)\n", "df50['mean'] = ((df50.mean_distance_used - df50.mean_distance_used_2) / df50.mean_distance_used_2) * 100\n", "\n", "df60 = df50.pivot_table(columns=['distance_used'], aggfunc='size')\n", "df60 = pd.DataFrame(df60)\n", "df60 = df60.rename(columns={0: 'countoo'}).reset_index()\n", "df50['count'] = df50[['count']].replace(to_replace =df60['distance_used'].to_list(), value =df60['countoo'].to_list())\n", "\n", "\n", "df50 = df50[['datetime', 'distant', 'qty', 'distance_used', 'mean_distance_used_2', 'km_lit', 'average', 'average_2', 'previous_qty']]\n", "\n", "outlier_detector = IsolationForest(random_state=42)\n", "distance_used_fillna = df50[['km_lit']].fillna(0)\n", "outlier_detector.fit(distance_used_fillna)\n", "prediction = outlier_detector.predict(distance_used_fillna)\n", "prediction_strings = ['Outlier' if x < 0 else 'Standard' for x in prediction]\n", "df50['outlier_flag_km'] = prediction_strings\n", "\n", "df50['kmAi'] = df50.apply(lambda x: None if x['outlier_flag_km'] == 'Outlier' else x['km_lit'], axis=1)\n", "df50['meanKm'] = df50['kmAi'].mean()\n", "# df50['aaa'] = 2.2\n", "df50['pre'] = df50['meanKm'] * df50['previous_qty']   \n", "df50['remainKm'] = df50.apply(lambda x: x['distance_used'] if x['distance_used'] == 0 else x.distance_used - x.pre, axis=1) \n", "df50['remainLit'] = df50['remainKm'] / df50['meanKm'] \n", "df50['lostLit'] = df50['remainLit'].sum() \n", "display(df50)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%%time\n", "import warnings\n", "warnings.simplefilter('ignore')\n", "\n", "import pandas as pd\n", "import sqlalchemy\n", "from sqlalchemy import create_engine, tuple_\n", "from sqlalchemy.pool import NullPool\n", "import numpy as np\n", "import json\n", "from sqlalchemy.ext.declarative import declarative_base\n", "from sqlalchemy import Column, Integer, String, Date, DateTime, ForeignKey, TEXT\n", "from sqlalchemy.sql.expression import table, text\n", "from sqlalchemy.dialects.postgresql.json import JSONB\n", "from sqlalchemy.orm import sessionmaker\n", "from sqlalchemy import and_, or_, not_\n", "from sqlalchemy import literal\n", "import datetime\n", "import itertools\n", "from sklearn.ensemble import IsolationForest\n", "\n", "DATABASE_URL = \"*************************************************/test_nern\"\n", "engine = create_engine(DATABASE_URL, poolclass=NullPool)\n", "Session = sessionmaker(bind=engine)\n", "\n", "Base = declarative_base()\n", "\n", "class PetrolModel(Base):\n", "    __tablename__ = 'petrol'\n", "    auto_id = Column(Integer, primary_key=True)\n", "    petrol_id = Column(TEXT(convert_unicode=True), server_default=text(\"'petrol'||lpad(NEXTVAL('petrol_sequence'::regclass)::text,8, '0')\"))\n", "    datetime = Column(DateTime)\n", "    data_details = Column(JSONB, nullable=False, server_default='[]')\n", "    \n", "    \n", "class PetrolModel2(Base):\n", "    __tablename__ = 'petrol2'\n", "    auto_id = Column(Integer, primary_key=True)\n", "    petrol_id = Column(TEXT(convert_unicode=True), server_default=text(\"'petrol'||lpad(NEXTVAL('petrol_sequence'::regclass)::text,8, '0')\"))\n", "    datetime = Column(DateTime)\n", "    data_details = Column(JSONB, nullable=False, server_default='[]')\n", "    status_details = Column(JSONB, nullable=False, server_default='{}')    \n", "\n", "\n", "session = Session()\n", "\n", "\n", "\n", "today = datetime.date.today()\n", "future_day = today.day\n", "future_month = (today.month - 3) % 12\n", "future_year = today.year + ((today.month - 3) // 12)\n", "six_months_ago = datetime.date(future_year, future_month, future_day)\n", "\n", "\n", "df20 = pd.read_sql(sql = session.query(PetrolModel)\\\n", "                        .with_entities(\n", "                            PetrolModel.petrol_id,\n", "                            PetrolModel.datetime,\n", "                            PetrolModel.data_details[0]['che_liang_id'],\n", "                            PetrolModel.data_details[0]['distant'],\n", "                            PetrolModel.data_details[0]['qty'],\n", "                            )\n", "                            .filter(PetrolModel.datetime > six_months_ago).statement,\n", "                con = session.bind)\n", "\n", "\n", "df20 = df20.rename(columns={\n", "    \"anon_1\": 'che_liang_id',\n", "    \"anon_2\": 'distant',\n", "    \"anon_3\": 'qty',\n", "    })\n", "\n", "# display(\"df20\", df20)\n", "\n", "\n", "df30 = pd.read_sql(sql = session.query(PetrolModel2)\\\n", "                        .with_entities(\n", "                            PetrolModel2.petrol_id,\n", "                            PetrolModel2.datetime,\n", "                            PetrolModel2.data_details[0]['che_liang_id'],\n", "                            PetrolModel2.data_details[0]['distant'],\n", "                            PetrolModel2.data_details[0]['qty'],\n", "                            )\n", "                            .filter( PetrolModel2.datetime > six_months_ago).statement,\n", "                con = session.bind)\n", "\n", "\n", "df30 = df30.rename(columns={\n", "    \"anon_1\": 'che_liang_id',\n", "    \"anon_2\": 'distant',\n", "    \"anon_3\": 'qty',\n", "    })\n", "\n", "# display(\"df30\", df30)\n", "\n", "\n", "df40 = pd.concat([df20, df30], ignore_index=True)\n", "df40 = df40.sort_values(['datetime'], ascending=[True])\n", "# display(df40)\n", "\n", "\n", "df50 = df40[df40['che_liang_id'] ==  29164 ]\n", "df50['previous_distant'] = df50['distant'].shift()\n", "df50['distance_used'] = df50.apply(lambda x: x['previous_distant'] if x['previous_distant'] == 0 else (x['distant'] if x['distant'] == 0 else x['distant'] - x['previous_distant']), axis=1)\n", "df50['mean_distance_used'] = df50['distance_used'].mean()\n", "df50['previous_qty'] = df50['qty'].shift()\n", "df50['count'] = df50['distance_used']\n", "df50['km_lit'] = (df50['distance_used'] / df50['previous_qty'])         \n", "df50['average'] = df50[df50['km_lit'] != 0][\"km_lit\"].mean()\n", "df50['average_percent'] = df50.apply(lambda x: x['km_lit'] if x['km_lit'] == 0 else x.mean_distance_used and ((x.distance_used - x.mean_distance_used) / x.mean_distance_used) * 100, axis=1)\n", "\n", "outlier_detector = IsolationForest(random_state=42)\n", "distance_used_fillna = df50[['km_lit']].fillna(0)\n", "outlier_detector.fit(distance_used_fillna)\n", "prediction = outlier_detector.predict(distance_used_fillna)\n", "prediction_strings = ['Outlier' if x < 0 else 'Standard' for x in prediction]\n", "df50['outlier_flag'] = prediction_strings\n", "\n", "df50['km_lit_2'] = df50.apply(lambda x: None if x['outlier_flag'] == 'Outlier' else x['km_lit'], axis=1)\n", "df50['average_2'] = df50[df50['km_lit_2'] != 0][\"km_lit_2\"].mean()\n", "df50['mean_distance_used_2'] = (df50['average_2'] * df50['previous_qty']) + 0.01\n", "df50['average_percent_2'] = df50.apply(lambda x: (x.distance_used - x.mean_distance_used_2) / x.mean_distance_used_2 * 100, axis=1)\n", "df50['mean'] = ((df50.average_2 - df50.average) / df50.average) * 100\n", "\n", "# df50['oneKmLit'] = 1 / df50['average_2']\n", "# df50['hundredKmLit'] = 100 / df50['average_2']\n", "# df50['minute'] = 60 * df50['average_2']\n", "# df50['kmLitStd'] = df50['km_lit'].std()\n", "\n", "df60 = df50.pivot_table(columns=['distance_used'], aggfunc='size')\n", "df60 = pd.DataFrame(df60)\n", "df60 = df60.rename(columns={0: 'countoo'}).reset_index()\n", "df50['count'] = df50[['count']].replace(to_replace =df60['distance_used'].to_list(), value =df60['countoo'].to_list())\n", "# qty_list = df50['qty'].astype(float).round(decimals = 1)\n", "# uniqleQtyToList = qty_list.unique().tolist()\n", "# df50['uniqleQty'] = str(uniqleQtyToList)\n", "# df50['FirstuniqleQty'] = str(uniqleQtyToList[0])\n", "# df50['LastuniqleQty'] = str(uniqleQtyToList[-1])\n", "\n", "# df50['remainKm'] = df50.apply(lambda x: x['distance_used'] if x['distance_used'] == 0 else x.distance_used - x.mean_distance_used_2, axis=1) \n", "# df50['remainLit'] = df50['remainKm'] / df50['average_2'] \n", "# df50['lostLit'] = df50['remainLit'].sum()\n", " \n", "\n", "\n", "\n", "\n", "# df50 = df50[['petrol_id', 'che_liang_id', 'datetime', 'distant', 'qty', 'previous_qty', 'distance_used', 'mean_distance_used', 'km_lit', \n", "#              'average', 'average_percent', 'outlier_flag', 'km_lit_2', 'average_2', 'mean_distance_used_2', 'average_percent_2', 'mean', 'count',\n", "#              'uniqleQty', 'FirstuniqleQty', 'LastuniqleQty']]\n", "\n", "\n", "\n", "\n", "\n", "display(df50)"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"ename": "SyntaxError", "evalue": "invalid syntax (<unknown>, line 102)", "output_type": "error", "traceback": ["Traceback \u001b[0;36m(most recent call last)\u001b[0m:\n", "  File \u001b[1;32m\"/home/<USER>/.local/lib/python3.8/site-packages/IPython/core/interactiveshell.py\"\u001b[0m, line \u001b[1;32m3457\u001b[0m, in \u001b[1;35mrun_code\u001b[0m\n    exec(code_obj, self.user_global_ns, self.user_ns)\n", "  File \u001b[1;32m\"/tmp/ipykernel_176367/3006291515.py\"\u001b[0m, line \u001b[1;32m1\u001b[0m, in \u001b[1;35m<module>\u001b[0m\n    get_ipython().run_cell_magic('time', '', 'import warnings\\nwarnings.simplefilter(\\'ignore\\')\\n\\nimport pandas as pd\\nimport sqlalchemy\\nfrom sqlalchemy import create_engine, tuple_\\nfrom sqlalchemy.pool import NullPool\\nimport numpy as np\\nimport json\\nfrom sqlalchemy.ext.declarative import declarative_base\\nfrom sqlalchemy import Column, Integer, String, Date, DateTime, ForeignKey, TEXT\\nfrom sqlalchemy.sql.expression import table, text\\nfrom sqlalchemy.dialects.postgresql.json import JSONB\\nfrom sqlalchemy.orm import sessionmaker\\nfrom sqlalchemy import and_, or_, not_\\nfrom sqlalchemy import literal\\nimport datetime\\nimport itertools\\nfrom sklearn.ensemble import IsolationForest\\n\\nDATABASE_URL = \"*************************************************/test_nern\"\\nengine = create_engine(DATABASE_URL, poolclass=NullPool)\\nSession = sessionmaker(bind=engine)\\n\\nBase = declarative_base()\\n\\nclass PetrolModel(Base):\\n    __tablename__ = \\'petrol\\'\\n    auto_id = Column(Integer, primary_key=True)\\n    petrol_id = Column(TEXT(convert_unicode=True), server_default=text(\"\\'petrol\\'||lpad(NEXTVAL(\\'petrol_sequence\\'::regclass)::text,8, \\'0\\')\"))\\n    datetime = Column(DateTime)\\n    data_details = Column(JSONB, nullable=False, server_default=\\'[]\\')\\n    \\n    \\nclass PetrolModel2(Base):\\n    __tablename__ = \\'petrol2\\'\\n    auto_id = Column(Integer, primary_key=True)\\n    petrol_id = Column(TEXT(convert_unicode=True), server_default=text(\"\\'petrol\\'||lpad(NEXTVAL(\\'petrol_sequence\\'::regclass)::text,8, \\'0\\')\"))\\n    datetime = Column(DateTime)\\n    data_details = Column(JSONB, nullable=False, server_default=\\'[]\\')\\n    status_details = Column(JSONB, nullable=False, server_default=\\'{}\\')    \\n\\n\\nsession = Session()\\n\\n\\n\\ntoday = datetime.date.today()\\nfuture_day = today.day\\nfuture_month = (today.month - 3) % 12\\nfuture_year = today.year + ((today.month - 3) // 12)\\nsix_months_ago = datetime.date(future_year, future_month, future_day)\\n\\n\\ndf20 = pd.read_sql(sql = session.query(PetrolModel)\\\\\\n                        .with_entities(\\n                            PetrolModel.petrol_id,\\n                            PetrolModel.datetime,\\n                            PetrolModel.data_details[0][\\'che_liang_id\\'],\\n                            PetrolModel.data_details[0][\\'distant\\'],\\n                            PetrolModel.data_details[0][\\'qty\\'],\\n                            )\\n                            .filter(PetrolModel.datetime > six_months_ago).statement,\\n                con = session.bind)\\n\\n\\ndf20 = df20.rename(columns={\\n    \"anon_1\": \\'che_liang_id\\',\\n    \"anon_2\": \\'distant\\',\\n    \"anon_3\": \\'qty\\',\\n    })\\n\\n# display(\"df20\", df20)\\n\\n\\ndf30 = pd.read_sql(sql = session.query(PetrolModel2)\\\\\\n                        .with_entities(\\n                            PetrolModel2.petrol_id,\\n                            PetrolModel2.datetime,\\n                            PetrolModel2.data_details[0][\\'che_liang_id\\'],\\n                            PetrolModel2.data_details[0][\\'distant\\'],\\n                            PetrolModel2.data_details[0][\\'qty\\'],\\n                            )\\n                            .filter( PetrolModel2.datetime > six_months_ago).statement,\\n                con = session.bind)\\n\\n\\ndf30 = df30.rename(columns={\\n    \"anon_1\": \\'che_liang_id\\',\\n    \"anon_2\": \\'distant\\',\\n    \"anon_3\": \\'qty\\',\\n    })\\n\\n# display(\"df30\", df30)\\n\\n\\ndf40 = pd.concat([df20, df30], ignore_index=True)\\ndf40 = df40.sort_values([\\'datetime\\'], ascending=[True])\\n# display(df40)\\n\\n\\ndf50 = df40[df40[\\'che_liang_id\\'] ==  V.18 ]\\ndf50[\\'previous_distant\\'] = df50[\\'distant\\'].shift()\\ndf50[\\'distance_used\\'] = df50.apply(lambda x: x[\\'previous_distant\\'] if x[\\'previous_distant\\'] == 0 else (x[\\'distant\\'] if x[\\'distant\\'] == 0 else x[\\'distant\\'] - x[\\'previous_distant\\']), axis=1)\\ndf50[\\'mean_distance_used\\'] = df50[\\'distance_used\\'].mean()\\ndf50[\\'previous_qty\\'] = df50[\\'qty\\'].shift()\\ndf50[\\'count\\'] = df50[\\'distance_used\\']\\ndf50[\\'km_lit\\'] = (df50[\\'distance_used\\'] / df50[\\'previous_qty\\'])         \\ndf50[\\'average\\'] = df50[df50[\\'km_lit\\'] != 0][\"km_lit\"].mean()\\ndf50[\\'average_percent\\'] = df50.apply(lambda x: x[\\'km_lit\\'] if x[\\'km_lit\\'] == 0 else x.mean_distance_used and ((x.distance_used - x.mean_distance_used) / x.mean_distance_used) * 100, axis=1)\\n\\noutlier_detector = IsolationForest(random_state=42)\\ndistance_used_fillna = df50[[\\'km_lit\\']].fillna(0)\\noutlier_detector.fit(distance_used_fillna)\\nprediction = outlier_detector.predict(distance_used_fillna)\\nprediction_strings = [\\'Outlier\\' if x < 0 else \\'Standard\\' for x in prediction]\\ndf50[\\'outlier_flag\\'] = prediction_strings\\n\\ndf50[\\'km_lit_2\\'] = df50.apply(lambda x: None if x[\\'outlier_flag\\'] == \\'Outlier\\' else x[\\'km_lit\\'], axis=1)\\ndf50[\\'average_2\\'] = df50[df50[\\'km_lit_2\\'] != 0][\"km_lit_2\"].mean()\\ndf50[\\'mean_distance_used_2\\'] = (df50[\\'average_2\\'] * df50[\\'previous_qty\\']) + 0.01\\ndf50[\\'average_percent_2\\'] = df50.apply(lambda x: (x.distance_used - x.mean_distance_used_2) / x.mean_distance_used_2 * 100, axis=1)\\ndf50[\\'mean\\'] = ((df50.average_2 - df50.average) / df50.average) * 100\\n\\ndf60 = df50.pivot_table(columns=[\\'distance_used\\'], aggfunc=\\'size\\')\\ndf60 = pd.DataFrame(df60)\\ndf60 = df60.rename(columns={0: \\'countoo\\'}).reset_index()\\ndf50[\\'count\\'] = df50[[\\'count\\']].replace(to_replace =df60[\\'distance_used\\'].to_list(), value =df60[\\'countoo\\'].to_list())\\n\\ndisplay(df50)\\n')\n", "  File \u001b[1;32m\"/home/<USER>/.local/lib/python3.8/site-packages/IPython/core/interactiveshell.py\"\u001b[0m, line \u001b[1;32m2419\u001b[0m, in \u001b[1;35mrun_cell_magic\u001b[0m\n    result = fn(*args, **kwargs)\n", "  File \u001b[1;32m\"/home/<USER>/.local/lib/python3.8/site-packages/decorator.py\"\u001b[0m, line \u001b[1;32m232\u001b[0m, in \u001b[1;35mfun\u001b[0m\n    return caller(func, *(extras + args), **kw)\n", "  File \u001b[1;32m\"/home/<USER>/.local/lib/python3.8/site-packages/IPython/core/magic.py\"\u001b[0m, line \u001b[1;32m187\u001b[0m, in \u001b[1;35m<lambda>\u001b[0m\n    call = lambda f, *a, **k: f(*a, **k)\n", "  File \u001b[1;32m\"/home/<USER>/.local/lib/python3.8/site-packages/IPython/core/magics/execution.py\"\u001b[0m, line \u001b[1;32m1291\u001b[0m, in \u001b[1;35mtime\u001b[0m\n    expr_ast = self.shell.compile.ast_parse(expr)\n", "\u001b[0;36m  File \u001b[0;32m\"/home/<USER>/.local/lib/python3.8/site-packages/IPython/core/compilerop.py\"\u001b[0;36m, line \u001b[0;32m101\u001b[0;36m, in \u001b[0;35mast_parse\u001b[0;36m\u001b[0m\n\u001b[0;31m    return compile(source, filename, symbol, self.flags | PyCF_ONLY_AST, 1)\u001b[0m\n", "\u001b[0;36m  File \u001b[0;32m\"<unknown>\"\u001b[0;36m, line \u001b[0;32m102\u001b[0m\n\u001b[0;31m    df50 = df40[df40['che_liang_id'] ==  V.18 ]\u001b[0m\n\u001b[0m                                          ^\u001b[0m\n\u001b[0;31mSyntaxError\u001b[0m\u001b[0;31m:\u001b[0m invalid syntax\n"]}], "source": ["%%time\n", "import warnings\n", "warnings.simplefilter('ignore')\n", "\n", "import pandas as pd\n", "import sqlalchemy\n", "from sqlalchemy import create_engine, tuple_\n", "from sqlalchemy.pool import NullPool\n", "import numpy as np\n", "import json\n", "from sqlalchemy.ext.declarative import declarative_base\n", "from sqlalchemy import Column, Integer, String, Date, DateTime, ForeignKey, TEXT\n", "from sqlalchemy.sql.expression import table, text\n", "from sqlalchemy.dialects.postgresql.json import JSONB\n", "from sqlalchemy.orm import sessionmaker\n", "from sqlalchemy import and_, or_, not_\n", "from sqlalchemy import literal\n", "import datetime\n", "import itertools\n", "from sklearn.ensemble import IsolationForest\n", "\n", "DATABASE_URL = \"*************************************************/test_nern\"\n", "engine = create_engine(DATABASE_URL, poolclass=NullPool)\n", "Session = sessionmaker(bind=engine)\n", "\n", "Base = declarative_base()\n", "\n", "class PetrolModel(Base):\n", "    __tablename__ = 'petrol'\n", "    auto_id = Column(Integer, primary_key=True)\n", "    petrol_id = Column(TEXT(convert_unicode=True), server_default=text(\"'petrol'||lpad(NEXTVAL('petrol_sequence'::regclass)::text,8, '0')\"))\n", "    datetime = Column(DateTime)\n", "    data_details = Column(JSONB, nullable=False, server_default='[]')\n", "    \n", "    \n", "class PetrolModel2(Base):\n", "    __tablename__ = 'petrol2'\n", "    auto_id = Column(Integer, primary_key=True)\n", "    petrol_id = Column(TEXT(convert_unicode=True), server_default=text(\"'petrol'||lpad(NEXTVAL('petrol_sequence'::regclass)::text,8, '0')\"))\n", "    datetime = Column(DateTime)\n", "    data_details = Column(JSONB, nullable=False, server_default='[]')\n", "    status_details = Column(JSONB, nullable=False, server_default='{}')    \n", "\n", "\n", "session = Session()\n", "\n", "\n", "\n", "today = datetime.date.today()\n", "future_day = today.day\n", "future_month = (today.month - 3) % 12\n", "future_year = today.year + ((today.month - 3) // 12)\n", "six_months_ago = datetime.date(future_year, future_month, future_day)\n", "\n", "\n", "df20 = pd.read_sql(sql = session.query(PetrolModel)\\\n", "                        .with_entities(\n", "                            PetrolModel.petrol_id,\n", "                            PetrolModel.datetime,\n", "                            PetrolModel.data_details[0]['che_liang_id'],\n", "                            PetrolModel.data_details[0]['distant'],\n", "                            PetrolModel.data_details[0]['qty'],\n", "                            )\n", "                            .filter(PetrolModel.datetime > six_months_ago).statement,\n", "                con = session.bind)\n", "\n", "\n", "df20 = df20.rename(columns={\n", "    \"anon_1\": 'che_liang_id',\n", "    \"anon_2\": 'distant',\n", "    \"anon_3\": 'qty',\n", "    })\n", "\n", "# display(\"df20\", df20)\n", "\n", "\n", "df30 = pd.read_sql(sql = session.query(PetrolModel2)\\\n", "                        .with_entities(\n", "                            PetrolModel2.petrol_id,\n", "                            PetrolModel2.datetime,\n", "                            PetrolModel2.data_details[0]['che_liang_id'],\n", "                            PetrolModel2.data_details[0]['distant'],\n", "                            PetrolModel2.data_details[0]['qty'],\n", "                            )\n", "                            .filter( PetrolModel2.datetime > six_months_ago).statement,\n", "                con = session.bind)\n", "\n", "\n", "df30 = df30.rename(columns={\n", "    \"anon_1\": 'che_liang_id',\n", "    \"anon_2\": 'distant',\n", "    \"anon_3\": 'qty',\n", "    })\n", "\n", "# display(\"df30\", df30)\n", "\n", "\n", "df40 = pd.concat([df20, df30], ignore_index=True)\n", "df40 = df40.sort_values(['datetime'], ascending=[True])\n", "# display(df40)\n", "\n", "\n", "df50 = df40[df40['che_liang_id'] ==  \t26594 ]\n", "df50['previous_distant'] = df50['distant'].shift()\n", "df50['distance_used'] = df50.apply(lambda x: x['previous_distant'] if x['previous_distant'] == 0 else (x['distant'] if x['distant'] == 0 else x['distant'] - x['previous_distant']), axis=1)\n", "df50['mean_distance_used'] = df50['distance_used'].mean()\n", "df50['previous_qty'] = df50['qty'].shift()\n", "df50['count'] = df50['distance_used']\n", "df50['km_lit'] = (df50['distance_used'] / df50['previous_qty'])         \n", "df50['average'] = df50[df50['km_lit'] != 0][\"km_lit\"].mean()\n", "df50['average_percent'] = df50.apply(lambda x: x['km_lit'] if x['km_lit'] == 0 else x.mean_distance_used and ((x.distance_used - x.mean_distance_used) / x.mean_distance_used) * 100, axis=1)\n", "\n", "outlier_detector = IsolationForest(random_state=42)\n", "distance_used_fillna = df50[['km_lit']].fillna(0)\n", "outlier_detector.fit(distance_used_fillna)\n", "prediction = outlier_detector.predict(distance_used_fillna)\n", "prediction_strings = ['Outlier' if x < 0 else 'Standard' for x in prediction]\n", "df50['outlier_flag'] = prediction_strings\n", "\n", "df50['km_lit_2'] = df50.apply(lambda x: None if x['outlier_flag'] == 'Outlier' else x['km_lit'], axis=1)\n", "df50['average_2'] = df50[df50['km_lit_2'] != 0][\"km_lit_2\"].mean()\n", "df50['mean_distance_used_2'] = (df50['average_2'] * df50['previous_qty']) + 0.01\n", "df50['average_percent_2'] = df50.apply(lambda x: (x.distance_used - x.mean_distance_used_2) / x.mean_distance_used_2 * 100, axis=1)\n", "df50['mean'] = ((df50.average_2 - df50.average) / df50.average) * 100\n", "\n", "df60 = df50.pivot_table(columns=['distance_used'], aggfunc='size')\n", "df60 = pd.DataFrame(df60)\n", "df60 = df60.rename(columns={0: 'countoo'}).reset_index()\n", "df50['count'] = df50[['count']].replace(to_replace =df60['distance_used'].to_list(), value =df60['countoo'].to_list())\n", "\n", "display(df50)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"interpreter": {"hash": "916dbcbb3f70747c44a77c7bcd40155683ae19c65e1c03b4aa3499c5328201f1"}, "kernelspec": {"display_name": "Python 3.8.10 64-bit", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}