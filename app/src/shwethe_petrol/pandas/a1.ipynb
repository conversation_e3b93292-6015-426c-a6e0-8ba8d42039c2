{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "# import module\n", "import requests\n", "import pandas as pd\n", "from bs4 import BeautifulSoup\n", " \n", "# link for extract html data\n", " \n", " \n", "def getdata(url):\n", "    r = requests.get(url)\n", "    return r.text\n", " \n", " \n", "htmldata = getdata(\"https://www.goodreturns.in/petrol-price.html\")\n", "soup = BeautifulSoup(htmldata, 'html.parser')\n", "\n", "# Declare string var\n", "# Declare list\n", "mydatastr = ''\n", "result = []\n", "\n", "# searching all tr in the html data\n", "# storing as a string\n", "for table in soup.find_all('tr'):\n", "    mydatastr += table.get_text()\n", "\n", "# set according to your required\n", "mydatastr = mydatastr[1:]\n", "itemlist = mydatastr.split(\"\\n\\n\")\n", "\n", "for item in itemlist[:-5]:\n", "    result.append(item.split(\"\\n\"))\n", "result\n", "\n", "# Calling DataFrame constructor on list\n", "# df = pd.<PERSON>Frame(result[:-13])\n", "# df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# mydatastr = 'Hello world 123'\n", "mydatastr = ['Hello world 123', 'Hello world 456', 'Hello world 789', 'Hello world 012', 'Hello world 345', 'Hello world 678']\n", "mydatastr = mydatastr[:-5]\n", "mydatastr"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "# import module\n", "import requests\n", "import pandas as pd\n", "from bs4 import BeautifulSoup\n", " \n", "# link for extract html data\n", " \n", " \n", "def getdata(url):\n", "    r = requests.get(url)\n", "    return r.text\n", " \n", " \n", "htmldata = getdata(\"https://xn--42cah7d0cxcvbbb9x.com/%E0%B8%A3%E0%B8%B2%E0%B8%84%E0%B8%B2%E0%B8%99%E0%B9%89%E0%B8%B3%E0%B8%A1%E0%B8%B1%E0%B8%99%E0%B8%A7%E0%B8%B1%E0%B8%99%E0%B8%99%E0%B8%B5%E0%B9%89/\")\n", "soup = BeautifulSoup(htmldata, 'html.parser')\n", "\n", "\n", "mydatastr = ''\n", "result = []\n", "\n", "\n", "for table in soup.find_all('tr'):\n", "    mydatastr += table.get_text()\n", "mydatastr"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "# The Building Blocks\n", "year = '2019'\n", "url_link = 'https://www.basketball-reference.com/leagues/NBA_{}_per_game.html'\n", "\n", "# Combining the URL + year strings together\n", "url = url_link.format(year)\n", "url\n", "\n", "df = pd.read_html(url, header = 0)\n", "len(df)\n", "df[0]\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "# The Building Blocks\n", "\n", "url_link = 'https://www.goodreturns.in/petrol-price.html#Daily+Petrol+Prices+Revision+in+India'\n", "\n", "# Combining the URL + year strings together\n", "url = url_link\n", "url\n", "\n", "df = pd.read_html(url, header = 0)\n", "# len(df)\n", "df[0]\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "# The Building Blocks\n", "\n", "url_link = 'https://xn--42cah7d0cxcvbbb9x.com/%E0%B8%A3%E0%B8%B2%E0%B8%84%E0%B8%B2%E0%B8%99%E0%B9%89%E0%B8%B3%E0%B8%A1%E0%B8%B1%E0%B8%99%E0%B8%A7%E0%B8%B1%E0%B8%99%E0%B8%99%E0%B8%B5%E0%B9%89/'\n", "\n", "# Combining the URL + year strings together\n", "url = url_link\n", "url\n", "\n", "df = pd.read_html(url, header = 0)\n", "# len(df)\n", "df = df[0]\n", "df = df.to_dict('records')\n", "df\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import warnings\n", "warnings.simplefilter('ignore')\n", "\n", "import pandas as pd\n", "import sqlalchemy\n", "from sqlalchemy import create_engine, tuple_\n", "from sqlalchemy.pool import NullPool\n", "import numpy as np\n", "import json\n", "from sqlalchemy.ext.declarative import declarative_base\n", "from sqlalchemy import Column, Integer, String, Date, DateTime, ForeignKey, TEXT\n", "from sqlalchemy.sql.expression import table, text\n", "from sqlalchemy.dialects.postgresql.json import JSONB\n", "from sqlalchemy.orm import sessionmaker\n", "from sqlalchemy import and_, or_, not_\n", "from sqlalchemy import extract, cast\n", "from sqlalchemy import literal\n", "from datetime import date, datetime, timedelta\n", "import datetime\n", "import itertools\n", "from sklearn.ensemble import IsolationForest\n", "from sqlalchemy.orm.attributes import flag_modified\n", "import requests\n", "from functools import reduce\n", "\n", "\n", "DATABASE_URL = \"*************************************************/n_data\"\n", "engine = create_engine(DATABASE_URL, poolclass=NullPool)\n", "# engine = create_engine(DATABASE_URL)\n", "Session = sessionmaker(bind=engine)\n", "\n", "Base = declarative_base()\n", "\n", "class PetrolModel(Base):\n", "    __tablename__ = 'petrol'\n", "    auto_id = Column(Integer, primary_key=True)\n", "    petrol_id = Column(TEXT(convert_unicode=True), server_default=text(\"'petrol'||lpad(NEXTVAL('petrol_sequence'::regclass)::text,8, '0')\"))\n", "    datetime = Column(DateTime)\n", "    data_details = Column(JSONB, nullable=False, server_default='[]')\n", "    status_details = Column(JSONB, nullable=False, server_default='{}')\n", "    update_time = Column(DateTime)\n", "    \n", "session = Session()\n", "\n", "\n", "df10 = pd.read_sql(sql = session.query(PetrolModel)\\\n", "                        .with_entities(\n", "                            PetrolModel.petrol_id,\n", "                            PetrolModel.datetime,\n", "                            PetrolModel.data_details[0]['product_id'],\n", "                            PetrolModel.data_details[0]['price']\n", "                            )\n", "                        .filter(PetrolModel.data_details[0]['price'].astext.cast(String) != \"0.0\")\n", "                        .order_by(PetrolModel.data_details[0]['product_id'], PetrolModel.datetime.desc())\n", "                        .distinct(PetrolModel.data_details[0]['product_id'])\n", "                        .statement,\n", "                con = session.bind)\n", "df10 = df10.rename(columns={\n", "    \"anon_1\": 'product_id', \"anon_2\": 'price',\n", "    })\n", "df10['ids'] = pd.Series([100,200])\n", "df10\n", "\n", "url_link = 'https://xn--42cah7d0cxcvbbb9x.com/%E0%B8%A3%E0%B8%B2%E0%B8%84%E0%B8%B2%E0%B8%99%E0%B9%89%E0%B8%B3%E0%B8%A1%E0%B8%B1%E0%B8%99%E0%B8%A7%E0%B8%B1%E0%B8%99%E0%B8%99%E0%B8%B5%E0%B9%89/'\n", "url = url_link\n", "df20 = pd.read_html(url, header = 0)\n", "df20 = df20[0]\n", "df20['ids'] = pd.Series([100,200], index=[1, 6])\n", "df20\n", "\n", "\n", "merge = df20.merge(df10, on='ids', how='right')\n", "# merge = merge.fillna('-').to_dict('records')\n", "merge"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import warnings\n", "warnings.simplefilter('ignore')\n", "\n", "import pandas as pd\n", "import sqlalchemy\n", "from sqlalchemy import create_engine, tuple_\n", "from sqlalchemy.pool import NullPool\n", "import numpy as np\n", "import json\n", "from sqlalchemy.ext.declarative import declarative_base\n", "from sqlalchemy import Column, Integer, String, Date, DateTime, ForeignKey, TEXT\n", "from sqlalchemy.sql.expression import table, text\n", "from sqlalchemy.dialects.postgresql.json import JSONB\n", "from sqlalchemy.orm import sessionmaker\n", "from sqlalchemy import and_, or_, not_\n", "from sqlalchemy import extract, cast\n", "from sqlalchemy import literal\n", "from datetime import date, datetime, timedelta\n", "import datetime\n", "import itertools\n", "from sklearn.ensemble import IsolationForest\n", "from sqlalchemy.orm.attributes import flag_modified\n", "import requests\n", "from functools import reduce\n", "\n", "\n", "DATABASE_URL = \"*************************************************/n_data\"\n", "engine = create_engine(DATABASE_URL, poolclass=NullPool)\n", "# engine = create_engine(DATABASE_URL)\n", "Session = sessionmaker(bind=engine)\n", "\n", "Base = declarative_base()\n", "\n", "class PetrolModel(Base):\n", "    __tablename__ = 'petrol'\n", "    auto_id = Column(Integer, primary_key=True)\n", "    petrol_id = Column(TEXT(convert_unicode=True), server_default=text(\"'petrol'||lpad(NEXTVAL('petrol_sequence'::regclass)::text,8, '0')\"))\n", "    datetime = Column(DateTime)\n", "    data_details = Column(JSONB, nullable=False, server_default='[]')\n", "    status_details = Column(JSONB, nullable=False, server_default='{}')\n", "    update_time = Column(DateTime)\n", "    \n", "session = Session()\n", "\n", "\n", "df10 = pd.read_sql(sql = session.query(PetrolModel)\\\n", "                        .with_entities(\n", "                            PetrolModel.petrol_id,\n", "                            PetrolModel.datetime,\n", "                            PetrolModel.data_details[0]['che_liang_id'],\n", "                            PetrolModel.data_details[0]['distant']\n", "                            )\n", "                        .filter(PetrolModel.data_details[0]['che_liang_id'].astext.cast(Integer) == 37620)\n", "                        .order_by(PetrolModel.datetime.desc())\n", "\n", "                        .limit(2)\n", "                        .statement,\n", "                con = session.bind)\n", "df10 = df10.rename(columns={\n", "    \"anon_1\": 'che_liang_id', \"anon_2\": 'distant',\n", "    })\n", "df10"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import warnings\n", "warnings.simplefilter('ignore')\n", "\n", "import pandas as pd\n", "import sqlalchemy\n", "from sqlalchemy import create_engine, tuple_\n", "from sqlalchemy.pool import NullPool\n", "import numpy as np\n", "import json\n", "from sqlalchemy.ext.declarative import declarative_base\n", "from sqlalchemy import Column, Integer, String, Date, DateTime, ForeignKey, TEXT\n", "from sqlalchemy.sql.expression import table, text\n", "from sqlalchemy.dialects.postgresql.json import JSONB\n", "from sqlalchemy.orm import sessionmaker\n", "from sqlalchemy import and_, or_, not_\n", "from sqlalchemy import extract, cast\n", "from sqlalchemy import literal\n", "from datetime import date, datetime, timedelta\n", "import datetime\n", "import itertools\n", "from sklearn.ensemble import IsolationForest\n", "from sqlalchemy.orm.attributes import flag_modified\n", "import requests\n", "from functools import reduce\n", "\n", "\n", "DATABASE_URL = \"*************************************************/test_nern\"\n", "engine = create_engine(DATABASE_URL, poolclass=NullPool)\n", "# engine = create_engine(DATABASE_URL)\n", "Session = sessionmaker(bind=engine)\n", "\n", "Base = declarative_base()\n", "\n", "class PetrolModel(Base):\n", "    __tablename__ = 'petrol'\n", "    auto_id = Column(Integer, primary_key=True)\n", "    petrol_id = Column(TEXT(convert_unicode=True), server_default=text(\"'petrol'||lpad(NEXTVAL('petrol_sequence'::regclass)::text,8, '0')\"))\n", "    datetime = Column(DateTime)\n", "    data_details = Column(JSONB, nullable=False, server_default='[]')\n", "    status_details = Column(JSONB, nullable=False, server_default='{}')\n", "    update_time = Column(DateTime)\n", "    datetime_bill = Column(DateTime)\n", "\n", "class PetrolModel2(Base):\n", "    __tablename__ = 'petrol2'\n", "    auto_id = Column(Integer, primary_key=True)\n", "    # petrol_id = Column(TEXT(convert_unicode=True), USER_ID_SEQ, server_default=text(\"'petrol'||lpad(NEXTVAL('user_id_seq'::regclass)::text,8, '0')\"))\n", "    petrol_id = Column(TEXT(convert_unicode=True), server_default=text(\"'petrol'||lpad(NEXTVAL('petrol_sequence'::regclass)::text,8, '0')\"))\n", "    datetime = Column(DateTime)\n", "    data_details = Column(JSONB, nullable=False, server_default='[]')\n", "    status_details = Column(JSONB, nullable=False, server_default='{}')\n", "    \n", "session = Session()\n", "\n", "\n", "\n", "import datetime as DT\n", "today = DT.date.today()\n", "week_ago = today - DT.<PERSON><PERSON><PERSON>(days=90)\n", "print(week_ago)\n", "\n", "df20 = pd.read_sql(sql = session.query(PetrolModel)\\\n", "                        .with_entities(\n", "                            PetrolModel.petrol_id,\n", "                            PetrolModel.datetime,\n", "                            PetrolModel.datetime_bill,\n", "                            PetrolModel.data_details[0]['che_liang_id'],\n", "                            PetrolModel.data_details[0]['distant'],\n", "                            PetrolModel.data_details[0]['qty'],\n", "                            )\n", "                            .filter(PetrolModel.datetime > week_ago).statement,\n", "                con = session.bind)\n", "df20 = df20.rename(columns={\n", "    \"anon_1\": 'che_liang_id',\n", "    \"anon_2\": 'distant',\n", "    \"anon_3\": 'qty',\n", "    })\n", "\n", "# display(\"df20\", df20)\n", "\n", "df30 = pd.read_sql(sql = session.query(PetrolModel2)\\\n", "                        .with_entities(\n", "                            PetrolModel2.petrol_id,\n", "                            PetrolModel2.datetime,\n", "                            PetrolModel2.data_details[0]['che_liang_id'],\n", "                            PetrolModel2.data_details[0]['distant'],\n", "                            PetrolModel2.data_details[0]['qty'],\n", "                            )\n", "                            .filter( PetrolModel2.datetime > week_ago).statement,\n", "                con = session.bind)\n", "df30 = df30.rename(columns={\n", "    \"anon_1\": 'che_liang_id',\n", "    \"anon_2\": 'distant',\n", "    \"anon_3\": 'qty',\n", "    })\n", "\n", "# display(\"df30\", df30)\n", "\n", "df40 = pd.concat([df20, df30], ignore_index=True)\n", "df40 = df40.sort_values(['datetime'], ascending=[True])\n", "# display(df40)\n", "\n", "df50 = df40[df40['che_liang_id'] == 24215]\n", "\n", "df50['previous_datetime_bill'] = df50['datetime_bill'].shift()\n", "df50['diff_datetime_bill'] = (df50['datetime_bill'] - df50['previous_datetime_bill']).dt.days\n", "df50['mean_datetime_bill'] = df50['diff_datetime_bill'].mean()\n", "df50['percent_date_bill'] = df50.apply(lambda x: (x.diff_datetime_bill - x.mean_datetime_bill) / x.mean_datetime_bill * 100, axis=1)\n", "\n", "df50['previous_distant'] = df50['distant'].shift()\n", "df50['distance_used'] = df50.apply(lambda x: x['previous_distant'] if x['previous_distant'] == 0 else (x['distant'] if x['distant'] == 0 else x['distant'] - x['previous_distant']), axis=1)\n", "df50['mean_distance_used'] = df50['distance_used'].mean()\n", "df50['previous_qty'] = df50['qty'].shift()\n", "df50['count'] = df50['distance_used']\n", "df50['km_lit'] = (df50['distance_used'] / df50['previous_qty'])         \n", "df50['average'] = df50[df50['km_lit'] != 0][\"km_lit\"].mean()\n", "df50['average_percent'] = df50.apply(lambda x: x['km_lit'] if x['km_lit'] == 0 else x.mean_distance_used and ((x.distance_used - x.mean_distance_used) / x.mean_distance_used) * 100, axis=1)\n", "\n", "outlier_detector = IsolationForest(random_state=42)\n", "distance_used_fillna = df50[['km_lit']].fillna(0)\n", "outlier_detector.fit(distance_used_fillna)\n", "prediction = outlier_detector.predict(distance_used_fillna)\n", "prediction_strings = ['Outlier' if x < 0 else 'Standard' for x in prediction]\n", "df50['outlier_flag'] = prediction_strings\n", "\n", "df50['km_lit_2'] = df50.apply(lambda x: None if x['outlier_flag'] == 'Outlier' else x['km_lit'], axis=1)\n", "df50['average_2'] = df50[df50['km_lit_2'] != 0][\"km_lit_2\"].mean()\n", "df50['mean_distance_used_2'] = (df50['average_2'] * df50['previous_qty']) + 0.01\n", "df50['average_percent_2'] = df50.apply(lambda x: (x.distance_used - x.mean_distance_used_2) / x.mean_distance_used_2 * 100, axis=1)\n", "df50['mean'] = ((df50.average_2 - df50.average) / df50.average) * 100\n", "\n", "df60 = df50.pivot_table(columns=['distance_used'], aggfunc='size')\n", "df60 = pd.DataFrame(df60)\n", "df60 = df60.rename(columns={0: 'countoo'}).reset_index()\n", "df50['count'] = df50[['count']].replace(to_replace =df60['distance_used'].to_list(), value =df60['countoo'].to_list())\n", "\n", "df50 = df50.fillna(0).round(decimals = 1)\n", "# df50 = df50.to_dict('records')\n", "df50\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "from sqlalchemy.orm.attributes import flag_modified\n", "import json\n", "import requests\n", "import pandas as pd\n", "\n", "import warnings\n", "warnings.simplefilter('ignore')\n", "\n", "from sqlalchemy import create_engine, tuple_\n", "from sqlalchemy.orm import Session, sessionmaker\n", "from sqlalchemy.pool import NullPool\n", "from datetime import date, datetime, timedelta\n", "import datetime\n", "from functools import reduce\n", "from sqlalchemy import and_, or_, not_\n", "from sqlalchemy import extract, cast\n", "from sqlalchemy import Column, Integer, String, Date, DateTime, ForeignKey, TEXT, FLOAT\n", "from sqlalchemy.ext.declarative import declarative_base\n", "from sklearn.ensemble import IsolationForest\n", "from sqlalchemy.sql.expression import table, text\n", "from sqlalchemy.dialects.postgresql.json import JSONB\n", "\n", "\n", "DATABASE_URL = \"*************************************************/test_nern\"\n", "engine = create_engine(DATABASE_URL, poolclass=NullPool)\n", "Session = sessionmaker(bind=engine)\n", "\n", "Base = declarative_base()\n", "\n", "\n", "\n", "class PetrolModel(Base):\n", "    __tablename__ = 'petrol'\n", "    auto_id = Column(Integer, primary_key=True)\n", "    # petrol_id = Column(TEXT(convert_unicode=True), USER_ID_SEQ, server_default=text(\"'petrol'||lpad(NEXTVAL('user_id_seq'::regclass)::text,8, '0')\"))\n", "    petrol_id = Column(TEXT(convert_unicode=True), server_default=text(\"'petrol'||lpad(NEXTVAL('petrol_sequence'::regclass)::text,8, '0')\"))\n", "    datetime = Column(DateTime)\n", "    data_details = Column(JSONB, nullable=False, server_default='[]')\n", "    status_details = Column(JSONB, nullable=False, server_default='{}')\n", "    update_time = Column(DateTime)\n", "    datetime_bill = Column(DateTime)\n", "\n", "\n", "class PetrolModel2(Base):\n", "    __tablename__ = 'petrol2'\n", "    auto_id = Column(Integer, primary_key=True)\n", "    # petrol_id = Column(TEXT(convert_unicode=True), USER_ID_SEQ, server_default=text(\"'petrol'||lpad(NEXTVAL('user_id_seq'::regclass)::text,8, '0')\"))\n", "    petrol_id = Column(TEXT(convert_unicode=True), server_default=text(\"'petrol'||lpad(NEXTVAL('petrol_sequence'::regclass)::text,8, '0')\"))\n", "    datetime = Column(DateTime)\n", "    data_details = Column(JSONB, nullable=False, server_default='[]')\n", "    status_details = Column(JSONB, nullable=False, server_default='{}')\n", "\n", "session = Session()\n", "\n", "import datetime as DT\n", "today = DT.date.today()\n", "week_ago = today - DT.<PERSON><PERSON><PERSON>(days=90)\n", "print(week_ago)\n", "df20 = pd.read_sql(sql = session.query(PetrolModel)\\\n", "                        .with_entities(\n", "                            PetrolModel.petrol_id,\n", "                            PetrolModel.datetime,\n", "                            PetrolModel.datetime_bill,\n", "                            PetrolModel.data_details[0]['che_liang_id'],\n", "                            PetrolModel.data_details[0]['distant'],\n", "                            PetrolModel.data_details[0]['qty'],\n", "                            PetrolModel.data_details[0]['driver'],\n", "                            )\n", "                            .filter(PetrolModel.datetime > week_ago).statement,\n", "                con = session.bind)\n", "df20 = df20.rename(columns={\n", "    \"anon_1\": 'che_liang_id',\n", "    \"anon_2\": 'distant',\n", "    \"anon_3\": 'qty',\n", "    \"anon_4\": 'driver',\n", "    })\n", "# display(\"df20\", df20)\n", "df30 = pd.read_sql(sql = session.query(PetrolModel2)\\\n", "                        .with_entities(\n", "                            PetrolModel2.petrol_id,\n", "                            PetrolModel2.datetime,\n", "                            PetrolModel2.data_details[0]['che_liang_id'],\n", "                            PetrolModel2.data_details[0]['distant'],\n", "                            PetrolModel2.data_details[0]['qty'],\n", "                            )\n", "                            .filter(PetrolModel2.datetime > week_ago).statement,\n", "                con = session.bind)\n", "df30 = df30.rename(columns={\n", "    \"anon_1\": 'che_liang_id',\n", "    \"anon_2\": 'distant',\n", "    \"anon_3\": 'qty',\n", "    })\n", "# # display(\"df30\", df30)\n", "df40 = pd.concat([df20, df30], ignore_index=True)\n", "df40 = df40.sort_values(['datetime'], ascending=[True])\n", "# # display(df40)\n", "df50 = df40[df40['che_liang_id'] == 785]\n", "\n", "df50['previous_datetime_bill'] = df50['datetime_bill'].shift()\n", "df50['diff_datetime_bill'] = (df50['datetime_bill'] - df50['previous_datetime_bill']).dt.days\n", "df50['mean_datetime_bill'] = df50['diff_datetime_bill'].mean()\n", "df50['percent_date_bill'] = df50.apply(lambda x: x.mean_datetime_bill and (x.diff_datetime_bill - x.mean_datetime_bill) / x.mean_datetime_bill * 100, axis=1)\n", "\n", "df50['previous_distant'] = df50['distant'].shift()  \n", "df50['distance_used'] = df50.apply(lambda x: x['previous_distant'] if x['previous_distant'] == 0 else (x['distant'] if x['distant'] == 0 else x['distant'] - x['previous_distant']), axis=1)\n", "df50['mean_distance_used'] = df50['distance_used'].mean()\n", "df50['previous_qty'] = df50['qty'].shift()\n", "df50['count'] = df50['distance_used']\n", "df50['km_lit'] = (df50['distance_used'] / df50['previous_qty'])         \n", "df50['average'] = df50[df50['km_lit'] != 0][\"km_lit\"].mean()\n", "df50['average_percent'] = df50.apply(lambda x: x['km_lit'] if x['km_lit'] == 0 else x.mean_distance_used and ((x.distance_used - x.mean_distance_used) / x.mean_distance_used) * 100, axis=1)\n", "\n", "outlier_detector = IsolationForest(random_state=42)\n", "distance_used_fillna = df50[['km_lit']].fillna(0)\n", "outlier_detector.fit(distance_used_fillna)\n", "prediction = outlier_detector.predict(distance_used_fillna)\n", "prediction_strings = ['Outlier' if x < 0 else 'Standard' for x in prediction]\n", "df50['outlier_flag'] = prediction_strings\n", "\n", "df50['km_lit_2'] = df50.apply(lambda x: None if x['outlier_flag'] == 'Outlier' else x['km_lit'], axis=1)\n", "df50['average_2'] = df50[df50['km_lit_2'] != 0][\"km_lit_2\"].mean()\n", "df50['mean_distance_used_2'] = (df50['average_2'] * df50['previous_qty']) + 0.01\n", "df50['average_percent_2'] = df50.apply(lambda x: (x.distance_used - x.mean_distance_used_2) / x.mean_distance_used_2 * 100, axis=1)\n", "df50['mean'] = ((df50.average_2 - df50.average) / df50.average) * 100\n", "\n", "df60 = df50.pivot_table(columns=['distance_used'], aggfunc='size')\n", "df60 = pd.DataFrame(df60)\n", "df60 = df60.rename(columns={0: 'countoo'}).reset_index()\n", "df50['count'] = df50[['count']].replace(to_replace =df60['distance_used'].to_list(), value =df60['countoo'].to_list())\n", "\n", "qty_list = df50['qty'].astype(float).round(decimals = 1)\n", "uniqleQtyToList = qty_list.unique().tolist()\n", "df50['uniqleQty'] = str(uniqleQtyToList)\n", "df50['FirstuniqleQty'] = str(uniqleQtyToList[0])\n", "df50['LastuniqleQty'] = str(uniqleQtyToList[-1])\n", "driver_list = df50['driver'].dropna().unique().tolist()\n", "df50['uniqleDriver'] = str(driver_list)\n", "df50['FirstuniqleDriver'] = driver_list[0]\n", "df50['LastuniqleDriver'] = driver_list[-1]\n", "\n", "# item_counts = df50[\"driver\"].value_counts()\n", "# print(item_counts)\n", "\n", "df50['driverCount'] = df50['driver'].map(df50['driver'].value_counts())\n", "df50['driverCount'] = df50.groupby(['driver'])['che_liang_id'].transform('count')\n", "\n", "\n", "\n", "\n", "df50 = df50.fillna(0).round(decimals = 1)\n", "df50"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "from sqlalchemy.orm.attributes import flag_modified\n", "import json\n", "import requests\n", "import pandas as pd\n", "\n", "import warnings\n", "warnings.simplefilter('ignore')\n", "\n", "from sqlalchemy import create_engine, tuple_\n", "from sqlalchemy.orm import Session, sessionmaker\n", "from sqlalchemy.pool import NullPool\n", "from datetime import date, datetime, timedelta\n", "import datetime\n", "from functools import reduce\n", "from sqlalchemy import and_, or_, not_\n", "from sqlalchemy import extract, cast\n", "from sqlalchemy import Column, Integer, String, Date, DateTime, ForeignKey, TEXT, FLOAT\n", "from sqlalchemy.ext.declarative import declarative_base\n", "from sklearn.ensemble import IsolationForest\n", "from sqlalchemy.sql.expression import table, text\n", "from sqlalchemy.dialects.postgresql.json import JSONB\n", "\n", "\n", "DATABASE_URL = \"*************************************************/test_nern\"\n", "engine = create_engine(DATABASE_URL, poolclass=NullPool)\n", "Session = sessionmaker(bind=engine)\n", "\n", "Base = declarative_base()\n", "\n", "\n", "\n", "class PetrolModel(Base):\n", "    __tablename__ = 'petrol'\n", "    auto_id = Column(Integer, primary_key=True)\n", "    # petrol_id = Column(TEXT(convert_unicode=True), USER_ID_SEQ, server_default=text(\"'petrol'||lpad(NEXTVAL('user_id_seq'::regclass)::text,8, '0')\"))\n", "    petrol_id = Column(TEXT(convert_unicode=True), server_default=text(\"'petrol'||lpad(NEXTVAL('petrol_sequence'::regclass)::text,8, '0')\"))\n", "    datetime = Column(DateTime)\n", "    data_details = Column(JSONB, nullable=False, server_default='[]')\n", "    status_details = Column(JSONB, nullable=False, server_default='{}')\n", "    update_time = Column(DateTime)\n", "    datetime_bill = Column(DateTime)\n", "\n", "\n", "class PetrolModel2(Base):\n", "    __tablename__ = 'petrol2'\n", "    auto_id = Column(Integer, primary_key=True)\n", "    # petrol_id = Column(TEXT(convert_unicode=True), USER_ID_SEQ, server_default=text(\"'petrol'||lpad(NEXTVAL('user_id_seq'::regclass)::text,8, '0')\"))\n", "    petrol_id = Column(TEXT(convert_unicode=True), server_default=text(\"'petrol'||lpad(NEXTVAL('petrol_sequence'::regclass)::text,8, '0')\"))\n", "    datetime = Column(DateTime)\n", "    data_details = Column(JSONB, nullable=False, server_default='[]')\n", "    status_details = Column(JSONB, nullable=False, server_default='{}')\n", "\n", "session = Session()\n", "\n", "import datetime as DT\n", "today = DT.date.today()\n", "week_ago = today - DT.<PERSON><PERSON><PERSON>(days=90)\n", "print(week_ago)\n", "df20 = pd.read_sql(sql = session.query(PetrolModel)\\\n", "                        .with_entities(\n", "                            PetrolModel.petrol_id,\n", "                            PetrolModel.datetime,\n", "                            PetrolModel.data_details[0]['che_liang_id'],\n", "                            PetrolModel.data_details[0]['distant'],\n", "                            PetrolModel.data_details[0]['qty'],\n", "                            PetrolModel.data_details[0]['driver'],\n", "                            )\n", "                            .filter(PetrolModel.datetime > week_ago).statement,\n", "                con = session.bind)\n", "df20 = df20.rename(columns={\n", "    \"anon_1\": 'che_liang_id',\n", "    \"anon_2\": 'distant',\n", "    \"anon_3\": 'qty',\n", "    \"anon_4\": 'driver',\n", "    })\n", "# display(\"df20\", df20)\n", "df30 = pd.read_sql(sql = session.query(PetrolModel2)\\\n", "                        .with_entities(\n", "                            PetrolModel2.petrol_id,\n", "                            PetrolModel2.datetime,\n", "                            PetrolModel2.data_details[0]['che_liang_id'],\n", "                            PetrolModel2.data_details[0]['distant'],\n", "                            PetrolModel2.data_details[0]['qty'],\n", "                            )\n", "                            .filter(PetrolModel2.datetime > week_ago).statement,\n", "                con = session.bind)\n", "df30 = df30.rename(columns={\n", "    \"anon_1\": 'che_liang_id',\n", "    \"anon_2\": 'distant',\n", "    \"anon_3\": 'qty',\n", "    })\n", "# # display(\"df30\", df30)\n", "df40 = pd.concat([df20, df30], ignore_index=True)\n", "df40 = df40.sort_values(['datetime'], ascending=[True])\n", "# # display(df40)\n", "df50 = df40[df40['che_liang_id'] == 26594]\n", "\n", "\n", "df50 =df50[df50['driver'].astype('Int64') != 0]\n", "\n", "\n", "\n", "# df50['previous_distant'] = df50['distant'].shift()  \n", "# df50['distance_used'] = df50.apply(lambda x: x['previous_distant'] if x['previous_distant'] == 0 else (x['distant'] if x['distant'] == 0 else x['distant'] - x['previous_distant']), axis=1)\n", "# df50['mean_distance_used'] = df50['distance_used'].mean()\n", "# df50['previous_qty'] = df50['qty'].shift()\n", "# df50['count'] = df50['distance_used']\n", "# df50['km_lit'] = (df50['distance_used'] / df50['previous_qty'])         \n", "# df50['average'] = df50[df50['km_lit'] != 0][\"km_lit\"].mean()\n", "# df50['average_percent'] = df50.apply(lambda x: x['km_lit'] if x['km_lit'] == 0 else x.mean_distance_used and ((x.distance_used - x.mean_distance_used) / x.mean_distance_used) * 100, axis=1)\n", "\n", "# outlier_detector = IsolationForest(random_state=42)\n", "# distance_used_fillna = df50[['km_lit']].fillna(0)\n", "# outlier_detector.fit(distance_used_fillna)\n", "# prediction = outlier_detector.predict(distance_used_fillna)\n", "# prediction_strings = ['Outlier' if x < 0 else 'Standard' for x in prediction]\n", "# df50['outlier_flag'] = prediction_strings\n", "\n", "# df50['km_lit_2'] = df50.apply(lambda x: None if x['outlier_flag'] == 'Outlier' else x['km_lit'], axis=1)\n", "# df50['average_2'] = df50[df50['km_lit_2'] != 0][\"km_lit_2\"].mean()\n", "# df50['mean_distance_used_2'] = (df50['average_2'] * df50['previous_qty']) + 0.01\n", "# df50['average_percent_2'] = df50.apply(lambda x: (x.distance_used - x.mean_distance_used_2) / x.mean_distance_used_2 * 100, axis=1)\n", "# df50['mean'] = ((df50.average_2 - df50.average) / df50.average) * 100\n", "\n", "\n", "\n", "\n", "# df50 =df50[df50['driver'].astype('Int64') != 0]\n", "\n", "# driver_list = df50['driver'].dropna().unique().tolist()\n", "# df50['uniqleDriver'] = str(driver_list)\n", "# df50['FirstuniqleDriver'] = driver_list[0]\n", "# df50['LastuniqleDriver'] = driver_list[-1]\n", "# df50['driverCount'] = df50['driver'].map(df50['driver'].value_counts())\n", "\n", "\n", "\n", "df50['previous_distant'] = df50.groupby('driver')['distant'].transform(lambda x : x.shift())\n", "df50['distance_used'] = df50.groupby('driver').apply(lambda x: x['previous_distant'] if x['previous_distant'].any() == 0 else (x['distant'] if x['distant'].any() == 0 else x['distant'] - x['previous_distant'])).reset_index('driver',drop=True)\n", "df50['mean_distance_used'] = df50.groupby('driver')['distance_used'].transform(lambda x : x.mean())\n", "df50['previous_qty'] = df50.groupby('driver')['qty'].transform(lambda x : x.shift())  \n", "df50['meanQty'] = df50.groupby('driver')['qty'].transform(lambda x : x.mean())   \n", "df50['km_lit'] = df50.groupby('driver').apply(lambda x : x['distance_used'] / x['previous_qty']  ).reset_index('driver',drop=True)  \n", "df50['average'] = df50.groupby('driver')['km_lit'].transform(lambda x : x.mean()) \n", " \n", "def train_isolation_group(pass_distance_used):\n", "    outlier_detector = IsolationForest(random_state=42)\n", "    np_scaled = pass_distance_used.values.reshape(-1, 1)\n", "    distance_used_fillna = pd.DataFrame(np_scaled).fillna(0)\n", "    outlier_detector.fit(distance_used_fillna)\n", "    prediction = outlier_detector.predict(distance_used_fillna)\n", "    prediction_strings = ['Outlier' if x < 0 else 'Standard' for x in prediction]\n", "    return prediction_strings\n", "df50['outlier_flag'] = df50.groupby('driver')['km_lit'].transform(train_isolation_group)\n", "\n", "df50['km_lit_2'] = df50.apply(lambda x: None if x['outlier_flag'] == 'Outlier' else x['km_lit'], axis=1)\n", "df50['average_2'] = df50.groupby('driver')['km_lit_2'].transform(lambda x : x.mean())\n", "df50['mean_distance_used_2'] = df50.groupby('driver').apply(lambda x: (x['average_2'] * x['previous_qty']) + 0.01).reset_index('driver',drop=True) \n", "df50['mean'] = df50.groupby('driver').apply(lambda x: ((x.average_2 - x.average) / x.average) * 100).reset_index('driver',drop=True) \n", "\n", "df50['driverCount'] = df50['driver'].map(df50['driver'].value_counts())\n", "\n", "df50 = df50.groupby('driver').tail(1)\n", "\n", "\n", "\n", "df50 = df50.fillna(0).round(decimals = 1)\n", "df50"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2022-08-05\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>petrol_id</th>\n", "      <th>datetime</th>\n", "      <th>datetime_bill</th>\n", "      <th>che_liang_id</th>\n", "      <th>distant</th>\n", "      <th>qty</th>\n", "      <th>driver</th>\n", "      <th>previous_distant</th>\n", "      <th>distance_used</th>\n", "      <th>mean_distance_used</th>\n", "      <th>previous_qty</th>\n", "      <th>meanQty</th>\n", "      <th>km_lit</th>\n", "      <th>average</th>\n", "      <th>outlier_flag</th>\n", "      <th>km_lit_2</th>\n", "      <th>average_2</th>\n", "      <th>mean_distance_used_2</th>\n", "      <th>mean</th>\n", "      <th>driverCount</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>633</th>\n", "      <td>petrol00002141</td>\n", "      <td>2022-10-07 08:56:03.980303</td>\n", "      <td>0</td>\n", "      <td>26594</td>\n", "      <td>26484.0</td>\n", "      <td>30.0</td>\n", "      <td>25730.0</td>\n", "      <td>26465.0</td>\n", "      <td>19.0</td>\n", "      <td>18.0</td>\n", "      <td>30.0</td>\n", "      <td>30.0</td>\n", "      <td>0.6</td>\n", "      <td>0.6</td>\n", "      <td>Standard</td>\n", "      <td>0.6</td>\n", "      <td>0.6</td>\n", "      <td>18.0</td>\n", "      <td>0.0</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>632</th>\n", "      <td>petrol00002256</td>\n", "      <td>2022-10-21 11:32:33.974773</td>\n", "      <td>0</td>\n", "      <td>26594</td>\n", "      <td>26535.0</td>\n", "      <td>30.0</td>\n", "      <td>30584.0</td>\n", "      <td>26521.0</td>\n", "      <td>14.0</td>\n", "      <td>18.0</td>\n", "      <td>30.0</td>\n", "      <td>30.0</td>\n", "      <td>0.5</td>\n", "      <td>0.6</td>\n", "      <td>Standard</td>\n", "      <td>0.5</td>\n", "      <td>0.6</td>\n", "      <td>18.0</td>\n", "      <td>0.0</td>\n", "      <td>3</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["          petrol_id                   datetime  datetime_bill  che_liang_id  \\\n", "633  petrol00002141 2022-10-07 08:56:03.980303              0         26594   \n", "632  petrol00002256 2022-10-21 11:32:33.974773              0         26594   \n", "\n", "     distant   qty   driver  previous_distant  distance_used  \\\n", "633  26484.0  30.0  25730.0           26465.0           19.0   \n", "632  26535.0  30.0  30584.0           26521.0           14.0   \n", "\n", "     mean_distance_used  previous_qty  meanQty  km_lit  average outlier_flag  \\\n", "633                18.0          30.0     30.0     0.6      0.6     Standard   \n", "632                18.0          30.0     30.0     0.5      0.6     Standard   \n", "\n", "     km_lit_2  average_2  mean_distance_used_2  mean  driverCount  \n", "633       0.6        0.6                  18.0   0.0            3  \n", "632       0.5        0.6                  18.0   0.0            3  "]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["import numpy as np\n", "from sqlalchemy.orm.attributes import flag_modified\n", "import json\n", "import requests\n", "import pandas as pd\n", "\n", "import warnings\n", "warnings.simplefilter('ignore')\n", "\n", "from sqlalchemy import create_engine, tuple_\n", "from sqlalchemy.orm import Session, sessionmaker\n", "from sqlalchemy.pool import NullPool\n", "from datetime import date, datetime, timedelta\n", "import datetime\n", "from functools import reduce\n", "from sqlalchemy import and_, or_, not_\n", "from sqlalchemy import extract, cast\n", "from sqlalchemy import Column, Integer, String, Date, DateTime, ForeignKey, TEXT, FLOAT\n", "from sqlalchemy.ext.declarative import declarative_base\n", "from sklearn.ensemble import IsolationForest\n", "from sqlalchemy.sql.expression import table, text\n", "from sqlalchemy.dialects.postgresql.json import JSONB\n", "\n", "\n", "DATABASE_URL = \"*************************************************/test_nern\"\n", "engine = create_engine(DATABASE_URL, poolclass=NullPool)\n", "Session = sessionmaker(bind=engine)\n", "\n", "Base = declarative_base()\n", "\n", "\n", "\n", "class PetrolModel(Base):\n", "    __tablename__ = 'petrol'\n", "    auto_id = Column(Integer, primary_key=True)\n", "    # petrol_id = Column(TEXT(convert_unicode=True), USER_ID_SEQ, server_default=text(\"'petrol'||lpad(NEXTVAL('user_id_seq'::regclass)::text,8, '0')\"))\n", "    petrol_id = Column(TEXT(convert_unicode=True), server_default=text(\"'petrol'||lpad(NEXTVAL('petrol_sequence'::regclass)::text,8, '0')\"))\n", "    datetime = Column(DateTime)\n", "    data_details = Column(JSONB, nullable=False, server_default='[]')\n", "    status_details = Column(JSONB, nullable=False, server_default='{}')\n", "    update_time = Column(DateTime)\n", "    datetime_bill = Column(DateTime)\n", "\n", "\n", "class PetrolModel2(Base):\n", "    __tablename__ = 'petrol2'\n", "    auto_id = Column(Integer, primary_key=True)\n", "    # petrol_id = Column(TEXT(convert_unicode=True), USER_ID_SEQ, server_default=text(\"'petrol'||lpad(NEXTVAL('user_id_seq'::regclass)::text,8, '0')\"))\n", "    petrol_id = Column(TEXT(convert_unicode=True), server_default=text(\"'petrol'||lpad(NEXTVAL('petrol_sequence'::regclass)::text,8, '0')\"))\n", "    datetime = Column(DateTime)\n", "    data_details = Column(JSONB, nullable=False, server_default='[]')\n", "    status_details = Column(JSONB, nullable=False, server_default='{}')\n", "\n", "session = Session()\n", "\n", "import datetime as DT\n", "today = DT.date.today()\n", "week_ago = today - DT.<PERSON><PERSON><PERSON>(days=90)\n", "print(week_ago)\n", "df20 = pd.read_sql(sql = session.query(PetrolModel)\\\n", "                        .with_entities(\n", "                            PetrolModel.petrol_id,\n", "                            PetrolModel.datetime,\n", "                            PetrolModel.datetime_bill,\n", "                            PetrolModel.data_details[0]['che_liang_id'],\n", "                            PetrolModel.data_details[0]['distant'],\n", "                            PetrolModel.data_details[0]['qty'],\n", "                            PetrolModel.data_details[0]['driver'],\n", "                            )\n", "                            .filter(PetrolModel.datetime > week_ago).statement,\n", "                con = session.bind)\n", "df20 = df20.rename(columns={\n", "    \"anon_1\": 'che_liang_id',\n", "    \"anon_2\": 'distant',\n", "    \"anon_3\": 'qty',\n", "    \"anon_4\": 'driver',\n", "    })\n", "# display(\"df20\", df20)\n", "df30 = pd.read_sql(sql = session.query(PetrolModel2)\\\n", "                        .with_entities(\n", "                            PetrolModel2.petrol_id,\n", "                            PetrolModel2.datetime,\n", "                            PetrolModel2.data_details[0]['che_liang_id'],\n", "                            PetrolModel2.data_details[0]['distant'],\n", "                            PetrolModel2.data_details[0]['qty'],\n", "                            )\n", "                            .filter(PetrolModel2.datetime > week_ago).statement,\n", "                con = session.bind)\n", "df30 = df30.rename(columns={\n", "    \"anon_1\": 'che_liang_id',\n", "    \"anon_2\": 'distant',\n", "    \"anon_3\": 'qty',\n", "    })\n", "# # display(\"df30\", df30)\n", "df40 = pd.concat([df20, df30], ignore_index=True)\n", "df40 = df40.sort_values(['datetime'], ascending=[True])\n", "# # display(df40)\n", "df50 = df40[df40['che_liang_id'] == 26594]\n", "\n", "\n", "df50 =df50[df50['driver'].astype('Int64') != 0]\n", "\n", "\n", "\n", "# df50['previous_distant'] = df50['distant'].shift()  \n", "# df50['distance_used'] = df50.apply(lambda x: x['previous_distant'] if x['previous_distant'] == 0 else (x['distant'] if x['distant'] == 0 else x['distant'] - x['previous_distant']), axis=1)\n", "# df50['mean_distance_used'] = df50['distance_used'].mean()\n", "# df50['previous_qty'] = df50['qty'].shift()\n", "# df50['count'] = df50['distance_used']\n", "# df50['km_lit'] = (df50['distance_used'] / df50['previous_qty'])         \n", "# df50['average'] = df50[df50['km_lit'] != 0][\"km_lit\"].mean()\n", "# df50['average_percent'] = df50.apply(lambda x: x['km_lit'] if x['km_lit'] == 0 else x.mean_distance_used and ((x.distance_used - x.mean_distance_used) / x.mean_distance_used) * 100, axis=1)\n", "\n", "# outlier_detector = IsolationForest(random_state=42)\n", "# distance_used_fillna = df50[['km_lit']].fillna(0)\n", "# outlier_detector.fit(distance_used_fillna)\n", "# prediction = outlier_detector.predict(distance_used_fillna)\n", "# prediction_strings = ['Outlier' if x < 0 else 'Standard' for x in prediction]\n", "# df50['outlier_flag'] = prediction_strings\n", "\n", "# df50['km_lit_2'] = df50.apply(lambda x: None if x['outlier_flag'] == 'Outlier' else x['km_lit'], axis=1)\n", "# df50['average_2'] = df50[df50['km_lit_2'] != 0][\"km_lit_2\"].mean()\n", "# df50['mean_distance_used_2'] = (df50['average_2'] * df50['previous_qty']) + 0.01\n", "# df50['average_percent_2'] = df50.apply(lambda x: (x.distance_used - x.mean_distance_used_2) / x.mean_distance_used_2 * 100, axis=1)\n", "# df50['mean'] = ((df50.average_2 - df50.average) / df50.average) * 100\n", "\n", "\n", "\n", "\n", "# df50 =df50[df50['driver'].astype('Int64') != 0]\n", "\n", "# driver_list = df50['driver'].dropna().unique().tolist()\n", "# df50['uniqleDriver'] = str(driver_list)\n", "# df50['FirstuniqleDriver'] = driver_list[0]\n", "# df50['LastuniqleDriver'] = driver_list[-1]\n", "# df50['driverCount'] = df50['driver'].map(df50['driver'].value_counts())\n", "\n", "\n", "\n", "df50['previous_distant'] = df50.groupby('driver')['distant'].transform(lambda x : x.shift())\n", "df50['distance_used'] = df50.groupby('driver').apply(lambda x: x['previous_distant'] if x['previous_distant'].any() == 0 else (x['distant'] if x['distant'].any() == 0 else x['distant'] - x['previous_distant'])).reset_index('driver',drop=True)\n", "df50['mean_distance_used'] = df50.groupby('driver')['distance_used'].transform(lambda x : x.mean())\n", "df50['previous_qty'] = df50.groupby('driver')['qty'].transform(lambda x : x.shift())  \n", "df50['meanQty'] = df50.groupby('driver')['qty'].transform(lambda x : x.mean())   \n", "df50['km_lit'] = df50.groupby('driver').apply(lambda x : x['distance_used'] / x['previous_qty']  ).reset_index('driver',drop=True)  \n", "df50['average'] = df50.groupby('driver')['km_lit'].transform(lambda x : x.mean()) \n", " \n", "def train_isolation_group(pass_distance_used):\n", "    outlier_detector = IsolationForest(random_state=42)\n", "    np_scaled = pass_distance_used.values.reshape(-1, 1)\n", "    distance_used_fillna = pd.DataFrame(np_scaled).fillna(0)\n", "    outlier_detector.fit(distance_used_fillna)\n", "    prediction = outlier_detector.predict(distance_used_fillna)\n", "    prediction_strings = ['Outlier' if x < 0 else 'Standard' for x in prediction]\n", "    return prediction_strings\n", "df50['outlier_flag'] = df50.groupby('driver')['km_lit'].transform(train_isolation_group)\n", "\n", "df50['km_lit_2'] = df50.apply(lambda x: None if x['outlier_flag'] == 'Outlier' else x['km_lit'], axis=1)\n", "df50['average_2'] = df50.groupby('driver')['km_lit_2'].transform(lambda x : x.mean())\n", "df50['mean_distance_used_2'] = df50.groupby('driver').apply(lambda x: (x['average_2'] * x['previous_qty']) + 0.01).reset_index('driver',drop=True) \n", "df50['mean'] = df50.groupby('driver').apply(lambda x: ((x.average_2 - x.average) / x.average) * 100).reset_index('driver',drop=True) \n", "\n", "df50['driverCount'] = df50['driver'].map(df50['driver'].value_counts())\n", "\n", "df50 = df50.groupby('driver').tail(1)\n", "\n", "df50 = df50.fillna(0).round(decimals = 1)\n", "df50"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>che_liang_id</th>\n", "      <th>fen_dian_id</th>\n", "      <th>qty</th>\n", "      <th>distant</th>\n", "      <th>jia_yi_id</th>\n", "      <th>jia_yi_idname</th>\n", "      <th>jia_yi_mm_name</th>\n", "      <th>ri_qi</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>106</td>\n", "      <td>1</td>\n", "      <td>234.0</td>\n", "      <td>123.0</td>\n", "      <td>106</td>\n", "      <td>W03A</td>\n", "      <td>နှစ်တန်ကားတင်ဘာပါ 3A-9900</td>\n", "      <td>2002-01-11 00:00:00.000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>194</td>\n", "      <td>1</td>\n", "      <td>123.0</td>\n", "      <td>123.0</td>\n", "      <td>194</td>\n", "      <td>W06A</td>\n", "      <td>နှစ်တန်ကားတင်ဘာပါ P-6308</td>\n", "      <td>2002-01-11 00:00:00.000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>785</td>\n", "      <td>1</td>\n", "      <td>123.0</td>\n", "      <td>123.0</td>\n", "      <td>785</td>\n", "      <td>V.1</td>\n", "      <td>ထွန်စက်</td>\n", "      <td>2002-01-11 00:00:00.000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>785</td>\n", "      <td>1</td>\n", "      <td>123.0</td>\n", "      <td>123.0</td>\n", "      <td>785</td>\n", "      <td>V.1</td>\n", "      <td>ထွန်စက်</td>\n", "      <td>2002-01-11 00:00:00.000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>785</td>\n", "      <td>1</td>\n", "      <td>123.0</td>\n", "      <td>123.0</td>\n", "      <td>785</td>\n", "      <td>V.1</td>\n", "      <td>ထွန်စက်</td>\n", "      <td>2002-01-11 00:00:00.000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>637</th>\n", "      <td>38374</td>\n", "      <td>1</td>\n", "      <td>30.0</td>\n", "      <td>159.0</td>\n", "      <td>38374</td>\n", "      <td>V.9</td>\n", "      <td>ဘိလပ်တင်မစက် 4မစ်</td>\n", "      <td>2022-03-08 00:00:00.000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>638</th>\n", "      <td>38374</td>\n", "      <td>1</td>\n", "      <td>30.0</td>\n", "      <td>147.0</td>\n", "      <td>38374</td>\n", "      <td>V.9</td>\n", "      <td>ဘိလပ်တင်မစက် 4မစ်</td>\n", "      <td>2022-03-08 00:00:00.000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>639</th>\n", "      <td>38374</td>\n", "      <td>1</td>\n", "      <td>30.0</td>\n", "      <td>128.0</td>\n", "      <td>38374</td>\n", "      <td>V.9</td>\n", "      <td>ဘိလပ်တင်မစက် 4မစ်</td>\n", "      <td>2022-03-08 00:00:00.000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>640</th>\n", "      <td>38374</td>\n", "      <td>1</td>\n", "      <td>30.0</td>\n", "      <td>112.0</td>\n", "      <td>38374</td>\n", "      <td>V.9</td>\n", "      <td>ဘိလပ်တင်မစက် 4မစ်</td>\n", "      <td>2022-03-08 00:00:00.000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>641</th>\n", "      <td>38374</td>\n", "      <td>1</td>\n", "      <td>30.0</td>\n", "      <td>743.0</td>\n", "      <td>38374</td>\n", "      <td>V.9</td>\n", "      <td>ဘိလပ်တင်မစက် 4မစ်</td>\n", "      <td>2022-03-08 00:00:00.000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>642 rows × 8 columns</p>\n", "</div>"], "text/plain": ["     che_liang_id  fen_dian_id    qty  distant  jia_yi_id jia_yi_idname  \\\n", "0             106            1  234.0    123.0        106          W03A   \n", "1             194            1  123.0    123.0        194          W06A   \n", "2             785            1  123.0    123.0        785           V.1   \n", "3             785            1  123.0    123.0        785           V.1   \n", "4             785            1  123.0    123.0        785           V.1   \n", "..            ...          ...    ...      ...        ...           ...   \n", "637         38374            1   30.0    159.0      38374           V.9   \n", "638         38374            1   30.0    147.0      38374           V.9   \n", "639         38374            1   30.0    128.0      38374           V.9   \n", "640         38374            1   30.0    112.0      38374           V.9   \n", "641         38374            1   30.0    743.0      38374           V.9   \n", "\n", "                jia_yi_mm_name                    ri_qi  \n", "0    နှစ်တန်ကားတင်ဘာပါ 3A-9900  2002-01-11 00:00:00.000  \n", "1     နှစ်တန်ကားတင်ဘာပါ P-6308  2002-01-11 00:00:00.000  \n", "2                      ထွန်စက်  2002-01-11 00:00:00.000  \n", "3                      ထွန်စက်  2002-01-11 00:00:00.000  \n", "4                      ထွန်စက်  2002-01-11 00:00:00.000  \n", "..                         ...                      ...  \n", "637          ဘိလပ်တင်မစက် 4မစ်  2022-03-08 00:00:00.000  \n", "638          ဘိလပ်တင်မစက် 4မစ်  2022-03-08 00:00:00.000  \n", "639          ဘိလပ်တင်မစက် 4မစ်  2022-03-08 00:00:00.000  \n", "640          ဘိလပ်တင်မစက် 4မစ်  2022-03-08 00:00:00.000  \n", "641          ဘိလပ်တင်မစက် 4မစ်  2022-03-08 00:00:00.000  \n", "\n", "[642 rows x 8 columns]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import numpy as np\n", "from sqlalchemy.orm.attributes import flag_modified\n", "import json\n", "import requests\n", "import pandas as pd\n", "\n", "import warnings\n", "warnings.simplefilter('ignore')\n", "\n", "from sqlalchemy import create_engine, tuple_\n", "from sqlalchemy.orm import Session, sessionmaker\n", "from sqlalchemy.pool import NullPool\n", "from datetime import date, datetime, timedelta\n", "import datetime\n", "from functools import reduce\n", "from sqlalchemy import and_, or_, not_\n", "from sqlalchemy import extract, cast\n", "from sqlalchemy import Column, Integer, String, Date, DateTime, ForeignKey, TEXT, FLOAT\n", "from sqlalchemy.ext.declarative import declarative_base\n", "from sklearn.ensemble import IsolationForest\n", "from sqlalchemy.sql.expression import table, text\n", "from sqlalchemy.dialects.postgresql.json import JSONB\n", "\n", "\n", "DATABASE_URL = \"*************************************************/test_nern\"\n", "engine = create_engine(DATABASE_URL, poolclass=NullPool)\n", "Session = sessionmaker(bind=engine)\n", "\n", "Base = declarative_base()\n", "\n", "\n", "\n", "class PetrolModel(Base):\n", "    __tablename__ = 'petrol'\n", "    auto_id = Column(Integer, primary_key=True)\n", "    # petrol_id = Column(TEXT(convert_unicode=True), USER_ID_SEQ, server_default=text(\"'petrol'||lpad(NEXTVAL('user_id_seq'::regclass)::text,8, '0')\"))\n", "    petrol_id = Column(TEXT(convert_unicode=True), server_default=text(\"'petrol'||lpad(NEXTVAL('petrol_sequence'::regclass)::text,8, '0')\"))\n", "    datetime = Column(DateTime)\n", "    data_details = Column(JSONB, nullable=False, server_default='[]')\n", "    status_details = Column(JSONB, nullable=False, server_default='{}')\n", "    update_time = Column(DateTime)\n", "    datetime_bill = Column(DateTime)\n", "\n", "\n", "class PetrolModel2(Base):\n", "    __tablename__ = 'petrol2'\n", "    auto_id = Column(Integer, primary_key=True)\n", "    # petrol_id = Column(TEXT(convert_unicode=True), USER_ID_SEQ, server_default=text(\"'petrol'||lpad(NEXTVAL('user_id_seq'::regclass)::text,8, '0')\"))\n", "    petrol_id = Column(TEXT(convert_unicode=True), server_default=text(\"'petrol'||lpad(NEXTVAL('petrol_sequence'::regclass)::text,8, '0')\"))\n", "    datetime = Column(DateTime)\n", "    data_details = Column(JSONB, nullable=False, server_default='[]')\n", "    status_details = Column(JSONB, nullable=False, server_default='{}')\n", "\n", "\n", "class Calculate(Base):\n", "    __tablename__ = 'petrol_calculate'\n", "    auto_id = Column(Integer, primary_key=True)\n", "    petrol_id = Column(TEXT)\n", "    calOne = Column(JSONB, nullable=False, server_default='{}')\n", "    calTwo = Column(JSONB, nullable=False, server_default='{}')\n", "    caltype = Column(TEXT)\n", "\n", "session = Session()\n", "\n", "\n", "df81 = pd.read_sql(sql = session.query(PetrolModel)\n", "                    .with_entities(\n", "                        PetrolModel.data_details[0]['che_liang_id'],\n", "                        PetrolModel.data_details[0]['fen_dian_id'],\n", "                        PetrolModel.data_details[0]['qty'],\n", "                        PetrolModel.data_details[0]['distant'],\n", "                    )\n", "                    .order_by(PetrolModel.data_details[0]['che_liang_id'],PetrolModel.datetime.desc())\n", "                    .filter(\n", "                        not_(\n", "                            PetrolModel.data_details[0]['bill'].astext.cast(String) == \"\",\n", "                            )\n", "                        )\n", "                    .statement, con = session.bind)\n", "df81 = df81.rename(columns={\n", "    \"anon_1\": 'che_liang_id',\n", "    \"anon_2\": 'fen_dian_id',\n", "    \"anon_3\": 'qty',\n", "    \"anon_4\": 'distant',\n", "    })\n", "# display(df81)\n", "\n", "\n", "df100 = df81\n", "df100 = df100.rename(columns={\"che_liang_id\": 'jia_yi_id'})\n", "# display(df100)\n", "\n", "url = 'http://192.168.1.11:8200/mongodb_data_api/api/v2/search/jia_yi_name_list_id'\n", "# url = Arter_api + 'jia_yi_name_list_id'\n", "to_dict = df100.to_dict('records')\n", "body_raw = {\"data_api\": to_dict}\n", "df110 = requests.get(url=url, json=body_raw).json()\n", "df110 = pd.DataFrame(df110)\n", "# display(df110)\n", "\n", "\n", "merge = df81.merge(df110, left_on='che_liang_id', right_on='jia_yi_id', how='left')\n", "merge = merge.fillna(\"\")\n", "display(merge)\n", "\n"]}, {"cell_type": "code", "execution_count": 50, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2022-08-13\n"]}, {"data": {"text/plain": ["'df20'"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>petrol_id</th>\n", "      <th>datetime</th>\n", "      <th>datetime_bill</th>\n", "      <th>che_liang_id</th>\n", "      <th>distant</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>petrol00002400</td>\n", "      <td>2022-11-11 10:05:27.480661</td>\n", "      <td>2022-11-11</td>\n", "      <td>1569</td>\n", "      <td>213.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["        petrol_id                   datetime datetime_bill  che_liang_id  \\\n", "0  petrol00002400 2022-11-11 10:05:27.480661    2022-11-11          1569   \n", "\n", "   distant  \n", "0    213.0  "]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Match\n"]}], "source": ["import numpy as np\n", "from sqlalchemy.orm.attributes import flag_modified\n", "import json\n", "import requests\n", "import pandas as pd\n", "\n", "import warnings\n", "warnings.simplefilter('ignore')\n", "\n", "from sqlalchemy import create_engine, tuple_\n", "from sqlalchemy.orm import Session, sessionmaker\n", "from sqlalchemy.pool import NullPool\n", "from datetime import date, datetime, timedelta\n", "import datetime\n", "from functools import reduce\n", "from sqlalchemy import and_, or_, not_\n", "from sqlalchemy import extract, cast\n", "from sqlalchemy import Column, Integer, String, Date, DateTime, ForeignKey, TEXT, FLOAT\n", "from sqlalchemy.ext.declarative import declarative_base\n", "from sklearn.ensemble import IsolationForest\n", "from sqlalchemy.sql.expression import table, text\n", "from sqlalchemy.dialects.postgresql.json import JSONB\n", "\n", "\n", "DATABASE_URL = \"*************************************************/test_nern\"\n", "engine = create_engine(DATABASE_URL, poolclass=NullPool)\n", "Session = sessionmaker(bind=engine)\n", "\n", "Base = declarative_base()\n", "\n", "\n", "\n", "class PetrolModel(Base):\n", "    __tablename__ = 'petrol'\n", "    auto_id = Column(Integer, primary_key=True)\n", "    # petrol_id = Column(TEXT(convert_unicode=True), USER_ID_SEQ, server_default=text(\"'petrol'||lpad(NEXTVAL('user_id_seq'::regclass)::text,8, '0')\"))\n", "    petrol_id = Column(TEXT(convert_unicode=True), server_default=text(\"'petrol'||lpad(NEXTVAL('petrol_sequence'::regclass)::text,8, '0')\"))\n", "    datetime = Column(DateTime)\n", "    data_details = Column(JSONB, nullable=False, server_default='[]')\n", "    status_details = Column(JSONB, nullable=False, server_default='{}')\n", "    update_time = Column(DateTime)\n", "    datetime_bill = Column(DateTime)\n", "\n", "\n", "class PetrolModel2(Base):\n", "    __tablename__ = 'petrol2'\n", "    auto_id = Column(Integer, primary_key=True)\n", "    # petrol_id = Column(TEXT(convert_unicode=True), USER_ID_SEQ, server_default=text(\"'petrol'||lpad(NEXTVAL('user_id_seq'::regclass)::text,8, '0')\"))\n", "    petrol_id = Column(TEXT(convert_unicode=True), server_default=text(\"'petrol'||lpad(NEXTVAL('petrol_sequence'::regclass)::text,8, '0')\"))\n", "    datetime = Column(DateTime)\n", "    data_details = Column(JSONB, nullable=False, server_default='[]')\n", "    status_details = Column(JSONB, nullable=False, server_default='{}')\n", "\n", "\n", "class Calculate(Base):\n", "    __tablename__ = 'petrol_calculate'\n", "    auto_id = Column(Integer, primary_key=True)\n", "    petrol_id = Column(TEXT)\n", "    calOne = Column(JSONB, nullable=False, server_default='{}')\n", "    calTwo = Column(JSONB, nullable=False, server_default='{}')\n", "    caltype = Column(TEXT)\n", "\n", "session = Session()\n", "\n", "\n", "df81 = pd.read_sql(sql = session.query(PetrolModel)\n", "                    .with_entities(\n", "                        PetrolModel.data_details[0]['che_liang_id'],\n", "                        PetrolModel.data_details[0]['fen_dian_id'],\n", "                        PetrolModel.data_details[0]['qty'],\n", "                        PetrolModel.data_details[0]['distant'],\n", "                    )\n", "                    .order_by(PetrolModel.data_details[0]['che_liang_id'],PetrolModel.datetime.desc())\n", "                    .filter(\n", "                        not_(\n", "                            PetrolModel.data_details[0]['bill'].astext.cast(String) == \"\",\n", "                            )\n", "                        )\n", "                    .statement, con = session.bind)\n", "df81 = df81.rename(columns={\n", "    \"anon_1\": 'che_liang_id',\n", "    \"anon_2\": 'fen_dian_id',\n", "    \"anon_3\": 'qty',\n", "    \"anon_4\": 'distant',\n", "    })\n", "# display(df81)\n", "\n", "import datetime as DT\n", "today = DT.date.today()\n", "week_ago = today - DT.<PERSON><PERSON><PERSON>(days=90)\n", "print(week_ago)\n", "df20 = pd.read_sql(sql = session.query(PetrolModel)\\\n", "                        .with_entities(\n", "                            PetrolModel.petrol_id,\n", "                            PetrolModel.datetime,\n", "                            PetrolModel.datetime_bill,\n", "                            PetrolModel.data_details[0]['che_liang_id'],\n", "                            PetrolModel.data_details[0]['distant']\n", "                            )\n", "                            .filter(PetrolModel.data_details[0]['che_liang_id'].astext.cast(Integer) == 1569)\n", "                            .order_by(PetrolModel.datetime.desc())\n", "                            .limit(1)\n", "                            .statement,\n", "                con = session.bind)\n", "df20 = df20.rename(columns={\n", "    \"anon_1\": 'che_liang_id',\n", "    \"anon_2\": 'distant'\n", "    })\n", "display(\"df20\", df20)\n", "# fen_dian_id=1 user_id=16380 warehouse_id=0 u_id=0 lei_a=22 lei_b=29 ke_bian=0 che_liang_id=1569 store=0 car=1569 driver=1788 petrol=1108 qty=123.0 currency=138 price=0.0 distant=213.0 bill='waiting' datetime_bill=datetime.date(2022, 11, 11)\n", "# ValueError: The truth value of a Series is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().\n", "\n", "df20['datetime_bill_match'] = df20['datetime_bill'].apply(lambda x: 'Match' if x == datetime.date(2022, 11, 11) else 'Mismatch')\n", "df20\n", "\n", "if df20['datetime_bill_match'].item() == 'Match':\n", "    print(\"Match\")\n", "else:\n", "    print(\"not Match\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"interpreter": {"hash": "916dbcbb3f70747c44a77c7bcd40155683ae19c65e1c03b4aa3499c5328201f1"}, "kernelspec": {"display_name": "Python 3.8.10 64-bit", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.6"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}