{"cells": [{"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"ename": "TypeError", "evalue": "String.__init__() got an unexpected keyword argument 'convert_unicode'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mTypeError\u001b[0m                                 Traceback (most recent call last)", "Cell \u001b[0;32mIn[12], line 33\u001b[0m\n\u001b[1;32m     27\u001b[0m Session \u001b[39m=\u001b[39m sessionmaker(bind\u001b[39m=\u001b[39mengine)\n\u001b[1;32m     29\u001b[0m Base \u001b[39m=\u001b[39m declarative_base()\n\u001b[0;32m---> 33\u001b[0m \u001b[39mclass\u001b[39;00m \u001b[39mPetrolModel\u001b[39;00m(Base):\n\u001b[1;32m     34\u001b[0m     __tablename__ \u001b[39m=\u001b[39m \u001b[39m'\u001b[39m\u001b[39mpetrol\u001b[39m\u001b[39m'\u001b[39m\n\u001b[1;32m     35\u001b[0m     auto_id \u001b[39m=\u001b[39m Column(Integer, primary_key\u001b[39m=\u001b[39m\u001b[39mTrue\u001b[39;00m)\n", "Cell \u001b[0;32mIn[12], line 37\u001b[0m, in \u001b[0;36mPetrolModel\u001b[0;34m()\u001b[0m\n\u001b[1;32m     35\u001b[0m auto_id \u001b[39m=\u001b[39m Column(Integer, primary_key\u001b[39m=\u001b[39m\u001b[39mTrue\u001b[39;00m)\n\u001b[1;32m     36\u001b[0m \u001b[39m# petrol_id = Column(TEXT(convert_unicode=True), USER_ID_SEQ, server_default=text(\"'petrol'||lpad(NEXTVAL('user_id_seq'::regclass)::text,8, '0')\"))\u001b[39;00m\n\u001b[0;32m---> 37\u001b[0m petrol_id \u001b[39m=\u001b[39m Column(TEXT(convert_unicode\u001b[39m=\u001b[39;49m\u001b[39mTrue\u001b[39;49;00m), server_default\u001b[39m=\u001b[39mtext(\u001b[39m\"\u001b[39m\u001b[39m'\u001b[39m\u001b[39mpetrol\u001b[39m\u001b[39m'\u001b[39m\u001b[39m||lpad(NEXTVAL(\u001b[39m\u001b[39m'\u001b[39m\u001b[39mpetrol_sequence\u001b[39m\u001b[39m'\u001b[39m\u001b[39m::regclass)::text,8, \u001b[39m\u001b[39m'\u001b[39m\u001b[39m0\u001b[39m\u001b[39m'\u001b[39m\u001b[39m)\u001b[39m\u001b[39m\"\u001b[39m))\n\u001b[1;32m     38\u001b[0m datetime \u001b[39m=\u001b[39m Column(DateTime)\n\u001b[1;32m     39\u001b[0m data_details \u001b[39m=\u001b[39m Column(JSONB, nullable\u001b[39m=\u001b[39m\u001b[39mFalse\u001b[39;00m, server_default\u001b[39m=\u001b[39m\u001b[39m'\u001b[39m\u001b[39m[]\u001b[39m\u001b[39m'\u001b[39m)\n", "\u001b[0;31mTypeError\u001b[0m: String.__init__() got an unexpected keyword argument 'convert_unicode'"]}], "source": ["import numpy as np\n", "from sqlalchemy.orm.attributes import flag_modified\n", "import json\n", "import requests\n", "import pandas as pd\n", "\n", "import warnings\n", "warnings.simplefilter('ignore')\n", "\n", "from sqlalchemy import create_engine, tuple_\n", "from sqlalchemy.orm import Session, sessionmaker\n", "from sqlalchemy.pool import NullPool\n", "from datetime import date, datetime, timedelta\n", "import datetime\n", "from functools import reduce\n", "from sqlalchemy import and_, or_, not_\n", "from sqlalchemy import extract, cast\n", "from sqlalchemy import Column, Integer, String, Date, DateTime, ForeignKey, TEXT, FLOAT\n", "from sqlalchemy.ext.declarative import declarative_base\n", "# from sklearn.ensemble import IsolationForest\n", "from sqlalchemy.sql.expression import table, text\n", "from sqlalchemy.dialects.postgresql.json import JSONB\n", "\n", "\n", "DATABASE_URL = \"*************************************************/test_nern\"\n", "engine = create_engine(DATABASE_URL, poolclass=NullPool)\n", "Session = sessionmaker(bind=engine)\n", "\n", "Base = declarative_base()\n", "\n", "\n", "\n", "class PetrolModel(Base):\n", "    __tablename__ = 'petrol'\n", "    auto_id = Column(Integer, primary_key=True)\n", "    # petrol_id = Column(TEXT(convert_unicode=True), USER_ID_SEQ, server_default=text(\"'petrol'||lpad(NEXTVAL('user_id_seq'::regclass)::text,8, '0')\"))\n", "    petrol_id = Column(TEXT(convert_unicode=True), server_default=text(\"'petrol'||lpad(NEXTVAL('petrol_sequence'::regclass)::text,8, '0')\"))\n", "    datetime = Column(DateTime)\n", "    data_details = Column(JSONB, nullable=False, server_default='[]')\n", "    status_details = Column(JSONB, nullable=False, server_default='{}')\n", "    update_time = Column(DateTime)\n", "    datetime_bill = Column(DateTime)\n", "\n", "session = Session()\n", "\n", "\n", "df81 = pd.read_sql(sql = session.query(PetrolModel)\n", "                    .with_entities(\n", "                        PetrolModel.data_details[0]['che_liang_id'],\n", "                        PetrolModel.data_details[0]['fen_dian_id'],\n", "                        PetrolModel.data_details[0]['qty'],\n", "                        PetrolModel.data_details[0]['distant'],\n", "                    )\n", "                    .order_by(PetrolModel.data_details[0]['che_liang_id'],PetrolModel.datetime.desc())\n", "                    .filter(\n", "                        not_(\n", "                            PetrolModel.data_details[0]['bill'].astext.cast(String) == \"\",\n", "                            )\n", "                        )\n", "                    .statement, con = session.bind)\n", "df81 = df81.rename(columns={\n", "    \"anon_1\": 'che_liang_id',\n", "    \"anon_2\": 'fen_dian_id',\n", "    \"anon_3\": 'qty',\n", "    \"anon_4\": 'distant',\n", "    })\n", "display(df81)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}