{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "from treelib import Tree\n", "import json\n", "\n", "\n", "\n", "data = {\n", "    'WD17CD': ['E05001678', '*********', '*********', '*********', '*********'],\n", "    'WD17NM': ['Newington', '<PERSON>leover', 'Higher Croft', 'Burn Valley', 'Newland'],\n", "    'LAD17CD': ['*********', '*********', '*********', '*********', '*********'],\n", "    'LAD17NM': ['Kingston upon Hull, City of', 'Derby', 'Blackburn with Darwen', 'Hartlepool', 'Kingston upon Hull, City of'],\n", "    'GOR10CD': ['*********', '*********', '*********', '*********', '*********'],\n", "    'GOR10NM': ['Yorkshire and The Humber', 'East Midlands', 'North West', 'North East', 'Yorkshire and The Humber'],\n", "    'CTRY17CD': ['*********', '*********', '*********', '*********', '*********'],\n", "    'CTRY17NM': ['England', 'England', 'England', 'England', 'England']\n", "}\n", "  \n", "\n", "wards_df = pd.DataFrame(data)\n", "\n", "country_tree = Tree()\n", "# Create a root node\n", "country_tree.create_node(\"Country\", \"countries\")\n", "# Group by country\n", "for country, regions in wards_df.head(5).groupby([\"CTRY17NM\", \"CTRY17CD\"]):\n", "    # Generate a node for each country\n", "    country_tree.create_node(country[0], country[1], parent=\"countries\")\n", "    \n", "    # Group by region\n", "    for region, las in regions.groupby([\"GOR10NM\", \"GOR10CD\"]):\n", "        # Generate a node for each region\n", "        country_tree.create_node(region[0], region[1], parent=country[1])\n", "        print(country_tree)\n", "        # Group by local authority\n", "        for la, wards in las.groupby(['LAD17NM', 'LAD17CD']):\n", "            # Create a node for each local authority\n", "            country_tree.create_node(la[0], la[1], parent=region[1])\n", "            for ward, _ in wards.groupby(['WD17NM', 'WD17CD']):\n", "                # Create a leaf node for each ward\n", "                country_tree.create_node(ward[0], ward[1], parent=la[1])\n", "\n", "# Output the hierarchical data\n", "# country_tree.show()\n", "\n", "tree_json = json.loads(country_tree.to_json())\n", "ghgh = [tree_json]\n", "ghgh"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "from treelib import Tree\n", "import json\n", "\n", "\n", "\n", "data = {\n", "    'auto_id': ['2', '13', '14', '15'],\n", "    'mm_name': ['thai', 'chaing rai', 'chaing mai', 'mae sai'],\n", "    'parent_id': ['0', '2', '2', '13'],\n", "    'CTRY17CD': ['*********', '*********', '*********', '*********'],\n", "    'CTRY17NM': ['England', 'England', 'England', 'England']\n", "}\n", "  \n", "\n", "wards_df = pd.DataFrame(data)\n", "\n", "country_tree = Tree()\n", "# Create a root node\n", "country_tree.create_node(\"Country\", \"countries\")\n", "# Group by country\n", "for country, regions in wards_df.head(5).groupby([\"CTRY17NM\", \"CTRY17CD\"]):\n", "    # Generate a node for each country\n", "    country_tree.create_node(country[0], country[1], parent=\"countries\")\n", "    \n", "    # Group by region\n", "    for region, las in regions.groupby([\"mm_name\", \"auto_id\"]):\n", "        # Generate a node for each region\n", "        country_tree.create_node(region[0], region[1], parent=country[1])\n", "        # print(country_tree)\n", "        # Group by local authority\n", "        # for la, wards in las.groupby(['mm_name', 'parent_id']):\n", "            # Create a node for each local authority\n", "            # country_tree.create_node(la[0], la[1], parent=region[1])\n", "            # print(country_tree)\n", "    #         for ward, _ in wards.groupby(['WD17NM', 'WD17CD']):\n", "    #             # Create a leaf node for each ward\n", "    #             country_tree.create_node(ward[0], ward[1], parent=la[1])\n", "\n", "\n", "# Output the hierarchical data\n", "country_tree.show()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["How to creating recursive JSON hierarchy tree with Python\n", "\n", "\n", "\n", "import pandas as pd\n", "\n", "data = {\n", "    'id': ['2', '13', '14', '15'],\n", "    'name': ['color', 'red', 'blue', 'ruby red'],\n", "    'child_id': ['0', '2', '2', '13']\n", "}\n", "  \n", "\n", "df = pd.DataFrame(data)\n", "df\n", "\n", "\n", "expected Output\n", "[\n", "    {\n", "        \"name\": \"color\",\n", "        \"id\": 2,\n", "        \"child_id\": 0,\n", "        \"children\": [\n", "            {\n", "                \"name\": \"red\",\n", "                \"id\": 13,\n", "                \"child_id\": 2,\n", "                \"children\": [\n", "                    {\n", "                        \"name\": \"ruby red\",\n", "                        \"id\": 15,\n", "                        \"child_id\": 13,\n", "                    }\n", "                ]\n", "            },\n", "            {\n", "                \"name\": \"blue\",\n", "                \"id\": 14,\n", "                \"child_id\": 2\n", "            }\n", "        ]\n", "    }\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "\n", "data = {\n", "    'id': ['2', '13', '14', '15', '16', '17', '18'],\n", "    'name': ['color', 'red', 'blue', 'ruby red', 'pet', 'cat', 'dog'],\n", "    'child_id': ['0', '2', '2', '13', '0', '16', '16']\n", "}\n", "  \n", "\n", "df = pd.DataFrame(data)\n", "df"]}, {"cell_type": "code", "execution_count": 66, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th>mm_name</th>\n", "      <th>jia_yi_id</th>\n", "      <th>nrcImgFront</th>\n", "      <th>personName</th>\n", "      <th>personPhone</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>20000</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>123456</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>30584</td>\n", "      <td>192.168.1.12:9000/personal/personalFolder/4e67...</td>\n", "      <td>ရဲမြတ်ထွန်း</td>\n", "      <td>99999</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["mm_name  jia_yi_id                                        nrcImgFront  \\\n", "0            20000                                                      \n", "1            30584  192.168.1.12:9000/personal/personalFolder/4e67...   \n", "\n", "mm_name   personName personPhone  \n", "0                         123456  \n", "1        ရဲမြတ်ထွန်း       99999  "]}, "metadata": {}, "output_type": "display_data"}], "source": ["import numpy as np\n", "from sqlalchemy.orm.attributes import flag_modified\n", "import json\n", "import requests\n", "import pandas as pd\n", "\n", "import warnings\n", "warnings.simplefilter('ignore')\n", "\n", "from sqlalchemy import create_engine, tuple_\n", "from sqlalchemy.orm import Session, sessionmaker\n", "from sqlalchemy.pool import NullPool\n", "from datetime import date, datetime, timedelta\n", "import datetime\n", "from functools import reduce\n", "from sqlalchemy import and_, or_, not_\n", "from sqlalchemy import extract, cast\n", "from sqlalchemy import Column, Integer, String, Date, DateTime, ForeignKey, TEXT, FLOAT\n", "from sqlalchemy.ext.declarative import declarative_base\n", "from sklearn.ensemble import IsolationForest\n", "from sqlalchemy.sql.expression import table, text\n", "from sqlalchemy.dialects.postgresql.json import JSONB\n", "\n", "\n", "DATABASE_URL = \"**************************************************/shwethe_personal\"\n", "engine = create_engine(DATABASE_URL, poolclass=NullPool)\n", "Session = sessionmaker(bind=engine)\n", "\n", "Base = declarative_base()\n", "\n", "\n", "\n", "class personal(Base):\n", "    __tablename__ = 'personal'\n", "    auto_id = Column(Integer, primary_key=True)\n", "    jia_yi_id = Column(Integer)\n", "    jia_yi = Column(String)\n", "    name = Column(String)\n", "    datetime = Column(DateTime)\n", "\n", "class personal_info(Base):\n", "    __tablename__ = 'personal_info'\n", "    auto_id = Column(Integer, primary_key=True)\n", "    jia_yi = Column(Integer)\n", "    jia_yi_id = Column(Integer)\n", "    type = Column(Integer)\n", "    key = Column(Integer)\n", "    value = Column(String)\n", "    iid = Column(Integer)\n", "    datetime = Column(DateTime)\n", "\n", "class tree_key(Base):\n", "    __tablename__ = 'tree_key'\n", "    auto_id = Column(Integer, primary_key=True)\n", "    mm_name = Column(String)\n", "    parent_id = Column(Integer)\n", "    head_id = Column(Integer)\n", "    fen_ji = Column(Integer)\n", "    type = Column(Integer)\n", "    input_or_text = Column(String)\n", "\n", "\n", "session = Session()\n", "\n", "\n", "df = pd.read_sql(sql = session.query(personal).statement, con = session.bind)\n", "df2 = pd.read_sql(sql = session.query(personal_info).statement, con = session.bind)\n", "df3 = pd.read_sql(sql = session.query(tree_key).statement, con = session.bind)\n", "\n", "df2 = df2.sort_values(['jia_yi', 'key' ,'datetime']).drop_duplicates(['jia_yi', 'key'], keep='last')\n", "\n", "mergeDF = df2.merge(df3, left_on='key', right_on='auto_id')\n", "mergeDF = mergeDF.set_index(['jia_yi_id', 'mm_name']).value.unstack().reset_index().fillna('')\n", "\n", "\n", "# mergeDF = df.merge(mergeDF, left_on='jia_yi_id', right_on='jia_yi_id')\n", "display(mergeDF)"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>auto_id</th>\n", "      <th>jia_yi_id</th>\n", "      <th>jia_yi</th>\n", "      <th>name</th>\n", "      <th>datetime</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>151</td>\n", "      <td>28958</td>\n", "      <td>AG149</td>\n", "      <td>ရွှေတုတ်</td>\n", "      <td>2022-12-12 09:24:44.372695</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>152</td>\n", "      <td>35268</td>\n", "      <td>AF01915</td>\n", "      <td>မောင်မြင့်ကျော်</td>\n", "      <td>2022-12-12 09:41:30.653805</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>153</td>\n", "      <td>43552</td>\n", "      <td>AF02191</td>\n", "      <td>မောင်မျိုးမင်းဦး</td>\n", "      <td>2022-12-12 09:43:22.066478</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>154</td>\n", "      <td>42263</td>\n", "      <td>AF02150</td>\n", "      <td>မောင်ဇင်ကိုကိုလွင်</td>\n", "      <td>2022-12-12 09:44:24.713343</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>90</td>\n", "      <td>40860</td>\n", "      <td>AG218</td>\n", "      <td>မောင်ဖြိုးသူအောင်</td>\n", "      <td>2022-12-07 15:30:44.704529</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>76</th>\n", "      <td>167</td>\n", "      <td>41703</td>\n", "      <td>AF02146</td>\n", "      <td>မောင်နေသူ</td>\n", "      <td>2022-12-13 08:51:22.857709</td>\n", "    </tr>\n", "    <tr>\n", "      <th>77</th>\n", "      <td>168</td>\n", "      <td>41669</td>\n", "      <td>AF02143</td>\n", "      <td>မောင်ကောင်းမြတ်ကျော်</td>\n", "      <td>2022-12-13 08:52:22.941518</td>\n", "    </tr>\n", "    <tr>\n", "      <th>78</th>\n", "      <td>169</td>\n", "      <td>41702</td>\n", "      <td>AF02145</td>\n", "      <td>မောင်ဖိုးထူး</td>\n", "      <td>2022-12-13 08:54:18.461956</td>\n", "    </tr>\n", "    <tr>\n", "      <th>79</th>\n", "      <td>170</td>\n", "      <td>41979</td>\n", "      <td>AF02148</td>\n", "      <td>မောင်ဇင်ကိုဝင်း</td>\n", "      <td>2022-12-13 08:55:12.808499</td>\n", "    </tr>\n", "    <tr>\n", "      <th>80</th>\n", "      <td>171</td>\n", "      <td>42593</td>\n", "      <td>AF02161</td>\n", "      <td>ဦးကျော်မိုး</td>\n", "      <td>2022-12-13 08:56:07.789554</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>81 rows × 5 columns</p>\n", "</div>"], "text/plain": ["    auto_id  jia_yi_id   jia_yi                  name  \\\n", "0       151      28958    AG149              ရွှေတုတ်   \n", "1       152      35268  AF01915       မောင်မြင့်ကျော်   \n", "2       153      43552  AF02191      မောင်မျိုးမင်းဦး   \n", "3       154      42263  AF02150    မောင်ဇင်ကိုကိုလွင်   \n", "4        90      40860    AG218     မောင်ဖြိုးသူအောင်   \n", "..      ...        ...      ...                   ...   \n", "76      167      41703  AF02146             မောင်နေသူ   \n", "77      168      41669  AF02143  မောင်ကောင်းမြတ်ကျော်   \n", "78      169      41702  AF02145          မောင်ဖိုးထူး   \n", "79      170      41979  AF02148       မောင်ဇင်ကိုဝင်း   \n", "80      171      42593  AF02161           ဦးကျော်မိုး   \n", "\n", "                     datetime  \n", "0  2022-12-12 09:24:44.372695  \n", "1  2022-12-12 09:41:30.653805  \n", "2  2022-12-12 09:43:22.066478  \n", "3  2022-12-12 09:44:24.713343  \n", "4  2022-12-07 15:30:44.704529  \n", "..                        ...  \n", "76 2022-12-13 08:51:22.857709  \n", "77 2022-12-13 08:52:22.941518  \n", "78 2022-12-13 08:54:18.461956  \n", "79 2022-12-13 08:55:12.808499  \n", "80 2022-12-13 08:56:07.789554  \n", "\n", "[81 rows x 5 columns]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import numpy as np\n", "from sqlalchemy.orm.attributes import flag_modified\n", "import json\n", "import requests\n", "import pandas as pd\n", "\n", "import warnings\n", "warnings.simplefilter('ignore')\n", "\n", "from sqlalchemy import create_engine, tuple_\n", "from sqlalchemy.orm import Session, sessionmaker\n", "from sqlalchemy.pool import NullPool\n", "from datetime import date, datetime, timedelta\n", "import datetime\n", "from functools import reduce\n", "from sqlalchemy import and_, or_, not_\n", "from sqlalchemy import extract, cast\n", "from sqlalchemy import Column, Integer, String, Date, DateTime, ForeignKey, TEXT, FLOAT\n", "from sqlalchemy.ext.declarative import declarative_base\n", "from sklearn.ensemble import IsolationForest\n", "from sqlalchemy.sql.expression import table, text\n", "from sqlalchemy.dialects.postgresql.json import JSONB\n", "\n", "\n", "DATABASE_URL = \"**************************************************/form_application\"\n", "engine = create_engine(DATABASE_URL, poolclass=NullPool)\n", "Session = sessionmaker(bind=engine)\n", "\n", "Base = declarative_base()\n", "\n", "\n", "\n", "# class personal(Base):\n", "#     __tablename__ = 'personal'\n", "#     auto_id = Column(Integer, primary_key=True)\n", "#     jia_yi_id = Column(Integer)\n", "#     jia_yi = Column(String)\n", "#     name = Column(String)\n", "#     datetime = Column(DateTime)\n", "\n", "# class personal_info(Base):\n", "#     __tablename__ = 'personal_info'\n", "#     auto_id = Column(Integer, primary_key=True)\n", "#     jia_yi = Column(Integer)\n", "#     jia_yi_id = Column(Integer)\n", "#     type = Column(Integer)\n", "#     key = Col<PERSON>n(Integer)\n", "#     value = Column(String)\n", "#     iid = Column(Integer)\n", "#     datetime = Column(DateTime)\n", "\n", "# class tree_key(Base):\n", "#     __tablename__ = 'tree_key'\n", "#     auto_id = Column(Integer, primary_key=True)\n", "#     mm_name = Column(String)\n", "#     parent_id = Column(Integer)\n", "#     head_id = Column(Integer)\n", "#     fen_ji = Column(Integer)\n", "#     type = Column(Integer)\n", "#     input_or_text = Column(String)\n", "\n", "class appPerson(Base):\n", "    __tablename__ = 'appPerson'\n", "    auto_id = Column(Integer, primary_key=True)\n", "    name = Column(String)\n", "    datetime = Column(DateTime)\n", "    fen_dian_id: <PERSON>umn(Integer)\n", "\n", "\n", "session = Session()\n", "\n", "\n", "df = pd.read_sql(sql = session.query(appPerson).statement, con = session.bind)\n", "\n", "display(df)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3.10.4 64-bit", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.6"}, "orig_nbformat": 4, "vscode": {"interpreter": {"hash": "916dbcbb3f70747c44a77c7bcd40155683ae19c65e1c03b4aa3499c5328201f1"}}}, "nbformat": 4, "nbformat_minor": 2}