#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# =============================================================================
# Created By  : <PERSON><PERSON><PERSON>
# Created Date: Tue Sep  8 20:57:59 +07 2020
# =============================================================================

# from .db import TSUTAYA_MEMBER
import uuid
import random
import pytz
from datetime import datetime, timezone


async def get_timezone():
    tz = pytz.timezone('Asia/Rangoon')
    ct = datetime.now(tz=tz)
    vv = ct.isoformat()
    return vv

async def get_bookszxzx():
    cc = uuid.uuid4().hex[:10]
    dd = str(uuid.uuid4().int)[:5]
    ff = int(dd)
    # start = datetime.today()
    tz = pytz.timezone('Asia/Rangoon')
    ct = datetime.now(tz=tz)
    vv = ct.isoformat()
    return cc, ff, vv


def generate_id() -> str:
    return str(uuid.uuid4().hex)[0:8]


def generate_datetime() -> str:
    import datetime

    x = datetime.datetime.now()

    return x


def generate_datetime_selie() -> str:
    import datetime

    x_ = datetime.datetime.now()
    x = x_.strftime("%Y%m%d%H%M")
    y = random.randint(0, 99)

    x = str(x) + str(y)

    return int(x)


def generate_datetime_selie_v1() -> str:
    import datetime

    x_ = datetime.datetime.now()
    x = x_.strftime("%Y%m%d%H%M%S%f")[:-3]
    y = random.randint(0, 99)

    x = str(x)

    return int(x)


def generate_datetime_id() -> str:
    import datetime
    datetime_ = datetime.datetime.now()
    x = datetime_.strftime("%Y%m%d%H%M")
    y = str(uuid.uuid4().hex)[0:8]
    z = x+y

    return z
