version: '2.4'
services:
  #  web:
  #    container_name: web
  #    restart : always
  #    build: ./docker/nginx
  #    ports:
  #      - 8888:80
  #    volumes:
  #      - ./app:/var/www/html
  #    working_dir: /etc/nginx
  #    links:
  #      - python
  #    environment:
  #      TZ: "Asia/Bangkok" # offset = -08:00 / DST -07:00


  shwethe_petrol_api:
    container_name: shwethe_petrol_api
    restart: always
    build: ./app
    environment:
      TZ: "Asia/Bangkok" # offset = -08:00 / DST -07:00
    volumes:
      - ./app:/var/www/html
    working_dir: /var/www/html
    #    command: uvicorn main:app --reload --host 0.0.0.0 --port 8000 --workers 5
    ports:
      - 8222:8000
    # sudo docker-compose up --build
volumes:
  data-volume: null

#sdcsdcsdc